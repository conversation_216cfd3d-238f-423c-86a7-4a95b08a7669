#!/usr/bin/env python
"""
Test script to verify ETL pipeline setup
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.settings')
django.setup()

def test_etl_imports():
    """Test if ETL modules can be imported"""
    try:
        print("Testing ETL imports...")
        
        # Test basic imports
        from etl.models import ETLJob
        print("✅ ETL models imported successfully")
        
        from etl.services import ETLService, BigQueryService, CSVExportService
        print("✅ ETL services imported successfully")
        
        from etl.bigquery_schemas import ORDERS_SCHEMA, ORDER_ITEMS_SCHEMA
        print("✅ BigQuery schemas imported successfully")
        
        # Test Google Cloud imports
        from google.cloud import bigquery, storage
        print("✅ Google Cloud libraries imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_django_setup():
    """Test Django setup"""
    try:
        print("\nTesting Django setup...")
        
        from django.conf import settings
        print(f"✅ Django settings loaded: {settings.DEBUG}")
        
        from companies.models import Company
        print("✅ Company model imported successfully")
        
        from picking.models import PickOrder
        print("✅ PickOrder model imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Django setup error: {e}")
        return False

def test_bigquery_client():
    """Test BigQuery client initialization"""
    try:
        print("\nTesting BigQuery client...")
        
        from google.cloud import bigquery
        
        # Try to create client (will fail if no credentials, but that's expected)
        try:
            client = bigquery.Client()
            print("✅ BigQuery client created successfully")
            return True
        except Exception as e:
            if "credentials" in str(e).lower() or "authentication" in str(e).lower():
                print("⚠️  BigQuery client needs authentication (expected in development)")
                return True
            else:
                raise e
                
    except Exception as e:
        print(f"❌ BigQuery client error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing ETL Pipeline Setup")
    print("=" * 50)
    
    tests = [
        test_django_setup,
        test_etl_imports,
        test_bigquery_client,
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 50)
    if all(results):
        print("🎉 All tests passed! ETL pipeline is ready for setup.")
        print("\nNext steps:")
        print("1. Run: python manage.py makemigrations etl")
        print("2. Run: python manage.py migrate")
        print("3. Set up Google Cloud credentials")
        print("4. Configure Cloud Function and Cloud Scheduler")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
