# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-10 10:34+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: core/authentication.py:16
msgid "Token has no device_id"
msgstr ""

#: core/authentication.py:30 core/authentication.py:42
msgid "Unrecornized device id, Your divice is logged out"
msgstr ""

#: core/templates/admin/base_site.html:3
msgid "Django site admin"
msgstr ""

#: core/templates/admin/index.html:137
#, python-format
msgid "Models in the %(name)s application"
msgstr ""

#: core/templates/admin/index.html:148
msgid "Add"
msgstr ""

#: core/templates/admin/index.html:155
msgid "View"
msgstr ""

#: core/templates/admin/index.html:157
msgid "Change"
msgstr ""

#: core/templates/admin/index.html:168
msgid "You don't have permission to view or edit anything."
msgstr ""

#: core/templates/admin/index.html:177
msgid "Recent actions"
msgstr ""

#: core/templates/admin/index.html:178
msgid "My actions"
msgstr ""

#: core/templates/admin/index.html:182
msgid "None available"
msgstr ""

#: core/templates/admin/index.html:196
msgid "Unknown content"
msgstr ""

#: picking/views/order_sync.py:219
msgid "This order number already exists."
msgstr "หมายเลขออเดอร์ไม่สามารถซ้ำได้"

#: users/admin.py:42
msgid "Personal info"
msgstr ""

#: users/admin.py:55
msgid "Permissions"
msgstr ""

#: users/admin.py:66
msgid "Important dates"
msgstr ""

#: users/serializers.py:25
msgid "This field is required."
msgstr ""

#: users/serializers.py:26
#, python-brace-format
msgid "Invalid permission code `{pk_value}` - object does not exist."
msgstr ""

#: users/serializers.py:27
#, python-brace-format
msgid "Incorrect type. Expected `string` value, received {data_type}."
msgstr ""

#: users/serializers.py:103
msgid "A user with that username already exists."
msgstr ""
