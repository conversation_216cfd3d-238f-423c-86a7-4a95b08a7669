import re
import nanoid
import jwt
import time
from typing import List, TYPE_CHECKING, Union

from dataclasses import asdict
from datetime import datetime, date, timedelta
from django.db import models, transaction
from django.contrib.postgres.fields import J<PERSON>NField
from cloudtasks.tasks import (
    create_lazada_ready_to_ship_task,
    create_nocnoc_ready_to_ship_task,
    create_vrich_checkout_task,
    create_zort_ready_to_ship_task,
    create_zort_update_order_status_task,
)
from django.conf import settings
from django.utils import timezone
from importdata.models import OrderImportOptions
from services.zort.zort import ZortService
from services.zort.zort_types import ZortProduct
from simple_history.models import HistoricalRecords

from utils.ignore_exception import get_or
from utils.valid_str import str_max_length
from thaiaddress.validators import ThaiAddressValidator

if TYPE_CHECKING:
    from companies.models import Company


def airway_bill_directory(instance, filename):
    today = timezone.localdate().strftime("%Y-%m-%d")
    return f"airway-bills/{instance.company_id}/{today}/{filename}"


def lowercase(text: str):
    if text:
        return text.lower()
    return text


class PickOrder(models.Model):
    """
    ใบสั่งจัดสินค้า
    """

    uuid = models.CharField(
        unique=True, db_index=True, default=nanoid.generate, max_length=21
    )
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    order_number = models.TextField(db_index=True)
    order_saleschannel = models.CharField(max_length=400, db_index=True)
    order_customer = models.CharField(max_length=400, db_index=True)
    order_customerphone = models.CharField(max_length=400, db_index=True)
    order_trackingno = models.TextField(
        blank=True, null=True, default="", db_index=True
    )
    order_warehousecode = models.CharField(
        max_length=50, blank=True, null=True, default="", db_index=True
    )
    order_shippingchannel = models.CharField(
        max_length=400, blank=True, null=True, default="", db_index=True
    )
    order_total_quantity = models.IntegerField(default=0)
    order_total_price = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    order_oms = models.CharField(max_length=100, null=True)
    order_marketplace = models.CharField(max_length=100, null=True)
    order_marketplaceshop = models.CharField(max_length=100, null=True)

    order_date = models.DateField(db_index=True)
    order_json = models.JSONField()
    packing_json = models.JSONField(default=dict)

    receipt_url = models.URLField(blank=True, null=True, max_length=400)
    short_receipt_url = models.URLField(blank=True, null=True, max_length=400)

    number = models.IntegerField()
    print_count = models.IntegerField(default=0)
    print_timestamp = models.DateTimeField(blank=True, null=True)
    airway_bill_print_count = models.IntegerField(default=0)
    airway_bill_print_timestamp = models.DateTimeField(blank=True, null=True)

    remark_status = models.TextField(blank=True, default="")
    remark = models.TextField(blank=True, default="")

    has_videos = models.BooleanField(default=False)
    has_return_videos = models.BooleanField(default=False)
    has_fixcases = models.BooleanField(default=False)
    ready_to_ship = models.BooleanField(default=False)
    ready_to_ship_by = models.ForeignKey(
        "users.User", on_delete=models.PROTECT, null=True, blank=True
    )
    ready_to_ship_timestamp = models.DateTimeField(blank=True, null=True)

    # total_items = models.IntegerField(default=0)
    total_packages = models.IntegerField(default=0)
    ready_to_ship_count = models.IntegerField(default=0)

    create_date = models.DateTimeField(auto_now_add=True, db_index=True)
    create_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="pickorder_createby_set",
        blank=True,
        null=True,
    )
    update_date = models.DateTimeField(auto_now=True, db_index=True)
    update_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="pickorder_updateby_set",
        blank=True,
        null=True,
    )

    has_images = models.BooleanField(default=False)
    images_taken_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="pickorder_images_taken_by",
        blank=True,
        null=True,
    )

    is_freeze = models.BooleanField(default=False)

    class Meta:
        unique_together = [["company", "order_number"]]

    def __str__(self) -> str:
        return f"Picking Order: {self.order_number}"

    @staticmethod
    def find_many(company: "Company", number: str):
        """
        Get PickOrder List from `order_number` or `tracking_number`
        """
        pick_orders = PickOrder.objects.filter(
            company=company, order_number__startswith=number
        )
        count = pick_orders.count()

        if count == 0:
            pick_orders = PickOrder.objects.filter(
                company=company,
                order_trackingno__startswith=number,
            )
            count = pick_orders.count()

        return (list(pick_orders), count)

    @staticmethod
    def get_by_number(company, number) -> "PickOrder":
        """
        Get PickOrder by `order_number` or `order_trackingno`,
        Return None if not found

        Parameters
        ----------
        company : Company
        number : str
            order number or tracking number

        Return
        ------
        PickOrder
        """
        pick_order: PickOrder = PickOrder.objects.filter(
            company=company,
            order_number=number,
        ).first()

        if not pick_order:
            pick_order: PickOrder = PickOrder.objects.filter(
                company=company,
                order_trackingno=number,
            ).first()

        return pick_order

    @staticmethod
    def create_from_zort_order(
        company,
        order,
        create_by=None,
        commit=True,
        order_oms=None,
    ) -> "PickOrder":
        """
        Create PickOrder from ZortOrder

        Parameters
        ----------
        company: Company
        order : dict
            ZortOrder
        create_by: User

        Returns
        -------
        PickOrder
        """
        if type(order["orderdateString"]) == str:
            order_date = datetime.strptime(order["orderdateString"][:10], "%Y-%m-%d")
        else:
            order_date = order["orderdateString"]

        pick_order: PickOrder = PickOrder(
            uuid=nanoid.generate(),
            company=company,
            number=0,
            order_number=str_max_length(order["number"], 100),
            order_saleschannel=PickOrder.get_order_saleschannel(
                order["saleschannel"], create_by
            ),
            order_customer=order["shippingname"] or order["customername"],
            order_customerphone=order["shippingphone"] or order["customerphone"],
            order_trackingno=str_max_length(order["trackingno"] or "", 100),
            order_warehousecode=order["warehousecode"] or "",
            order_shippingchannel=order["shippingchannel"],
            order_total_price=order["amount"] or 0,
            order_total_quantity=sum(
                item["number"] or 0 for item in order.get("list") or []
            )
            or 0,
            order_date=order_date,
            order_json=order,
            create_by=create_by,
            remark=order.get("remark", "") or "",
            order_oms=lowercase(order_oms or order.get("orderManagementSystem")),
            order_marketplace=lowercase(order.get("integrationName", "")),
            order_marketplaceshop=str_max_length(order.get("integrationShop"), 100),
        )
        pick_order.receipt_url = PickOrder.get_signed_receipt_url(pick_order)

        if "vrich" in order["saleschannel"].lower():
            pick_order.order_oms = "vrich"

        if create_by:
            if create_by.username.startswith("openapi-jambolive"):
                pick_order.order_oms = "jambolive"
            elif create_by.username.startswith("openapi-cfshop"):
                pick_order.order_oms = "cfshop"

        if commit:
            pick_order.save()

        return pick_order

    def update_from_zort_order(
        self,
        order,
        update_by=None,
        commit=True,
        order_oms=None,
        import_options: OrderImportOptions = None,
    ):
        if not import_options:
            import_options = OrderImportOptions()

        if order.get("remark"):
            self.remark = order["remark"]

        if order.get("remark_status"):
            self.remark_status = order["remark_status"]

        if self.is_freeze:
            self.order_json["status"] = order["status"]
            self.update_by = update_by
        else:
            self.order_saleschannel = PickOrder.get_order_saleschannel(
                order["saleschannel"], self.create_by
            )
            self.order_customer = order["shippingname"]
            self.order_customerphone = order["shippingphone"] or order["customerphone"]

            update_tracking = not import_options.do_not_update_tracking
            if update_tracking:
                self.order_trackingno = str_max_length(order["trackingno"] or "", 100)

            self.order_warehousecode = order["warehousecode"] or ""
            self.order_shippingchannel = order["shippingchannel"] or ""
            self.order_total_price = order["amount"] or 0
            self.order_total_quantity = (
                sum(item["number"] for item in (order.get("list") or [])) or 0
            )
            self.order_json = order
            self.update_by = update_by
            self.order_marketplace = lowercase(order.get("integrationName", ""))
            self.order_marketplaceshop = str_max_length(
                order.get("integrationShop", ""), 100
            )
            self.order_oms = lowercase(order_oms or order.get("orderManagementSystem"))

        if order.get("freeze") is not None:
            self.is_freeze = order["freeze"]

        if commit == True:
            self.save(
                update_fields=[
                    "remark",
                    "remark_status",
                    "order_json",
                    "update_by",
                    "order_saleschannel",
                    "order_customer",
                    "order_customerphone",
                    "order_trackingno",
                    "order_warehousecode",
                    "order_shippingchannel",
                    "order_total_price",
                    "order_total_quantity",
                    "order_marketplace",
                    "order_marketplaceshop",
                    "order_oms",
                    "is_freeze",
                ]
            )

    @staticmethod
    def get_order_saleschannel(saleschannel: str, create_by=None):
        """
        For company that links to multiple vrich shop, return 'VRICH (shop_name)'.
        User.first_name and/or User.last_name must be `shop_name`

        Parameters
        ----------
        saleschannel : str
        create_by : User, optional

        Returns
        -------
        str
            saleschannel name
        """
        if saleschannel.lower() != "vrich":
            return saleschannel

        if not create_by or not create_by.first_name:
            return saleschannel

        return f"{saleschannel} ({create_by.first_name})"

    @staticmethod
    def get_signed_receipt_url(pick_order):
        key = settings.RECEIPT_SIGINING_KEY
        payload = {
            "iat": int(time.time()),
            "sub": pick_order.order_number,
            "ref": pick_order.company_id,
        }
        token = jwt.encode(payload, key, algorithm="HS256")
        return f"{settings.UI_HOST}/receipt/?t={token}"

    @property
    def signed_receipt_url(self):
        if not self.receipt_url:
            self.receipt_url = PickOrder.get_signed_receipt_url(self)
            self.save(update_fields=["receipt_url"])
        return self.receipt_url

    def make_ready_to_ship(self, ready_to_ship_by=None):
        """Update Ready To Ship Status"""
        self.ready_to_ship = True
        self.ready_to_ship_timestamp = timezone.now()
        self.ready_to_ship_by = ready_to_ship_by
        self.save()

        sales_channel = self.order_saleschannel.lower()

        if self.order_oms in ["dobybot-connect-zort", "zort"]:
            if (
                self.company.get_setting("ZORT_API_V2_READY_TO_SHIP_HOOK")
                == "ready_to_ship"
            ):
                create_zort_ready_to_ship_task(self)
            if (
                self.company.get_setting("ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK")
                == "ready_to_ship"
            ):
                create_zort_update_order_status_task(self)

        if self.order_oms == "dobybot-connect" and self.order_marketplace == "lazada":
            lazada_setting = self.company.get_setting("LAZADA_API")
            lazada_shop_setting = lazada_setting.get(self.order_marketplaceshop, {})
            if lazada_shop_setting.get("ready_to_ship"):
                create_lazada_ready_to_ship_task(self)

        if self.order_oms == "dobybot-connect" and self.order_marketplace == "nocnoc":
            nocnoc_setting = self.company.get_setting("NOCNOC_API")
            nocnoc_shop_setting = nocnoc_setting.get(self.order_marketplaceshop, {})
            if nocnoc_shop_setting.get("ready_to_ship"):
                create_nocnoc_ready_to_ship_task(self)

        if self.order_oms == "vrich" or "vrich" in sales_channel:
            create_vrich_checkout_task(
                company_id=self.company_id, order_number=self.order_number
            )

    def validate_shipping_address(self, commit=False):
        data = {
            "province": get_or(self.order_json, "shippingprovince", ""),
            "district": get_or(self.order_json, "shippingdistrict", ""),
            "subdistrict": get_or(self.order_json, "shippingsubdistrict", ""),
            "postcode": get_or(self.order_json, "shippingpostcode", ""),
        }

        if (
            ("*" in data["province"])
            or ("*" in data["district"])
            or ("*" in data["subdistrict"])
            or ("*" in data["postcode"])
        ):
            return False, data

        validator = ThaiAddressValidator(data=data)
        if validator.is_valid():
            return True, None

        if "non_field_errors" in validator.errors:
            self.order_json["shippingprovince"] = (
                "*" + self.order_json["shippingprovince"]
            )
            self.order_json["shippingdistrict"] = (
                "*" + self.order_json["shippingdistrict"]
            )
            self.order_json["shippingsubdistrict"] = (
                "*" + self.order_json["shippingsubdistrict"]
            )
            self.order_json["shippingpostcode"] = (
                "*" + self.order_json["shippingpostcode"]
            )
        if "province" in validator.errors:
            self.order_json["shippingprovince"] = (
                "*" + self.order_json["shippingprovince"]
            )
        if "district" in validator.errors:
            self.order_json["shippingdistrict"] = (
                "*" + self.order_json["shippingdistrict"]
            )
        if "subdistrict" in validator.errors:
            self.order_json["shippingsubdistrict"] = (
                "*" + self.order_json["shippingsubdistrict"]
            )
        if "postcode" in validator.errors:
            self.order_json["shippingpostcode"] = (
                "*" + self.order_json["shippingpostcode"]
            )

        if commit:
            self.save(update_fields=["order_json"])

        return False, validator.errors

    @staticmethod
    def is_splitted_order(order_number):
        return re.match(r"^[A-Z0-9]+---\d+-\d+$", order_number)

    @staticmethod
    def extract_order_number(order_number):
        if PickOrder.is_splitted_order(order_number):
            order_number, box_info = order_number.split("---")
            box_number, box_count = box_info.split("-")
            return order_number, box_number, box_count
        else:
            return order_number, None, None

    def get_main_order(self):
        if PickOrder.is_splitted_order(self.order_number):
            if self.order_json["integrationName"] != "NocNoc":
                order_number, _, _ = PickOrder.extract_order_number(self.order_number)
                return PickOrder.objects.filter(
                    order_number=order_number, company_id=self.company_id
                ).first()
        return self


class AirwayBill(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    order_number = models.CharField(max_length=50, db_index=True)
    file = models.FileField(upload_to=airway_bill_directory)

    class Meta:
        unique_together = [["order_number", "company"]]


class Product(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    name = models.CharField(max_length=400, blank=True)
    sku = models.CharField(max_length=200, db_index=True)
    barcode = models.CharField(max_length=200, blank=True, default=str)
    product_json = models.JSONField()
    product_oms = models.CharField(max_length=200)

    images = models.JSONField(default=list)
    uploaded_images = models.JSONField(default=list)
    main_image = models.CharField(max_length=500, blank=True, default="")

    color = models.CharField(max_length=200, blank=True, default="")
    price = models.DecimalField(max_digits=12, decimal_places=2, null=True)
    marketplace = models.CharField(max_length=200, null=True)
    dobysync_products = models.JSONField(default=list)
    main_shop_id = models.CharField(max_length=200, null=True)
    vat_percent = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, default=7
    )
    show_qrcode_on_record_page = models.BooleanField(default=True)
    width = models.IntegerField(null=True, default=None)  # mm
    height = models.IntegerField(null=True, default=None)  # mm
    length = models.IntegerField(null=True, default=None)  # mm
    weight = models.DecimalField(null=True, max_digits=12, decimal_places=2)  # grams
    packing_score = models.IntegerField(null=True, default=None)

    def __str__(self) -> str:
        return f"{self.sku} - {self.name}"

    @staticmethod
    def create_from_zort_product(
        zort_product: ZortProduct, company: "Company", commit=False
    ):
        product = Product(
            company=company,
            name=zort_product.name,
            sku=zort_product.sku,
            barcode=zort_product.barcode,
            product_json=asdict(zort_product),
            product_oms="zort",
        )

        if commit:
            product.save()

        return product


class ProductSerialNo(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)

    # รหัส serial number สำหรับระบุสินค้าแต่ละชิ้น
    serial_no = models.CharField(max_length=200, db_index=True)

    # รหัสสินค้า
    sku = models.CharField(max_length=200, db_index=True)

    # สถานะว่า serial no. นี้ถูกยังมีอยู่ในคลังหรือไม่
    in_stock = models.BooleanField(default=True)

    # วันที่สร้าง serial no
    create_date = models.DateTimeField(auto_now_add=True)

    # วันที่ตัดสต็อก
    cutoff_date = models.DateTimeField(null=True, default=None)

    # สินค้าที่ผูกกับ serial no นี้
    product = models.ForeignKey(Product, on_delete=models.CASCADE)

    class Meta:
        unique_together = ("serial_no", "company")


class ProductSet(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    name = models.CharField(max_length=400, blank=True)
    sku = models.CharField(max_length=200, db_index=True)

    # Example products:
    # [
    #     {"sku": "AAAA", "name": "", "amount": 1},
    #     {"sku": "BBBB", "name": "", "amount": 2},
    #     {"sku": "CCCC", "name": "", "amount": 3}
    # ]
    products = models.JSONField(default=list)

    # change_logs = models.JSONField(default=list)
    is_deleted = models.BooleanField(default=False)
    create_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="productset_createby",
        blank=True,
        null=True,
    )
    create_date = models.DateTimeField(auto_now_add=True)
    update_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="productset_updateby",
        blank=True,
        null=True,
    )
    update_date = models.DateTimeField(auto_now=True)
    history = HistoricalRecords()

    def as_dict(self):
        return {
            "name": self.name,
            "sku": self.sku,
            "products": self.products,
        }


class PickOrderTrackingNo(models.Model):
    uuid = models.CharField(
        unique=True,
        db_index=True,
        default=nanoid.generate,
        max_length=21,
    )
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    pick_order = models.ForeignKey("picking.PickOrder", on_delete=models.CASCADE)
    tracking_no = models.CharField(max_length=255)

    shipping_status = models.TextField()
    shipping_message = models.TextField()

    # RECEIVED = 'RECEIVED'
    # RECEIVE_WAREHOUSE_SCAN = 'RECEIVE_WAREHOUSE_SCAN'
    # SHIPMENT_WAREHOUSE_SCAN = 'SHIPMENT_WAREHOUSE_SCAN'
    # ARRIVAL_WAREHOUSE_SCAN = 'ARRIVAL_WAREHOUSE_SCAN'
    # DELIVERY_TICKET_CREATION_SCAN = 'DELIVERY_TICKET_CREATION_SCAN'
    # DELIVERY_CONFIRM = 'DELIVERY_CONFIRM'

    PENDING = "PENDING"
    IN_DELIVERY = "IN_DELIVERY"
    SUCCESS = "SUCCESS"
    CANCELLED = "CANCELLED"
    ERROR = "ERROR"
    UNKNOWN = "UNKNOWN"
    STATUS = (
        # (RECEIVED, 'RECEIVED'),
        # (RECEIVE_WAREHOUSE_SCAN, 'RECEIVE_WAREHOUSE_SCAN'),
        # (SHIPMENT_WAREHOUSE_SCAN, 'SHIPMENT_WAREHOUSE_SCAN'),
        # (ARRIVAL_WAREHOUSE_SCAN, 'ARRIVAL_WAREHOUSE_SCAN'),
        # (DELIVERY_TICKET_CREATION_SCAN, 'DELIVERY_TICKET_CREATION_SCAN'),
        # (DELIVERY_CONFIRM, 'DELIVERY_CONFIRM'),
        (PENDING, "PENDING"),
        (IN_DELIVERY, "IN DELIVERY"),
        (SUCCESS, "SUCCESS"),
        (CANCELLED, "CANCELLED"),
        (ERROR, "ERROR"),
        (UNKNOWN, "UNKNOWN"),
    )

    status = models.CharField(max_length=20, choices=STATUS)
    is_active = models.BooleanField(default=True)

    create_date = models.DateTimeField(auto_now_add=True)
    update_date = models.DateTimeField(auto_now=True, null=True)

    DBB_FLASH = "DBB_FLASH"
    DBB_KERRY = "DBB_KERRY"
    DBB_THAIPOST = "DBB_THAIPOST"
    DBB_THAIPOST_EMS = "DBB_THAIPOST_EMS"
    DBB_THAIPOST_REG = "DBB_THAIPOST_REG"

    SHIPPING_PROVIDERS = (
        (DBB_FLASH, "DBB_FLASH"),
        (DBB_KERRY, "DBB_KERRY"),
        (DBB_THAIPOST, "DBB_THAIPOST"),
        (DBB_THAIPOST_EMS, "DBB_THAIPOST_EMS"),
        (DBB_THAIPOST_REG, "DBB_THAIPOST_REG"),
    )

    shipping_provider = models.CharField(
        max_length=20, choices=SHIPPING_PROVIDERS, blank=True, null=True, default=None
    )

    webhook_logs = models.JSONField(default=list)

    # Source/Sender Addressd
    src_name = models.TextField(blank=True)
    src_phone = models.CharField(max_length=20, blank=True)
    src_address = models.TextField()
    src_province = models.CharField(max_length=100)
    src_district = models.CharField(max_length=100)
    src_subdistrict = models.CharField(max_length=100)
    src_postcode = models.CharField(max_length=10)

    # Destination/Receiver Addressd
    dst_name = models.TextField(blank=True)
    dst_phone = models.CharField(max_length=200, blank=True)
    dst_address = models.TextField()
    dst_province = models.CharField(max_length=100)
    dst_district = models.CharField(max_length=100)
    dst_subdistrict = models.CharField(max_length=100)
    dst_postcode = models.CharField(max_length=10)

    tracking_info = models.JSONField()
    cod_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True)

    is_cod_refunded = models.BooleanField(default=False)
    cod_refunded_pno = models.CharField(max_length=255, blank=True, default="")


class OrderLastNumber(models.Model):
    company = models.OneToOneField(
        "companies.Company", primary_key=True, on_delete=models.CASCADE
    )
    last_number = models.IntegerField()
    date = models.DateField(auto_now_add=True)


class EtaxTaxInvoiceLastNumber(models.Model):
    company = models.OneToOneField(
        "companies.Company", primary_key=True, on_delete=models.CASCADE
    )
    last_number = models.IntegerField()
    date = models.DateField(auto_now_add=True)


class EtaxCreditNoteLastNumber(models.Model):
    company = models.OneToOneField(
        "companies.Company", primary_key=True, on_delete=models.CASCADE
    )
    last_number = models.IntegerField()
    date = models.DateField(auto_now_add=True)


def get_next_number(
    running_number_model: Union[
        "OrderLastNumber", "EtaxTaxInvoiceLastNumber", "EtaxCreditNoteLastNumber"
    ],
    company: "Company",
):
    """
    Example usage
    """
    row, _ = running_number_model.objects.get_or_create(
        company=company, defaults={"last_number": 0}
    )

    row = running_number_model.objects.select_for_update().filter(pk=row.pk).first()

    if row.date != date.today():
        row.date = date.today()
        row.last_number = 0

    row.last_number += 1
    row.save()

    return f"ESO-{row.date.strftime('%y%m%d')}{row.last_number:04}"
