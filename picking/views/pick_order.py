import re
import sentry_sdk
from datetime import timedel<PERSON>, datetime

import requests
from django.db.models.query_utils import Q
from django.db.models import F
from django.http.response import Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone
from dynamic_rest.viewsets import DynamicModelViewSet
from google.cloud import logging
from rest_framework import serializers
from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import APIException
from rest_framework.response import Response
from rest_framework.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_405_METHOD_NOT_ALLOWED,
)
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from cloudtasks.tasks import (
    create_single_print_pick_order_task,
    create_webhook_task,
    create_zort_ready_to_ship_task,
    create_zort_update_order_status_task,
    create_ready_to_ship_messaging_task,
)
from companies.models import Company
from core.serializers.serializers import <PERSON>Date<PERSON>ield, StartDate<PERSON>ield, end_of, start_of
from core.throttling import OutputTaskUserThrottle
from etax.models import TaxDocument
from fixcases.serializers import FixCaseSerializer
from logger.models import VideoRecordLog
from logger.serializers import VideoRecordLogSerializer
from picking.models import PickOrder, Product, ProductSerialNo, ProductSet
from picking.permissions import CanViewOrderCenterPage, CanViewRecordPage
from picking.serializers import PickOrderSerializer
from picking.serializers.webhook import ReadyToShipWebhookSerializer
from picking.services import WebhookService
from picking.services.webhook import PreRecordWebhookException
from services.zort.zort import ZortException
from sms.services.messaging import MessagingService
from stats.models import OrderPackingStatLog
from users.models import User
from utils.excel_utils import file_xlsx_reponse
from utils.random import random_chars
from utils.valid_str import trim
from wallets.models import RecordPackage, Wallet
from django.utils.decorators import method_decorator
from django.db import transaction

from wallets.service import WalletService

logging_client = logging.Client()


class PickOrderViewSet(DynamicModelViewSet):
    authentication_classes = [JWTAuthentication, TokenAuthentication]
    permission_classes = [CanViewOrderCenterPage]
    throttle_classes = [OutputTaskUserThrottle]
    serializer_class = PickOrderSerializer

    def get_queryset(self, queryset=None):
        company = self.request.user.company
        return PickOrder.objects.filter(company=company)

    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def filter_queryset(self, queryset):
        params = self.request.GET

        start_date = params.get("filter{order_date.gte}")
        if start_date:
            start_date = start_of(datetime.strptime(start_date, "%Y-%m-%d").date())

        end_date = params.get("filter{order_date.lte}")
        if end_date:
            end_date = end_of(datetime.strptime(end_date, "%Y-%m-%d").date())

        if params.get("search"):
            search = params["search"]

            if search[0] == "*":
                search = search[1:]
                queryset = queryset.filter(
                    Q(order_number__icontains=search)
                    | Q(order_trackingno__icontains=search)
                    | Q(order_customer__icontains=search)
                )
            else:
                queryset = queryset.filter(
                    Q(order_number__istartswith=params["search"])
                    | Q(order_trackingno__istartswith=params["search"])
                    | Q(order_customer__icontains=params["search"])
                )

        if params.get("order_status"):
            queryset = queryset.filter(order_json__status=params["order_status"])

        if params.get("record_start") or params.get("record_end"):
            record_logs = VideoRecordLog.objects.filter(
                company=self.request.user.company
            )
            if params.get("record_start"):
                record_logs = record_logs.filter(
                    record_date__gte=start_of(params.get("record_start"))
                )
            if params.get("record_end"):
                record_logs = record_logs.filter(
                    record_date__lte=end_of(params.get("record_end"))
                )

            order_related_with_record = record_logs.values_list("pick_order", flat=True)
            queryset = queryset.filter(id__in=order_related_with_record)

        if params.get("etax_status"):

            if params.get("etax_status") == "new":
                existing_order_numbers = TaxDocument.objects.filter(
                    company_id=self.request.user.company.id
                ).values_list("order_number", flat=True)
                queryset = queryset.exclude(order_number__in=existing_order_numbers)
            else:
                taxdoc_queryset = (
                    TaxDocument.objects.filter(company_id=self.request.user.company.id)
                    .order_by("order_number", "-id")
                    .distinct("order_number")
                )

                if start_date:
                    taxdoc_queryset = taxdoc_queryset.filter(
                        create_date__gte=start_date
                    )
                if end_date:
                    taxdoc_queryset = taxdoc_queryset.filter(
                        create_date__lte=end_date + timedelta(days=14)
                    )

                order_related_to_tax_doc = taxdoc_queryset.filter(
                    status=params.get("etax_status")
                ).values_list("pick_order", flat=True)
                queryset = queryset.filter(id__in=order_related_to_tax_doc)

        filter_is_request_tax_inv = params.get("is_request_tax_inv")
        if filter_is_request_tax_inv:
            queryset = queryset.exclude(
                Q(order_json__customeridnumber=None)
                | Q(order_json__customeridnumber__exact="")
            )

        queryset = queryset.defer("order_json")

        return super().filter_queryset(queryset)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class PickOrderUpdateWithZortAPI(APIView):
    permission_classes = [CanViewOrderCenterPage]

    class Validator(serializers.Serializer):
        start_date = serializers.DateField()
        end_date = serializers.DateField()

    def validate(self, request):
        company: Company = self.request.user.company

        try:
            company.get_zort_service()
        except ZortException as error:
            raise APIException(
                {
                    "message": "กรุณาตั้งค่า Market Place API ก่อนใช้งาน",
                    "code": str(error),
                }
            )

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        return validator.validated_data

    def post(self, request):
        company: Company = self.request.user.company
        data = self.validate(request)
        start_date = data["start_date"]
        end_date = data["end_date"] + timedelta(days=1)

        pick_order_map = {
            x.order_number: x
            for x in PickOrder.objects.filter(
                company=company,
                order_date__gte=start_date,
                order_date__lte=end_date,
            )
        }

        zort_orders = (
            company.get_zort_service()
            .get_orders(
                order_date_after=start_date.strftime("%Y-%m-%d"),
                order_date_before=end_date.strftime("%Y-%m-%d"),
            )
            .get("list", [])
        )

        updatelist = []
        for zort_order in zort_orders:
            pick_order = pick_order_map.get(zort_order["number"])
            if pick_order:
                pick_order.update_from_zort_order(zort_order, commit=False)
                updatelist.append(pick_order)

        PickOrder.objects.bulk_update(
            updatelist,
            fields=[
                "order_json",
                "order_saleschannel",
                "order_customer",
                "order_customerphone",
                "order_trackingno",
                "order_warehousecode",
                "order_shippingchannel",
                "order_marketplace",
                "order_marketplaceshop",
            ],
        )

        return Response("OK")


class PickOrderReadyToShipAPI(APIView):
    permission_classes = [CanViewOrderCenterPage]

    def post(self, request, order_number, *args, **kwargs):
        company: Company = request.user.company

        # TODO: handle multiple order number
        pick_order = PickOrder.get_by_number(company, order_number)
        if not pick_order:
            pick_orders, count = PickOrder.find_many(company, order_number)
            if count == 0:
                raise Http404
            if count > 1:
                serializer = PickOrderSerializer(pick_orders, many=True)
                return Response(
                    {
                        "pick_orders": serializer.data,
                        "code": "MULTIPLE_ORDERS",
                        "detail": "พบคำสั่งซื้อหลายรายการ กรุณาเลือกว่าต้องการปรับสถานะพร้อมส่งคำสั่งซื้อใด",
                    },
                    status=HTTP_400_BAD_REQUEST,
                )

        serializer = PickOrderSerializer(
            pick_order,
            request_fields={
                "videorecordlog_set": {"*": True, "upload_by": {"*": True}}
            },
        )

        # Check if pick_order can ready-to-ship
        is_valid, error = self.validate_can_ready_to_ship(company, pick_order)
        if not is_valid:
            data = {"pick_order": serializer.data, **error}
            return Response(data, status=HTTP_400_BAD_REQUEST)

        # Make order ready to ship
        pick_order.make_ready_to_ship(ready_to_ship_by=request.user)

        # Send sms ready to ship
        self.send_ready_to_ship_message(company, pick_order)

        # Send email ready to ship
        # self.send_email_ready_to_ship(company, pick_order)

        # Perform Post Ready To Ship Action
        self.perform_post_ready_to_ship_action(company, pick_order)

        return Response(serializer.data, status=HTTP_200_OK)

    @staticmethod
    def validate_can_ready_to_ship(
        company,
        pick_order,
    ):
        if (
            company.get_setting("READY_TO_SHIP_REQUIRE_VIDEO")
            and not pick_order.has_videos
        ):
            detail = {
                "code": "READY_TO_SHIP_REQUIRE_VIDEO",
                "detail": f"ไม่สามารถปรับสถานะพร้อมส่งได้: ไม่พบวิดีโอสำหรับคำสั่งซื้อ {pick_order.order_number}",
            }
            return (False, detail)

        if (
            company.get_setting("READY_TO_SHIP_REQUIRE_NO_FIXCASES")
            and pick_order.has_fixcases
        ):
            fixcases = pick_order.fixcase_set.filter(is_complete=False).all()
            detail = {
                "code": "READY_TO_SHIP_REQUIRE_NO_FIXCASES",
                "detail": (
                    f"ไม่สามารถปรับสถานะพร้อมส่งได้: คำสั่งซื้อ {pick_order.order_number} "
                    + f"มี fixcase เปิดอยู่ {len(fixcases)} รายการ"
                    + "".join([f"\n * {f.description}" for f in fixcases])
                ),
            }
            return (False, detail)

        if pick_order.ready_to_ship:
            detail = {
                "code": "ALREADY_READY_TO_SHIP",
                "detail": f"ไม่สามารถปรับสถานะพร้อมส่งได้: คำสั่งซื้อ {pick_order.order_number} มีสถานะพร้อมส่งอยู่แล้ว",
            }
            return (False, detail)

        return (True, None)

    def perform_post_ready_to_ship_action(
        self, company: Company, pick_order: PickOrder
    ):
        if company.get_setting("READY_TO_SHIP_WEBHOOK_ENABLE"):
            try:
                self.send_ready_to_ship_webhook(company, pick_order)
            except Exception as e:
                logger = logging_client.logger("ready_to_ship_webhook")
                logger.log_text(
                    text=f"{company.name} | Failed to send webhook" + str(e),
                    severity="ERROR",
                )

    @staticmethod
    def send_ready_to_ship_webhook(company: Company, pick_order: PickOrder):
        url = company.get_setting("READY_TO_SHIP_WEBHOOK_URL")
        auth = company.get_setting("READY_TO_SHIP_WEBHOOK_AUTH")
        assert url is not None, "READY_TO_SHIP_WEBHOOK_URL is required"
        assert auth is not None, "READY_TO_SHIP_WEBHOOK_AUTH is required"

        video_record_log = pick_order.videorecordlog_set.last()
        serializer = ReadyToShipWebhookSerializer(video_record_log)
        headers = {"Authorization": auth}
        json = serializer.data
        res = requests.post(url, headers=headers, json=json)

        logger = logging_client.logger("ready_to_ship_webhook")
        severity = "ERROR" if res.status_code >= 400 else "DEFAULT"
        logger.log_text(
            f"{company.name} | [{res.status_code}]: POST {url}", severity=severity
        )

        return res

    @staticmethod
    def send_ready_to_ship_message(company: Company, pick_order: PickOrder):
        msg_service = MessagingService(company)
        msg_service.send_ready_to_ship_message(pick_order)

    @staticmethod
    def send_email_ready_to_ship(company: Company, pick_order: PickOrder):
        if not company.get_setting("POST_RECORD_ACTION_SEND_EMAIL"):
            return

        email_service = company.get_email_service()
        email_service.send_email_ready_to_ship(pick_order)


class PickOrderPreRecordActionAPI(APIView):
    permission_classes = [CanViewRecordPage]

    def post(self, request):
        order_number = request.data["order_number"]
        is_exact = request.data.get("exact", False)
        company: Company = request.user.company

        if company.package == Company.PACKAGE_RECORD_ONLY:
            return self.pre_record_action_record_only(company, order_number)

        if company.get_setting("POST_RECORD_ACTION_CREATE_ORDER"):
            # ถ้าเป็นกรณี full integrate แต่เปิด POST_RECORD_ACTION_CREATE_ORDER
            # ให้ทำการเช็คแค่ว่ามีวิดีโอซ้ำรึเปล่าเหมือน record only
            return self.pre_record_action_record_only(company, order_number)

        if company.package == Company.PACKAGE_FULL_INTEGRATION:
            return self.pre_record_action_full_integration(
                company, order_number, request.user, is_exact
            )

    @staticmethod
    def pre_record_action_record_only(company: Company, order_number: str):
        last_record_ids = VideoRecordLog.objects.filter(
            company=company,
            name=order_number,
        ).values_list("id", flat=True)

        if last_record_ids:
            last_record = VideoRecordLog.objects.get(id=max(last_record_ids))
        else:
            last_record = None

        if last_record:
            serializer = VideoRecordLogSerializer(last_record)
            record_date = timezone.localtime(last_record.record_date)
            detail = f'คำสั่งซื้อ {order_number} เคยถูกบันทึกวิดีโอไปแล้วเมื่อ {record_date.strftime("%d/%m/%y %H:%M")} โดย {last_record.upload_by}'
            return Response(
                {
                    "pick_order_id": None,
                    "order_number": order_number,
                    "status": "warning",
                    "code": "has_videos",
                    "remark": "",
                    "detail": detail,
                    "data": serializer.data,
                    "pre_record_webhook": "False",
                }
            )

        return Response(
            {
                "pick_order_id": None,
                "order_number": order_number,
                "status": "success",
                "pre_record_webhook": "False",
            }
        )

    @staticmethod
    def pre_record_action_full_integration(
        company: Company, order_number: str, user: User, is_exact: bool = False
    ):
        # pick_order = PickOrder.get_by_number(company, order_number)
        pick_order = None
        is_return = order_number.startswith("RT-")

        _order_number = order_number
        if is_return:
            _order_number = order_number[3:]

        if is_exact:
            pick_order = PickOrder.get_by_number(company, _order_number)
        else:
            pick_orders, count = PickOrder.find_many(company, _order_number)
            if count > 1:
                return Response(
                    {
                        "pick_order_id": None,
                        "order_number": None,
                        "status": "warning",
                        "code": "multiple_orders",
                        "detail": f"พบคำสั่งซื้อ {count} รายการ กรุณาเลือกว่าต้องการบันทึกคำสั่งซื้อใด",
                        "data": [
                            {
                                "id": pick_order.id,
                                "order_number": pick_order.order_number,
                                "order_trackingno": pick_order.order_trackingno,
                                "order_customer": pick_order.order_customer,
                                "has_videos": pick_order.has_videos,
                                "ready_to_ship": pick_order.ready_to_ship,
                                "remark": pick_order.remark,
                            }
                            for pick_order in pick_orders
                        ],
                        "pre_record_webhook": False,
                    }
                )
            if count == 1:
                pick_order_exact = PickOrder.get_by_number(company, _order_number)
                if pick_order_exact is not None:
                    pick_order = pick_orders[0]

        pre_record_webhook_success = False
        if not is_return and company.get_setting("PRE_RECORD_WEBHOOK_ENABLE"):
            try:
                result = WebhookService.send_pre_record_webhook(
                    company=company,
                    order_number=order_number,
                    pick_order=pick_order,
                    user=user,
                )

                if result["success"] and result["pick_order"]:
                    pick_order = result["pick_order"]

                pre_record_webhook_success = str(result["success"])
            except PreRecordWebhookException as e:
                sentry_sdk.capture_exception(e)
                pre_record_webhook_success = "ERROR"

        # Check duplicate record

        scope_day = timezone.now() - timedelta(days=4)
        if pick_order is not None:
            last_record: VideoRecordLog = VideoRecordLog.objects.filter(
                pick_order=pick_order,
                company=company,
                is_return=is_return,
                upload_date__gte=scope_day,
            ).last()
        else:
            last_record: VideoRecordLog = VideoRecordLog.objects.filter(
                name=order_number,
                company=company,
                upload_date__gte=scope_day,
            ).last()

        if last_record:
            serializer = VideoRecordLogSerializer(last_record)
            record_date = timezone.localtime(last_record.record_date)

            detail = f'คำสั่งซื้อ {order_number} เคยถูกบันทึกวิดีโอไปแล้วเมื่อ {record_date.strftime("%d/%m/%y %H:%M")} โดย {last_record.upload_by}'
            if last_record.pick_order:
                detail += (
                    f"\n\nและมีหมายเหตุ \n *{last_record.pick_order.remark}"
                    if last_record.pick_order.remark
                    else ""
                )
            return Response(
                {
                    "pick_order_id": (
                        last_record.pick_order.id if last_record.pick_order else None
                    ),
                    "order_number": (
                        last_record.pick_order.order_number
                        if last_record.pick_order
                        else None
                    ),
                    "status": "warning",
                    "code": "has_videos",
                    "detail": detail,
                    "remark": (
                        last_record.pick_order.remark if last_record.pick_order else ""
                    ),
                    "data": serializer.data,
                    "pre_record_webhook": pre_record_webhook_success,
                }
            )

        if not pick_order:
            return Response(
                {
                    "pick_order_id": None,
                    "order_number": None,
                    "status": "warning",
                    "code": "not_found",
                    "detail": f"ไม่พบคำสั่งซื้อ {order_number} ในระบบ",
                    "data": None,
                    "pre_record_webhook": pre_record_webhook_success,
                }
            )

        if pick_order.has_fixcases:
            fixcases = pick_order.fixcase_set.filter(is_complete=False)
            serializer = FixCaseSerializer(fixcases, many=True)
            detail = (
                f"คำสั่งซื้อ {order_number} มีการเปิด Fix Case {len(fixcases)} รายการ"
                + ("".join([f"\n * {f.description}" for f in fixcases]))
                + (
                    f"\n\nและมีหมายเหตุ \n* {pick_order.remark}"
                    if pick_order.remark
                    else ""
                )
            )

            return Response(
                {
                    "status": "warning",
                    "code": "has_fixcases",
                    "pick_order_id": pick_order.id,
                    "order_number": pick_order.order_number,
                    "detail": detail,
                    "remark": pick_order.remark,
                    "data": serializer.data,
                    "pre_record_webhook": pre_record_webhook_success,
                }
            )

        if pick_order.remark:
            serializer = PickOrderSerializer(pick_order)
            return Response(
                {
                    "pick_order_id": pick_order.id,
                    "order_number": pick_order.order_number,
                    "status": "warning",
                    "code": "remark",
                    "detail": f"คำสั่งซื้อ {order_number} มีหมายเหตุ \n* {pick_order.remark}",
                    "remark": pick_order.remark,
                    "data": serializer.data,
                    "pre_record_webhook": pre_record_webhook_success,
                }
            )

        if not is_return and pick_order.order_json.get("status") == "Voided":
            serializer = PickOrderSerializer(pick_order)
            return Response(
                {
                    "pick_order_id": pick_order.id,
                    "order_number": pick_order.order_number,
                    "status": "warning",
                    "code": "voided",
                    "detail": f"คำสั่งซื้อ {order_number} มีสถานะเป็น Voided",
                    "remark": pick_order.remark,
                    "data": serializer.data,
                    "pre_record_webhook": pre_record_webhook_success,
                }
            )

        if pick_order.order_oms in ["dobybot-connect-zort", "zort"]:
            if company.get_setting("ZORT_API_V2_READY_TO_SHIP_HOOK") == "pre_record":
                create_zort_ready_to_ship_task(pick_order)
            if (
                company.get_setting("ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK")
                == "pre_record"
            ):
                create_zort_update_order_status_task(pick_order)

        return Response(
            {
                "pick_order_id": pick_order.id,
                "order_number": pick_order.order_number,
                "status": "success",
                "pre_record_webhook": pre_record_webhook_success,
            }
        )


class PickOrderPostRecordActionAPI(APIView):
    permission_classes = [CanViewRecordPage]

    @method_decorator(transaction.atomic)
    def post(self, request):
        serializer = VideoRecordLogSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        company: Company = request.user.company
        wallet: Wallet = company.wallet

        if self.check_duplicate_video(company, data):
            sentry_sdk.capture_message(
                f"Duplicate video: {company.name} {data['name']}"
            )
            record_balance = wallet.get_realtime_record_balance()
            return Response({"record_balance": record_balance})

        if data["is_return"]:
            pick_order = PickOrder.get_by_number(company, data["name"][3:])
        else:
            pick_order = PickOrder.get_by_number(company, data["name"])

        if (
            company.get_setting("POST_RECORD_ACTION_CREATE_ORDER")
            and not pick_order
            and not data["name"].startswith("TF-")
        ):
            pick_order = self.create_pick_order_from_video_name(
                company=company,
                video_name=data["name"],
                pattern=company.get_setting("POST_RECORD_ACTION_CREATE_ORDER_PATTERN"),
            )

        # Save VideoRecordLog
        vlog: VideoRecordLog = serializer.save(
            company=company,
            pick_order=pick_order,
            upload_by=request.user,
        )

        # Deduct realtime credit
        # if wallet.version == 2:
        #     fefo_package = wallet.get_fefo_record_package()
        #     WalletService.deduct_balance(fefo_package, vlog)

        # Update has_return_videos flag
        if pick_order and data["is_return"]:
            pick_order.has_return_videos = True
            pick_order.save(update_fields=["has_return_videos"])

        # Update has_videos flag
        if pick_order and not data["is_return"]:
            pick_order.has_videos = True
            pick_order.save(update_fields=["has_videos"])

        if not company.get_setting("RECORD_SCAN_SERIAL_NO_MODE") == "NONE":
            barcodes = [log["barcode"] for log in data["scan_logs"]]

            in_stock = not data["is_return"]

            serial_nos = ProductSerialNo.objects.filter(
                serial_no__in=barcodes, in_stock=in_stock
            )

            # Bulk update the serial no
            if in_stock:
                serial_nos.update(in_stock=False, cutoff_date=timezone.now())
            else:
                serial_nos.update(in_stock=True)

        if company.get_setting("POST_RECORD_ACTION_WEBHOOK_ENABLE"):
            create_webhook_task(
                webhook_type="post-record-webhook",
                payload={
                    "company": company.id,
                    "video_record_log": vlog.id,
                },
            )

        if pick_order and not data["is_return"]:
            # Webhhok Jambolive
            if pick_order.order_oms == "jambolive":
                create_webhook_task(
                    "post-record-webhook2",
                    {
                        "company": company.id,
                        "video_record_log": vlog.id,
                        "webhook_name": "JAMBOLIVE_WEBHOOK_CONFIG",
                    },
                )
            if pick_order.order_oms == "cfshop":
                create_webhook_task(
                    "post-record-webhook2",
                    {
                        "company": company.id,
                        "video_record_log": vlog.id,
                        "webhook_name": "CF_SHOPS_WEBHOOK_CONFIG",
                    },
                )

            # Webhook Zort
            if pick_order.order_oms in ["dobybot-connect-zort", "zort"]:
                rts_hook = company.get_setting("ZORT_API_V2_READY_TO_SHIP_HOOK")
                if rts_hook == "post_record":
                    create_zort_ready_to_ship_task(pick_order)

                uos_hook = company.get_setting("ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK")
                if uos_hook == "post_record":
                    create_zort_update_order_status_task(pick_order)

            # Auto ready to ship
            auto_rts = company.get_setting("POST_RECORD_ACTION_READY_TO_SHIP")
            if auto_rts and not pick_order.ready_to_ship:
                pick_order.make_ready_to_ship(ready_to_ship_by=request.user)
                create_ready_to_ship_messaging_task(
                    company_id=company.id,
                    pick_order_id=pick_order.id,
                )

        if company.get_setting("ENABLE_P_SCORE"):
            if (
                not vlog.diff_logs
                and not vlog.is_return
                and vlog.pick_order is not None
                and vlog.pick_order.order_json.get("list")
            ):
                slog = OrderPackingStatLog.from_video_record_log(vlog)
                slog.save()

        record_balance = wallet.get_realtime_record_balance()
        return Response({"record_balance": record_balance})

    @classmethod
    def create_pick_order_from_video_name(
        cls, company: Company, video_name: str, pattern: str
    ):
        pattern = pattern.replace("?<", "?P<")
        pattern = re.compile(pattern)

        mo = re.match(pattern, video_name)
        order_number = None
        order_customerphone = ""
        order_trackingno = ""
        if mo:
            result = mo.groupdict()
            order_number = result.get("order_number")
            order_customerphone = result.get("order_customerphone", "") or ""
            order_trackingno = result.get("order_trackingno", "") or ""

        if order_number:
            count = PickOrder.objects.filter(
                company=company, order_number__startswith=order_number
            ).count()

            if count > 0:
                order_number = f"{order_number}-{count:03}"
        else:
            order_number = cls.generate_running_order_number(company)

        pick_order = PickOrder.objects.create(
            company=company,
            order_number=order_number,
            order_saleschannel="-",
            order_customer="-",
            order_customerphone=order_customerphone,
            order_trackingno=order_trackingno,
            order_date=timezone.now().today(),
            order_json={},
            number=0,
        )

        return pick_order

    @classmethod
    def generate_running_order_number(cls, company: Company):
        today = timezone.localdate().strftime("%y%m%d")
        count = PickOrder.objects.filter(
            company=company, order_number__startswith=today
        ).count()
        count += 1
        random_str = random_chars(4)
        return f"{today}-{random_str}-{count:03}"

    @staticmethod
    def check_duplicate_video(company: Company, data: dict):
        return VideoRecordLog.objects.filter(
            company=company,
            name=data["name"],
            record_date=data["record_date"],
            file_size=data["file_size"],
        ).exists()


class PickOrderPrintAPI(APIView):
    permission_classes = [CanViewOrderCenterPage]

    class Validator(serializers.Serializer):
        order_numbers = serializers.ListField(child=serializers.CharField())
        force_reprint = serializers.BooleanField(required=False, default=False)
        force_print_voided_order = serializers.BooleanField(
            required=False, default=False
        )

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        company = request.user.company
        order_numbers = validator.validated_data["order_numbers"]
        force_reprint = validator.validated_data["force_reprint"]
        force_print_voided_order = validator.validated_data["force_print_voided_order"]

        queryset = (
            PickOrder.objects.filter(order_number__in=order_numbers, company=company)
            .annotate(status=F("order_json__status"))
            .values("order_number", "print_count", "status")
        )

        results = []
        orders_map = {order["order_number"]: order for order in queryset}
        for order_number in order_numbers:
            if order_number not in orders_map:
                results.append(
                    {
                        "order_number": order_number,
                        "print": False,
                        "reason": "ไม่พบคำสั่งซื้อในระบบ",
                    }
                )
                continue

            order = orders_map[order_number]
            if not force_print_voided_order and order["status"] == "Voided":
                results.append(
                    {
                        "order_number": order["order_number"],
                        "print": False,
                        "status": "คำสั่งซื้อมีสถานะ Voided",
                    }
                )
                continue

            if not force_reprint and order["print_count"] > 0:
                results.append(
                    {
                        "order_number": order["order_number"],
                        "print": False,
                        "status": "คำสั่งซื้อนี้เคยพิมพ์ไปแล้ว",
                    }
                )
                continue

            create_single_print_pick_order_task(
                company_id=company.id,
                order_number=order["order_number"],
                idempotency=not force_reprint,
            )
            results.append(
                {
                    "order_number": order["order_number"],
                    "print": True,
                    "status": "กำลังพิมพ์",
                }
            )

        return file_xlsx_reponse(results)
        # results = {
        #     'accepted': [],
        #     'voided': [],
        #     'printed': [],
        # }
        # for order in queryset:
        #     if not force_print_voided_order and order['status'] == 'Voided':
        #         results['voided'].append(order['order_number'])
        #         continue

        #     if not force_reprint and order['print_count'] > 0:
        #         results['printed'].append(order['order_number'])
        #         continue

        #     create_single_print_pick_order_task(
        #         company_id=company.id,
        #         order_number=order['order_number'],
        #         idempotency=not force_reprint
        #     )
        #     results['accepted'].append(order['order_number'])

        # create excel report for results
        # report = []
        # for item in results['accepted']:
        #     report.append({'order_number': item, 'status': 'accepted'})
        # for item in results['voided']:
        #     report.append({'order_number': item, 'status': 'voided'})
        # for item in results['printed']:
        #     report.append({'order_number': item, 'status': 'printed'})

        # return Response(results)


class StatAPI(APIView):
    """
    สำหรับ record only ใช้ check สถานะก่อนเริ่มอัดวิดีโอ
    """

    def get(self, request):
        return Response(status=200)


class PickOrderItemListAPI(APIView):
    permission_classes = [CanViewRecordPage]

    class Validator(serializers.Serializer):
        order_number = serializers.CharField()

    def post(self, request):
        company: Company = request.user.company
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        order_number = validator.validated_data["order_number"]
        pick_order = get_object_or_404(
            PickOrder, company=company, order_number=order_number
        )

        product_sets = ProductSet.objects.filter(
            company=company,
            is_deleted=False,
            sku__in=[trim(item["sku"]) for item in pick_order.order_json["list"] or []],
        )
        product_sets_dict = {p.sku: p for p in product_sets}

        item_sets = []
        item_list = []

        # Seperate item_sets and item_list
        for item in pick_order.order_json["list"]:
            sku = trim(item["sku"])
            if sku in product_sets_dict:
                item_sets.append(item)
            else:
                item_list.append(item)

        # Split item_set into item_list
        for item in item_sets:
            sku = trim(item["sku"])
            ps: ProductSet = product_sets_dict[sku]
            products = ps.products

            for p in products:
                item_list.append(
                    {
                        "id": None,
                        "sku": p["sku"],
                        "name": f"{p['name'] or p['sku']} (from set {ps.name or item['name']} x {item['number']})",
                        "number": item["number"] * p["amount"],
                        "barcode": "",
                        "unittext": "",
                        "discountamount": 0,
                        "pricepernumber": 0,
                        "totalprice": 0,
                    }
                )

        products = Product.objects.filter(
            company=company, sku__in=[trim(item["sku"]) for item in item_list]
        )
        products_dict = {p.sku: p for p in products}

        # Replace barcode and name
        for item in item_list:
            sku = trim(item["sku"])

            if sku in products_dict:
                item["image"] = products_dict[sku].main_image
                item["barcode"] = products_dict[sku].barcode
                item["show_image_qr_code"] = products_dict[
                    sku
                ].show_qrcode_on_record_page
                if company.get_setting("PICK_ITEM_RENAME_BY_PRODUCT"):
                    item["name"] = products_dict[sku].name
            elif item.get("sku_barcode"):
                item["barcode"] = item["sku_barcode"]
            else:
                item["barcode"] = "-"

        pick_order.order_json["list"] = item_list
        serializer = PickOrderSerializer(pick_order)
        return Response(serializer.data)


class PickOrderPostRecordActionSendWebhookAPI(APIView):
    def post(self, request):
        serializer = VideoRecordLogSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        company: Company = request.user.company

        pick_order = PickOrder.get_by_number(company, data["name"])
        video_record_log = VideoRecordLog(**data)
        video_record_log.upload_by = request.user
        video_record_log.pick_order = pick_order

        response = WebhookService.send_post_record_webhook(company, video_record_log)

        return Response(status=response.status_code)


class PickOrderBulkPick(APIView):
    permission_classes = [CanViewOrderCenterPage]

    class Validator(serializers.Serializer):
        reference_number = serializers.CharField()

    def post(self, request):
        company: Company = request.user.company
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        reference_number = validator.validated_data["reference_number"]

        try:
            pick_order = PickOrder.objects.get(
                Q(order_number=reference_number) | Q(order_trackingno=reference_number),
                company=company,
            )
        except PickOrder.DoesNotExist:
            return Response(
                {"status": "error", "message": "PickOrder does not exist"}, status=404
            )
        # except PickOrder.MultipleObjectsReturned:
        #     return Response(
        #         {"status": "error", "message": "PickOrder does not exist"}, status=404
        #     )

        list_order_item = pick_order.order_json["list"]

        for order_item in list_order_item:
            order_item["order_number"] = pick_order.order_number
            order_item["tracking_number"] = pick_order.order_trackingno

        return Response({"list_order_items": list_order_item})
