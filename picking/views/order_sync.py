from django.utils import timezone
from django.forms.models import model_to_dict
from django.shortcuts import get_object_or_404
from typing import Dict, List
from django.db import transaction
from django.http import Http404
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.serializers import ReturnList
from rest_framework.status import (
    HTTP_204_NO_CONTENT,
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_400_BAD_REQUEST,
)
from cloudtasks.tasks import create_cancel_order_task
from companies.models import Company
from core.authentication import DobybotJWTAuthentication
from core.responses.error_responses import ResponseError
from core.serializers.serializers import parse_date
from core.throttling import InputTaskUserThrottle
from etax.models import TaxDocument
from etax.permissions import CanCreateETaxDocument
from picking.models import OrderLastNumber, PickOrder, get_next_number
from picking.permissions import CanManageEasyOrder
from picking.serializers import ZortOrderSerializer
from rest_framework.authentication import TokenAuthentication
from rest_framework.generics import GenericAPIView
from rest_framework import serializers
from core.permissions import ModelPermissions
from picking.serializers.serializers import PickOrderSerializer
from picking.services.webhook import WebhookService
from services.etax_invoice.etax_service import ETaxService
from shipping.permissions import CanAddPickOrderTrackingNo
from shipping.services import ShippingService
from django.utils.translation import gettext as _
from utils.valid_str import is_valid_eng_keyboard_char

# Zort API Docs
# https://zortout.com/apidocs/ZORT_API_Spec_6_8.pdf?_gl=1*12pvpr9*_ga*MTM5ODA3MDc2Ny4xNjM5NzE3MDI0*_ga_Z5R537XZJ4*MTY0MDcwMTg3OC41MS4xLjE2NDA3MDI0OTguMA


class OrderSyncAPI(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [ModelPermissions]
    throttle_classes = [InputTaskUserThrottle]
    queryset = PickOrder.objects.all()

    @transaction.atomic
    def post(self, request):
        serializer = ZortOrderSerializer(data=request.data, many=True)
        if not serializer.is_valid():
            if type(serializer.errors) is not ReturnList:
                return Response(serializer.errors, status=400)

            errors = []
            for order, error in zip(request.data, serializer.errors):
                if error:
                    errors.append({"_order_number": order.get("number"), **error})
                else:
                    errors.append({})
            return Response(errors, status=400)
        # serializer.is_valid(raise_exception=True)
        company: Company = request.user.company

        append_tracking = company.get_setting("VRICH_ORDER_NUMBER_APPEND_TRACKING")

        existed_pick_orders = self.get_existed_pick_orders(
            company=company,
            order_numbers=[o["number"] for o in serializer.validated_data],
        )

        # upsertlist = []
        insertlist = []
        updatelist = []
        cancel_order_numbers = []
        payment_change_order_numbers = []
        for order in serializer.validated_data:
            if not is_valid_eng_keyboard_char(order["number"]):
                continue

            if append_tracking:
                order["number"] = f"{order['number']}:{order['trackingno']}"

            if not order["paymentamount"]:
                order["paymentamount"] = order["amount"]

            pick_order: PickOrder = existed_pick_orders.get(order["number"])
            if pick_order:
                cancel_status = ["Voided", "Partial Voided"]
                if (
                    order.get("status") in cancel_status
                    and pick_order.order_json.get("status") not in cancel_status
                ):
                    cancel_order_numbers.append(order["number"])

                if order.get("amount") != pick_order.order_json.get("amount"):
                    payment_change_order_numbers.append(order["number"])

                pick_order.update_from_zort_order(
                    order=order, commit=False, update_by=request.user
                )
                updatelist.append(pick_order)
            else:
                pick_order = PickOrder.create_from_zort_order(
                    company=company, order=order, create_by=request.user, commit=False
                )
                insertlist.append(pick_order)
            # upsertlist.append(self.pick_order_to_dict(pick_order))

        # count = bulk_update_or_create(
        #     PickOrder,
        #     values=upsertlist,
        #     key_fields=['company', 'order_number']
        # )

        PickOrder.objects.bulk_create(insertlist)
        PickOrder.objects.bulk_update(
            updatelist,
            fields=[
                "order_saleschannel",
                "order_customer",
                "order_customerphone",
                "order_trackingno",
                "order_warehousecode",
                "order_shippingchannel",
                "order_total_price",
                "order_total_quantity",
                "order_json",
                "update_by",
                "order_marketplace",
                "order_marketplaceshop",
                "order_oms",
                "remark",
                "remark_status",
            ],
        )

        company: Company = request.user.company

        if company.get_setting("AUTO_GENERATE_TRACKINGNO_ENABLE"):
            update_order_numbers = [
                x.order_number for x in updatelist if x.order_trackingno == ""
            ]
            insert_order_numbers = [
                x.order_number for x in insertlist if x.order_trackingno == ""
            ]
            order_numbers = update_order_numbers + insert_order_numbers
            pick_orders = PickOrder.objects.filter(
                order_number__in=order_numbers,
                order_trackingno__exact="",
                company=company,
            )
            ShippingService.enqueue_create_tracking_no(company, pick_orders)

        if company.get_setting("BKP_PREDICT_ORDER_LEVEL"):
            insert_order_numbers = [x.order_number for x in insertlist]
            order_numbers = update_order_numbers + insert_order_numbers
            pick_orders = PickOrder.objects.filter(
                order_number__in=insert_order_numbers,
                company=company,
            )
            WebhookService.enqueue_predict_order_level(pick_orders)

        if cancel_order_numbers:
            ETaxService.enqueue_auto_cancel_etax_tasks(
                company=company,
                order_numbers=cancel_order_numbers,
            )

        if company.feature_flag.get(Company.ETAX) and company.get_setting(
            "ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST"
        ):
            auto_etax_numbers = []
            for order in insertlist + updatelist:
                is_void = order.order_json.get("status") in ["Voided", "Partial Voided"]
                if (
                    order.order_json.get("customeridnumber")
                    and not is_void
                    and not PickOrder.is_splitted_order(order.order_number)
                ):
                    auto_etax_numbers.append(order.order_number)

            start_date = company.get_setting("ETAX_AUTO_START_DATE")
            start_date = (
                parse_date(start_date)
                if start_date
                else timezone.localdate().replace(day=1)
            )
            pick_orders = PickOrder.objects.filter(
                company=company,
                order_number__in=auto_etax_numbers,
                order_date__gte=start_date,
            )
            ETaxService.enqueue_auto_etax_tasks(pick_orders)

        if payment_change_order_numbers:
            _action = company.get_setting("ETAX_AUTO_ACTION_WHEN_ORDER_CHANGE")
            if _action == "cancel":
                ETaxService.enqueue_auto_cancel_etax_tasks(
                    company=company,
                    order_numbers=payment_change_order_numbers,
                )
            elif _action == "cancel_and_renew":
                pick_orders = PickOrder.objects.filter(
                    company=company,
                    order_number__in=payment_change_order_numbers,
                )
                ETaxService.enqueue_cancel_and_renew_tasks(pick_orders)

        map_order_number_with_split = {}
        for order in serializer.validated_data:
            if "---" in order["number"]:
                order_number = order["number"].split("---")[0]
                if order_number not in map_order_number_with_split:
                    map_order_number_with_split[order_number] = []
                map_order_number_with_split[order_number].append(order["number"])

        for order_number, order_numbers in map_order_number_with_split.items():
            total_order = len(order_numbers)
            pick_orders = PickOrder.objects.filter(
                order_number__startswith=f"{order_number}---",
                company=company,
            ).exclude(order_number__endswith=f"-{total_order}")

            for pick_order in pick_orders:
                pick_order.order_json["status"] = "Voided"
                pick_order.order_trackingno = ""
                pick_order.remark = "ยกเลิกออเดอร์เนื่องจากมีการแตกกล่องเพิ่ม"
            PickOrder.objects.bulk_update(
                pick_orders, fields=["order_json", "order_trackingno", "remark"]
            )

        # return Response({
        #     'status': 'success',
        #     'message': f'Successfully importing {count} order',
        # }, status=HTTP_200_OK)
        return Response(status=HTTP_204_NO_CONTENT)

    def get_existed_pick_orders(
        self, company: Company, order_numbers: List[str]
    ) -> Dict[str, PickOrder]:
        pick_orders = PickOrder.objects.filter(
            company=company, order_number__in=order_numbers
        )
        return {po.order_number: po for po in pick_orders}

    def pick_order_to_dict(self, pick_order):
        data = model_to_dict(pick_order)
        del data["id"]
        return data

    # def loop_create_cancel_task(self, company: Company, order_numbers: List[str]):
    #     if company.get_setting("ETAX_SELLER"):
    #         for order_number in order_numbers:
    #             create_cancel_order_task(
    #                 company_id=company.id,
    #                 order_number=order_number,
    #             )


class OrderCreateAPI(APIView):
    authentication_classes = [TokenAuthentication, DobybotJWTAuthentication]
    permission_classes = [ModelPermissions]
    throttle_classes = [InputTaskUserThrottle]
    queryset = PickOrder.objects.all()

    def post(self, request):
        serializer = ZortOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        company = request.user.company
        order_number = serializer.validated_data["number"]
        if PickOrder.objects.filter(
            company=company, order_number=order_number
        ).exists():
            raise serializers.ValidationError(
                {
                    "number": f"This field must be unique, '{order_number}' is already taken."
                }
            )

        PickOrder.create_from_zort_order(
            company=request.user.company,
            order=serializer.validated_data,
            create_by=request.user,
        )
        return Response(status=HTTP_204_NO_CONTENT)


class OrderUpdateAPI(GenericAPIView):
    authentication_classes = [TokenAuthentication, DobybotJWTAuthentication]
    permission_classes = [CanAddPickOrderTrackingNo | ModelPermissions]
    throttle_classes = [InputTaskUserThrottle]
    lookup_url_kwarg = "order_number"
    lookup_field = "order_number"

    def get_object(self):
        company_id = self.request.user.company_id
        order_number = self.kwargs["order_number"].replace("-slash-", "/")
        obj = get_object_or_404(
            PickOrder,
            company_id=company_id,
            order_number=order_number,
        )

        # May raise a permission denied
        self.check_object_permissions(self.request, obj)

        return obj

    def get_queryset(self, queryset=None):
        company = self.request.user.company
        return PickOrder.objects.filter(company=company)

    def put(self, request, order_number):
        serializer = ZortOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            pick_order: PickOrder = self.get_object()
            pick_order.update_from_zort_order(
                serializer.validated_data, update_by=request.user
            )
        except Http404:
            company = request.user.company
            pick_order = PickOrder.create_from_zort_order(
                company=company,
                create_by=request.user,
                order=serializer.validated_data,
            )

        self.create_post_save_tasks(pick_order)
        return Response(status=HTTP_204_NO_CONTENT)

    def patch(self, request, order_number):
        serializer = ZortOrderSerializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        pick_order: PickOrder = self.get_object()
        for key in data:
            pick_order.order_json[key] = data[key]

        pick_order.update_from_zort_order(pick_order.order_json, update_by=request.user)
        self.create_post_save_tasks(pick_order)

        return Response("OK", status=HTTP_200_OK)

    def create_post_save_tasks(self, pick_order: PickOrder):
        company = pick_order.company

        if pick_order.order_json["status"] in ["Voided", "Partial Voided"]:
            ETaxService.enqueue_auto_cancel_etax_tasks(
                company=company,
                order_numbers=[pick_order.order_number],
            )


class EasyOrderCreateAPI(APIView):
    permission_classes = [CanManageEasyOrder | CanCreateETaxDocument]

    class Validator(serializers.Serializer):
        order_json = ZortOrderSerializer()
        auto_gen_order_number = serializers.BooleanField()

        def validate(self, attrs):
            attrs = super().validate(attrs)
            request = self.context["request"]

            if attrs["auto_gen_order_number"]:
                return attrs

            order_number = attrs["order_json"]["number"]
            if PickOrder.objects.filter(
                order_number=order_number, company_id=request.user.company_id
            ).exists():
                raise serializers.ValidationError(
                    {
                        "order_json": {
                            "number": [_("This order number already exists.")],
                        },
                        "status": "THIS_ORDER_ALREADY_EXISTS",
                    }
                )

            return attrs

    @transaction.atomic
    def post(self, request):
        validator = self.Validator(data=request.data, context={"request": request})
        validator.is_valid(raise_exception=True)

        company: Company = request.user.company
        order_json = validator.validated_data["order_json"]
        auto_gen_order_number = validator.validated_data["auto_gen_order_number"]
        order_json["orderManagementSystem"] = "easy-order"
        order_json["is_confirm_received"] = True

        if auto_gen_order_number:
            # TODO Warning developer will use function get_next_number
            order_json["number"] = get_next_number(OrderLastNumber, company=company)

        ok, errors = ETaxService.check_order_d1a_json_data(order_json, None)
        if not ok:
            return Response(errors.get("order_json"), status=HTTP_400_BAD_REQUEST)

        pick_order_created = PickOrder.create_from_zort_order(
            company=request.user.company,
            order=order_json,
            order_oms="easy-order",
            create_by=request.user,
        )

        pick_order_created.refresh_from_db()

        serializer = PickOrderSerializer(pick_order_created)
        return Response(serializer.data, status=HTTP_201_CREATED)


class EasyOrderUpdateAPI(APIView):
    permission_classes = [CanManageEasyOrder]

    def get(self, request, order_number):
        pick_order = get_object_or_404(
            PickOrder,
            company_id=request.user.company_id,
            order_number=order_number,
        )

        serializer = PickOrderSerializer(pick_order)
        return Response(serializer.data)

    def put(self, request, order_number):
        serializer = ZortOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        ok, errors = ETaxService.check_order_d1a_json_data(
            serializer.validated_data, None
        )
        if not ok:
            return Response(errors.get("order_json"), status=HTTP_400_BAD_REQUEST)

        pick_order = get_object_or_404(
            PickOrder,
            company_id=request.user.company_id,
            order_number=order_number,
        )

        # validate has document
        if (
            TaxDocument.objects.filter(
                company_id=request.user.company_id,
                pick_order=pick_order,
            )
            .exclude(status=TaxDocument.STATUS_CANCEL)
            .exists()
        ):
            return ResponseError("TAX_DOCUMENT_ALREADY_CREATED")

        pick_order.update_from_zort_order(
            serializer.validated_data, update_by=request.user, order_oms="easy-order"
        )
        pick_order.refresh_from_db()

        serializer = PickOrderSerializer(pick_order)
        return Response(serializer.data, status=HTTP_201_CREATED)


class ETaxEasyOrderUpdateAPIView(APIView):
    permission_classes = [CanCreateETaxDocument]

    class Validator(serializers.Serializer):
        order_json = ZortOrderSerializer()

    def get(self, request, order_number):
        pick_order = get_object_or_404(
            PickOrder,
            company_id=request.user.company_id,
            order_number=order_number,
        )

        serializer = PickOrderSerializer(pick_order)
        return Response(serializer.data)

    def put(self, request, order_number):
        if TaxDocument.objects.filter(
            company_id=request.user.company_id, order_number=order_number
        ).exists():
            return ResponseError("TAX_DOCUMENT_ALREADY_CREATED")

        serializer = self.Validator(data=request.data)
        serializer.is_valid(raise_exception=True)

        pick_order = get_object_or_404(
            PickOrder,
            company_id=request.user.company_id,
            order_number=order_number,
        )

        order_json = serializer.validated_data["order_json"]

        pick_order.update_from_zort_order(
            order_json, update_by=request.user, order_oms="easy-order"
        )
        pick_order.refresh_from_db()

        serializer = PickOrderSerializer(pick_order)
        return Response(serializer.data, status=HTTP_201_CREATED)
