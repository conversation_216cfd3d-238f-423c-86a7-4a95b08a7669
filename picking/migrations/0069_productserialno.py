# Generated by Django 3.2.19 on 2025-03-24 07:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0078_auto_20250324_1401'),
        ('picking', '0068_merge_20250128_0223'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductSerialNo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_no', models.CharField(db_index=True, max_length=200)),
                ('sku', models.CharField(db_index=True, max_length=200)),
                ('in_stock', models.BooleanField(default=True)),
                ('cutoff_date', models.DateTimeField(auto_now=True, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.company')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='picking.product')),
            ],
        ),
    ]
