# Generated by Django 3.2.19 on 2025-05-26 05:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import nanoid.generate


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('companies', '0081_alter_settingvalue_key'),
    ]

    operations = [
        migrations.CreateModel(
            name='ETLJob',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.CharField(db_index=True, default=nanoid.generate.generate, max_length=21, unique=True)),
                ('job_type', models.CharField(choices=[('orders_export', 'Orders Export'), ('order_items_export', 'Order Items Export'), ('full_export', 'Full Export')], max_length=50)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('orders_csv_path', models.CharField(blank=True, max_length=500, null=True)),
                ('order_items_csv_path', models.CharField(blank=True, max_length=500, null=True)),
                ('bigquery_dataset', models.CharField(blank=True, max_length=100, null=True)),
                ('bigquery_orders_table', models.CharField(blank=True, max_length=100, null=True)),
                ('bigquery_order_items_table', models.CharField(blank=True, max_length=100, null=True)),
                ('total_orders', models.IntegerField(default=0)),
                ('total_order_items', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
