from django.db import models
from django.contrib.auth import get_user_model
import nanoid

User = get_user_model()


class ETLJob(models.Model):
    """Track ETL job executions"""
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    JOB_TYPE_CHOICES = [
        ('orders_export', 'Orders Export'),
        ('order_items_export', 'Order Items Export'),
        ('full_export', 'Full Export'),
    ]
    
    uuid = models.CharField(
        unique=True, db_index=True, default=nanoid.generate, max_length=21
    )
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    job_type = models.CharField(max_length=50, choices=JOB_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Date range for export
    start_date = models.DateField()
    end_date = models.DateField()
    
    # File paths
    orders_csv_path = models.CharField(max_length=500, blank=True, null=True)
    order_items_csv_path = models.CharField(max_length=500, blank=True, null=True)
    
    # BigQuery details
    bigquery_dataset = models.CharField(max_length=100, blank=True, null=True)
    bigquery_orders_table = models.CharField(max_length=100, blank=True, null=True)
    bigquery_order_items_table = models.CharField(max_length=100, blank=True, null=True)
    
    # Execution details
    total_orders = models.IntegerField(default=0)
    total_order_items = models.IntegerField(default=0)
    error_message = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(blank=True, null=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    # User tracking
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"ETL Job {self.uuid} - {self.job_type} ({self.status})"
    
    @property
    def duration(self):
        """Calculate job duration if completed"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None
