"""
Enhanced ETL Services with deduplication and upsert support
"""

import csv
import json
import uuid
import hashlib
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from io import StringIO

from django.conf import settings
from django.utils import timezone as django_timezone
from google.cloud import bigquery, storage
from google.cloud.exceptions import NotFound

from picking.models import PickOrder
from .bigquery_schemas_v2 import (
    BigQuerySchemaManager,
    SCHEMA_VERSION,
    get_dataset_name,
    get_orders_table_name,
    get_order_items_table_name,
)
from .models import ETLJob


class BigQueryServiceV2:
    """Enhanced BigQuery service with deduplication support"""

    def __init__(self):
        self.client = bigquery.Client()

    def ensure_dataset_exists(self, company_id: int) -> str:
        """Ensure BigQuery dataset exists for company"""
        dataset_name = get_dataset_name(company_id)
        dataset_id = f"{self.client.project}.{dataset_name}"

        try:
            self.client.get_dataset(dataset_id)
        except NotFound:
            # Create dataset
            dataset = bigquery.Dataset(dataset_id)
            dataset.location = getattr(settings, "BIGQUERY_LOCATION", "US")
            dataset.description = f"Dobybot data for company {company_id}"
            self.client.create_dataset(dataset)

        return dataset_name

    def ensure_tables_exist(self, dataset_name: str):
        """Ensure orders and order_items tables exist with latest schema"""
        # Create orders table
        orders_table_id = (
            f"{self.client.project}.{dataset_name}.{get_orders_table_name()}"
        )
        try:
            self.client.get_table(orders_table_id)
        except NotFound:
            orders_table = bigquery.Table(
                orders_table_id, schema=BigQuerySchemaManager.get_orders_schema()
            )
            # Add clustering for better performance
            orders_table.clustering_fields = ["company_id", "order_date"]
            # Add partitioning by order_date
            orders_table.time_partitioning = bigquery.TimePartitioning(
                type_=bigquery.TimePartitioningType.DAY, field="order_date"
            )
            self.client.create_table(orders_table)

        # Create order_items table
        order_items_table_id = (
            f"{self.client.project}.{dataset_name}.{get_order_items_table_name()}"
        )
        try:
            self.client.get_table(order_items_table_id)
        except NotFound:
            order_items_table = bigquery.Table(
                order_items_table_id,
                schema=BigQuerySchemaManager.get_order_items_schema(),
            )
            # Add clustering for better performance
            order_items_table.clustering_fields = [
                "company_id",
                "order_date",
                "order_id",
            ]
            # Add partitioning by order_date
            order_items_table.time_partitioning = bigquery.TimePartitioning(
                type_=bigquery.TimePartitioningType.DAY, field="order_date"
            )
            self.client.create_table(order_items_table)

    def upsert_data_from_csv(self, dataset_name: str, table_name: str, csv_uri: str):
        """Upsert data from CSV using MERGE statement to avoid duplicates"""

        # Create temporary table for new data
        temp_table_id = f"{self.client.project}.{dataset_name}.{table_name}_temp_{int(datetime.now().timestamp())}"

        # Get schema
        if table_name == get_orders_table_name():
            schema = BigQuerySchemaManager.get_orders_schema()
            merge_key = "order_id"
        else:
            schema = BigQuerySchemaManager.get_order_items_schema()
            merge_key = "order_item_id"

        try:
            # Create temporary table
            temp_table = bigquery.Table(temp_table_id, schema=schema)
            temp_table = self.client.create_table(temp_table)

            # Load CSV into temporary table
            job_config = bigquery.LoadJobConfig(
                source_format=bigquery.SourceFormat.CSV,
                skip_leading_rows=1,
                schema=schema,
                write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
            )

            load_job = self.client.load_table_from_uri(
                csv_uri, temp_table_id, job_config=job_config
            )
            load_job.result()  # Wait for completion

            # Perform MERGE operation
            target_table_id = f"{self.client.project}.{dataset_name}.{table_name}"

            # Build field list for MERGE
            field_names = [field.name for field in schema]
            field_list = ", ".join(field_names)
            update_list = ", ".join(
                [
                    f"target.{field} = source.{field}"
                    for field in field_names
                    if field != merge_key
                ]
            )

            merge_query = f"""
            MERGE `{target_table_id}` AS target
            USING `{temp_table_id}` AS source
            ON target.{merge_key} = source.{merge_key}
            WHEN MATCHED THEN
                UPDATE SET {update_list}
            WHEN NOT MATCHED THEN
                INSERT ({field_list})
                VALUES ({field_list})
            """

            merge_job = self.client.query(merge_query)
            merge_job.result()  # Wait for completion

            return merge_job.num_dml_affected_rows

        finally:
            # Clean up temporary table
            try:
                self.client.delete_table(temp_table_id)
            except:
                pass  # Ignore cleanup errors


class CSVExportServiceV2:
    """Enhanced CSV export service with deduplication support"""

    def __init__(self, storage_client=None):
        self.storage_client = storage_client or storage.Client()

    def calculate_record_hash(self, record_data: Dict[str, Any]) -> str:
        """Calculate hash for record deduplication"""
        # Remove ETL-specific fields from hash calculation
        hash_data = {
            k: v
            for k, v in record_data.items()
            if not k.startswith("etl_") and k != "record_hash"
        }

        # Sort keys for consistent hashing
        sorted_data = json.dumps(hash_data, sort_keys=True, default=str)
        return hashlib.sha256(sorted_data.encode()).hexdigest()

    def export_orders_to_csv(
        self, company_id: int, start_date, end_date
    ) -> tuple[str, int]:
        """Export orders to CSV with deduplication support"""
        orders = PickOrder.objects.filter(
            company_id=company_id, order_date__gte=start_date, order_date__lte=end_date
        ).order_by("order_date", "id")

        # Generate CSV content
        csv_content = StringIO()
        writer = csv.writer(csv_content)

        # Write header (using schema field names)
        schema_fields = BigQuerySchemaManager.get_orders_schema()
        headers = [field.name for field in schema_fields]
        writer.writerow(headers)

        # Write data
        etl_timestamp = django_timezone.now().isoformat()
        for order in orders:
            # Prepare record data
            record_data = {
                "order_id": order.id,
                "uuid": order.uuid,
                "company_id": order.company_id,
                "order_number": order.order_number,
                "order_saleschannel": order.order_saleschannel,
                "order_customer": order.order_customer,
                "order_customerphone": order.order_customerphone,
                "order_trackingno": order.order_trackingno,
                "order_warehousecode": order.order_warehousecode,
                "order_shippingchannel": order.order_shippingchannel,
                "order_total_quantity": order.order_total_quantity,
                "order_total_price": (
                    str(order.order_total_price) if order.order_total_price else None
                ),
                "order_oms": order.order_oms,
                "order_marketplace": order.order_marketplace,
                "order_marketplaceshop": order.order_marketplaceshop,
                "order_date": order.order_date.isoformat(),
                "order_json": (
                    json.dumps(order.order_json) if order.order_json else None
                ),
                "packing_json": (
                    json.dumps(order.packing_json) if order.packing_json else None
                ),
                "receipt_url": order.receipt_url,
                "short_receipt_url": order.short_receipt_url,
                "ready_to_ship": order.ready_to_ship,
                "ready_to_ship_timestamp": (
                    order.ready_to_ship_timestamp.isoformat()
                    if order.ready_to_ship_timestamp
                    else None
                ),
                "create_date": order.create_date.isoformat(),
                "update_date": (
                    order.update_date.isoformat() if order.update_date else None
                ),
                "etl_processed_at": etl_timestamp,
                "etl_schema_version": SCHEMA_VERSION,
            }

            # Calculate hash for deduplication
            record_data["record_hash"] = self.calculate_record_hash(record_data)

            # Write row in schema order
            row = [record_data.get(field.name, "") for field in schema_fields]
            writer.writerow(row)

        # Upload to GCS
        bucket_name = getattr(
            settings, "GS_ETL_BUCKET_NAME", settings.GS_SECURE_BUCKET_NAME
        )
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"etl/orders/company_{company_id}/orders_{start_date}_{end_date}_{timestamp}.csv"

        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(file_path)
        blob.upload_from_string(csv_content.getvalue(), content_type="text/csv")

        return f"gs://{bucket_name}/{file_path}", orders.count()

    def export_order_items_to_csv(
        self, company_id: int, start_date, end_date
    ) -> tuple[str, int]:
        """Export order items to CSV with deduplication support"""
        orders = PickOrder.objects.filter(
            company_id=company_id, order_date__gte=start_date, order_date__lte=end_date
        ).order_by("order_date", "id")

        # Generate CSV content
        csv_content = StringIO()
        writer = csv.writer(csv_content)

        # Write header (using schema field names)
        schema_fields = BigQuerySchemaManager.get_order_items_schema()
        headers = [field.name for field in schema_fields]
        writer.writerow(headers)

        # Write data
        etl_timestamp = django_timezone.now().isoformat()
        total_items = 0

        for order in orders:
            if order.order_json and "list" in order.order_json:
                for item in order.order_json["list"]:
                    total_items += 1

                    # Generate deterministic order_item_id based on order and item data
                    item_key = f"{order.id}_{item.get('sku', '')}_{item.get('productid', '')}_{total_items}"
                    order_item_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, item_key))

                    # Prepare record data
                    record_data = {
                        "order_item_id": order_item_id,
                        "order_id": order.id,
                        "order_uuid": order.uuid,
                        "company_id": order.company_id,
                        "order_number": order.order_number,
                        "product_id": item.get("productid"),
                        "sku": item.get("sku"),
                        "name": item.get("name"),
                        "quantity": item.get("number"),
                        "unit_text": item.get("unittext"),
                        "price_per_unit": str(item.get("pricepernumber", 0)),
                        "discount": item.get("discount"),
                        "discount_amount": str(item.get("discountamount", 0)),
                        "total_price": str(item.get("totalprice", 0)),
                        "product_type": item.get("producttype"),
                        "serial_no_list": json.dumps(item.get("serialnolist", [])),
                        "sku_type": item.get("skutype"),
                        "order_date": order.order_date.isoformat(),
                        "etl_processed_at": etl_timestamp,
                        "etl_schema_version": SCHEMA_VERSION,
                    }

                    # Calculate hash for deduplication
                    record_data["record_hash"] = self.calculate_record_hash(record_data)

                    # Write row in schema order
                    row = [record_data.get(field.name, "") for field in schema_fields]
                    writer.writerow(row)

        # Upload to GCS
        bucket_name = getattr(
            settings, "GS_ETL_BUCKET_NAME", settings.GS_SECURE_BUCKET_NAME
        )
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"etl/order_items/company_{company_id}/order_items_{start_date}_{end_date}_{timestamp}.csv"

        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(file_path)
        blob.upload_from_string(csv_content.getvalue(), content_type="text/csv")

        return f"gs://{bucket_name}/{file_path}", total_items


class ETLServiceV2:
    """Enhanced ETL service with deduplication and upsert support"""

    def __init__(self):
        self.bigquery_service = BigQueryServiceV2()
        self.csv_service = CSVExportServiceV2()

    def run_etl_job(self, etl_job: ETLJob):
        """Run complete ETL job with deduplication"""
        try:
            # Update job status
            etl_job.status = "running"
            etl_job.started_at = django_timezone.now()
            etl_job.save()

            # Ensure BigQuery infrastructure exists
            dataset_name = self.bigquery_service.ensure_dataset_exists(
                etl_job.company_id
            )
            self.bigquery_service.ensure_tables_exist(dataset_name)

            # Export data to CSV
            if etl_job.job_type in ["orders_export", "full_export"]:
                orders_path, orders_count = self.csv_service.export_orders_to_csv(
                    etl_job.company_id, etl_job.start_date, etl_job.end_date
                )
                etl_job.orders_csv_path = orders_path
                etl_job.total_orders = orders_count

            if etl_job.job_type in ["order_items_export", "full_export"]:
                order_items_path, items_count = (
                    self.csv_service.export_order_items_to_csv(
                        etl_job.company_id, etl_job.start_date, etl_job.end_date
                    )
                )
                etl_job.order_items_csv_path = order_items_path
                etl_job.total_order_items = items_count

            # Update job with BigQuery details
            etl_job.bigquery_dataset = dataset_name
            etl_job.bigquery_orders_table = get_orders_table_name()
            etl_job.bigquery_order_items_table = get_order_items_table_name()

            # Mark as completed
            etl_job.status = "completed"
            etl_job.completed_at = django_timezone.now()
            etl_job.save()

        except Exception as e:
            etl_job.status = "failed"
            etl_job.error_message = str(e)
            etl_job.completed_at = django_timezone.now()
            etl_job.save()
            raise
