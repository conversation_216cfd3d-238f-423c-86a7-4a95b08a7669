"""
ETL Services for BigQuery data pipeline
"""

import csv
import json
import uuid
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from io import String<PERSON>

from django.conf import settings
from django.utils import timezone as django_timezone
from google.cloud import bigquery, storage
from google.cloud.exceptions import NotFound

from picking.models import Pick<PERSON>rder
from .bigquery_schemas import (
    ORDERS_SCHEMA, 
    ORDER_ITEMS_SCHEMA,
    get_dataset_name,
    get_orders_table_name,
    get_order_items_table_name
)
from .models import ETLJob


class BigQueryService:
    """Service for BigQuery operations"""
    
    def __init__(self):
        self.client = bigquery.Client()
        
    def ensure_dataset_exists(self, company_id: int) -> str:
        """Ensure BigQuery dataset exists for company"""
        dataset_name = get_dataset_name(company_id)
        dataset_id = f"{self.client.project}.{dataset_name}"
        
        try:
            self.client.get_dataset(dataset_id)
        except NotFound:
            # Create dataset
            dataset = bigquery.Dataset(dataset_id)
            dataset.location = "US"  # or your preferred location
            dataset.description = f"Dobybot data for company {company_id}"
            self.client.create_dataset(dataset)
            
        return dataset_name
    
    def ensure_tables_exist(self, dataset_name: str):
        """Ensure orders and order_items tables exist"""
        # Create orders table
        orders_table_id = f"{self.client.project}.{dataset_name}.{get_orders_table_name()}"
        try:
            self.client.get_table(orders_table_id)
        except NotFound:
            orders_table = bigquery.Table(orders_table_id, schema=ORDERS_SCHEMA)
            self.client.create_table(orders_table)
            
        # Create order_items table
        order_items_table_id = f"{self.client.project}.{dataset_name}.{get_order_items_table_name()}"
        try:
            self.client.get_table(order_items_table_id)
        except NotFound:
            order_items_table = bigquery.Table(order_items_table_id, schema=ORDER_ITEMS_SCHEMA)
            self.client.create_table(order_items_table)


class CSVExportService:
    """Service for exporting data to CSV"""
    
    def __init__(self, storage_client=None):
        self.storage_client = storage_client or storage.Client()
        
    def export_orders_to_csv(self, company_id: int, start_date, end_date) -> tuple[str, int]:
        """Export orders to CSV and upload to GCS"""
        orders = PickOrder.objects.filter(
            company_id=company_id,
            order_date__gte=start_date,
            order_date__lte=end_date
        ).order_by('order_date', 'id')
        
        # Generate CSV content
        csv_content = StringIO()
        writer = csv.writer(csv_content)
        
        # Write header
        headers = [
            'order_id', 'uuid', 'company_id', 'order_number', 'order_saleschannel',
            'order_customer', 'order_customerphone', 'order_trackingno', 
            'order_warehousecode', 'order_shippingchannel', 'order_total_quantity',
            'order_total_price', 'order_oms', 'order_marketplace', 'order_marketplaceshop',
            'order_date', 'order_json', 'packing_json', 'receipt_url', 'short_receipt_url',
            'ready_to_ship', 'ready_to_ship_timestamp', 'create_date', 'update_date',
            'etl_processed_at'
        ]
        writer.writerow(headers)
        
        # Write data
        etl_timestamp = django_timezone.now().isoformat()
        for order in orders:
            row = [
                order.id,
                order.uuid,
                order.company_id,
                order.order_number,
                order.order_saleschannel,
                order.order_customer,
                order.order_customerphone,
                order.order_trackingno,
                order.order_warehousecode,
                order.order_shippingchannel,
                order.order_total_quantity,
                str(order.order_total_price),
                order.order_oms,
                order.order_marketplace,
                order.order_marketplaceshop,
                order.order_date.isoformat(),
                json.dumps(order.order_json) if order.order_json else None,
                json.dumps(order.packing_json) if order.packing_json else None,
                order.receipt_url,
                order.short_receipt_url,
                order.ready_to_ship,
                order.ready_to_ship_timestamp.isoformat() if order.ready_to_ship_timestamp else None,
                order.create_date.isoformat(),
                order.update_date.isoformat() if order.update_date else None,
                etl_timestamp
            ]
            writer.writerow(row)
        
        # Upload to GCS
        bucket_name = settings.GS_SECURE_BUCKET_NAME
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_path = f"etl/orders/company_{company_id}/orders_{start_date}_{end_date}_{timestamp}.csv"
        
        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(file_path)
        blob.upload_from_string(csv_content.getvalue(), content_type='text/csv')
        
        return f"gs://{bucket_name}/{file_path}", orders.count()
        
    def export_order_items_to_csv(self, company_id: int, start_date, end_date) -> tuple[str, int]:
        """Export order items to CSV and upload to GCS"""
        orders = PickOrder.objects.filter(
            company_id=company_id,
            order_date__gte=start_date,
            order_date__lte=end_date
        ).order_by('order_date', 'id')
        
        # Generate CSV content
        csv_content = StringIO()
        writer = csv.writer(csv_content)
        
        # Write header
        headers = [
            'order_item_id', 'order_id', 'order_uuid', 'company_id', 'order_number',
            'product_id', 'sku', 'name', 'quantity', 'unit_text', 'price_per_unit',
            'discount', 'discount_amount', 'total_price', 'product_type', 
            'serial_no_list', 'sku_type', 'order_date', 'etl_processed_at'
        ]
        writer.writerow(headers)
        
        # Write data
        etl_timestamp = django_timezone.now().isoformat()
        total_items = 0
        
        for order in orders:
            if order.order_json and 'list' in order.order_json:
                for item in order.order_json['list']:
                    total_items += 1
                    row = [
                        str(uuid.uuid4()),  # Generate unique ID for order item
                        order.id,
                        order.uuid,
                        order.company_id,
                        order.order_number,
                        item.get('productid'),
                        item.get('sku'),
                        item.get('name'),
                        item.get('number'),  # quantity
                        item.get('unittext'),
                        str(item.get('pricepernumber', 0)),
                        item.get('discount'),
                        str(item.get('discountamount', 0)),
                        str(item.get('totalprice', 0)),
                        item.get('producttype'),
                        json.dumps(item.get('serialnolist', [])),
                        item.get('skutype'),
                        order.order_date.isoformat(),
                        etl_timestamp
                    ]
                    writer.writerow(row)
        
        # Upload to GCS
        bucket_name = settings.GS_SECURE_BUCKET_NAME
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_path = f"etl/order_items/company_{company_id}/order_items_{start_date}_{end_date}_{timestamp}.csv"
        
        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(file_path)
        blob.upload_from_string(csv_content.getvalue(), content_type='text/csv')
        
        return f"gs://{bucket_name}/{file_path}", total_items


class ETLService:
    """Main ETL service orchestrator"""
    
    def __init__(self):
        self.bigquery_service = BigQueryService()
        self.csv_service = CSVExportService()
        
    def run_etl_job(self, etl_job: ETLJob):
        """Run complete ETL job"""
        try:
            # Update job status
            etl_job.status = 'running'
            etl_job.started_at = django_timezone.now()
            etl_job.save()
            
            # Ensure BigQuery infrastructure exists
            dataset_name = self.bigquery_service.ensure_dataset_exists(etl_job.company_id)
            self.bigquery_service.ensure_tables_exist(dataset_name)
            
            # Export data to CSV
            if etl_job.job_type in ['orders_export', 'full_export']:
                orders_path, orders_count = self.csv_service.export_orders_to_csv(
                    etl_job.company_id, etl_job.start_date, etl_job.end_date
                )
                etl_job.orders_csv_path = orders_path
                etl_job.total_orders = orders_count
                
            if etl_job.job_type in ['order_items_export', 'full_export']:
                order_items_path, items_count = self.csv_service.export_order_items_to_csv(
                    etl_job.company_id, etl_job.start_date, etl_job.end_date
                )
                etl_job.order_items_csv_path = order_items_path
                etl_job.total_order_items = items_count
            
            # Update job with BigQuery details
            etl_job.bigquery_dataset = dataset_name
            etl_job.bigquery_orders_table = get_orders_table_name()
            etl_job.bigquery_order_items_table = get_order_items_table_name()
            
            # Mark as completed
            etl_job.status = 'completed'
            etl_job.completed_at = django_timezone.now()
            etl_job.save()
            
        except Exception as e:
            etl_job.status = 'failed'
            etl_job.error_message = str(e)
            etl_job.completed_at = django_timezone.now()
            etl_job.save()
            raise
