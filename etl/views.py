"""
ETL API Views
"""

from datetime import datetime, timed<PERSON>ta
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.dateparse import parse_date
from django.http import JsonResponse

from .models import ETLJob
from .services import ETLService
from cloudtasks.tasks import create_task
from django.conf import settings


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_etl_export(request):
    """
    Trigger ETL export job
    
    POST /api/etl/export/
    {
        "job_type": "full_export",  // orders_export, order_items_export, full_export
        "start_date": "2024-01-01",  // optional, defaults to yesterday
        "end_date": "2024-01-31"     // optional, defaults to yesterday
    }
    """
    user = request.user
    company = user.company
    
    # Parse request data
    job_type = request.data.get('job_type', 'full_export')
    if job_type not in ['orders_export', 'order_items_export', 'full_export']:
        return Response(
            {'error': 'Invalid job_type. Must be orders_export, order_items_export, or full_export'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Parse dates
    start_date_str = request.data.get('start_date')
    end_date_str = request.data.get('end_date')
    
    if start_date_str:
        start_date = parse_date(start_date_str)
        if not start_date:
            return Response(
                {'error': 'Invalid start_date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )
    else:
        # Default to yesterday
        start_date = (datetime.now() - timedelta(days=1)).date()
        
    if end_date_str:
        end_date = parse_date(end_date_str)
        if not end_date:
            return Response(
                {'error': 'Invalid end_date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )
    else:
        # Default to yesterday
        end_date = (datetime.now() - timedelta(days=1)).date()
        
    if start_date > end_date:
        return Response(
            {'error': 'start_date cannot be after end_date'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Create ETL job
    etl_job = ETLJob.objects.create(
        company=company,
        job_type=job_type,
        start_date=start_date,
        end_date=end_date,
        created_by=user
    )
    
    # Queue the job for background processing
    if settings.CLOUD_TASK_HOST:
        task_id = create_task(
            queue="etl-export",
            url=f"{settings.CLOUD_TASK_HOST}/api/etl/process-job/",
            payload={"etl_job_id": etl_job.id}
        )
    else:
        # For development, run synchronously
        try:
            etl_service = ETLService()
            etl_service.run_etl_job(etl_job)
        except Exception as e:
            return Response(
                {'error': f'ETL job failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return Response({
        'job_id': etl_job.uuid,
        'status': etl_job.status,
        'job_type': etl_job.job_type,
        'start_date': etl_job.start_date,
        'end_date': etl_job.end_date,
        'created_at': etl_job.created_at
    }, status=status.HTTP_201_CREATED)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_etl_job_status(request, job_id):
    """
    Get ETL job status
    
    GET /api/etl/jobs/{job_id}/
    """
    user = request.user
    
    try:
        etl_job = ETLJob.objects.get(uuid=job_id, company=user.company)
    except ETLJob.DoesNotExist:
        return Response(
            {'error': 'ETL job not found'},
            status=status.HTTP_404_NOT_FOUND
        )
    
    response_data = {
        'job_id': etl_job.uuid,
        'status': etl_job.status,
        'job_type': etl_job.job_type,
        'start_date': etl_job.start_date,
        'end_date': etl_job.end_date,
        'total_orders': etl_job.total_orders,
        'total_order_items': etl_job.total_order_items,
        'created_at': etl_job.created_at,
        'started_at': etl_job.started_at,
        'completed_at': etl_job.completed_at,
        'error_message': etl_job.error_message,
    }
    
    if etl_job.status == 'completed':
        response_data.update({
            'bigquery_dataset': etl_job.bigquery_dataset,
            'bigquery_orders_table': etl_job.bigquery_orders_table,
            'bigquery_order_items_table': etl_job.bigquery_order_items_table,
            'duration': etl_job.duration.total_seconds() if etl_job.duration else None
        })
    
    return Response(response_data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_etl_jobs(request):
    """
    List ETL jobs for the user's company
    
    GET /api/etl/jobs/
    """
    user = request.user
    
    jobs = ETLJob.objects.filter(company=user.company).order_by('-created_at')[:50]
    
    jobs_data = []
    for job in jobs:
        jobs_data.append({
            'job_id': job.uuid,
            'status': job.status,
            'job_type': job.job_type,
            'start_date': job.start_date,
            'end_date': job.end_date,
            'total_orders': job.total_orders,
            'total_order_items': job.total_order_items,
            'created_at': job.created_at,
            'completed_at': job.completed_at,
            'duration': job.duration.total_seconds() if job.duration else None
        })
    
    return Response({'jobs': jobs_data})


@api_view(['POST'])
def process_etl_job(request):
    """
    Background task handler for processing ETL jobs
    This endpoint is called by Cloud Tasks
    
    POST /api/etl/process-job/
    {
        "etl_job_id": 123
    }
    """
    # Verify this is coming from Cloud Tasks
    auth_token = request.headers.get('Authorization')
    if not auth_token or auth_token != f"Bearer {settings.CLOUD_TASK_AUTH_TOKEN}":
        return JsonResponse({'error': 'Unauthorized'}, status=401)
    
    etl_job_id = request.data.get('etl_job_id')
    if not etl_job_id:
        return JsonResponse({'error': 'etl_job_id is required'}, status=400)
    
    try:
        etl_job = ETLJob.objects.get(id=etl_job_id)
    except ETLJob.DoesNotExist:
        return JsonResponse({'error': 'ETL job not found'}, status=404)
    
    # Process the job
    try:
        etl_service = ETLService()
        etl_service.run_etl_job(etl_job)
        
        return JsonResponse({
            'status': 'success',
            'job_id': etl_job.uuid,
            'total_orders': etl_job.total_orders,
            'total_order_items': etl_job.total_order_items
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'job_id': etl_job.uuid,
            'error': str(e)
        }, status=500)


@api_view(['POST'])
def trigger_etl_for_all_companies(request):
    """
    Trigger ETL export for all companies
    This endpoint is designed to be called by Cloud Scheduler
    
    POST /api/etl/trigger-all-companies/
    {
        "job_type": "full_export",  // optional, defaults to full_export
        "start_date": "2024-01-01", // optional, defaults to yesterday
        "end_date": "2024-01-31"    // optional, defaults to yesterday
    }
    """
    # Verify this is coming from Cloud Scheduler or has proper auth
    auth_token = request.headers.get('Authorization')
    if not auth_token or auth_token != f"Bearer {settings.CLOUD_TASK_AUTH_TOKEN}":
        return JsonResponse({'error': 'Unauthorized'}, status=401)
    
    # Parse request data
    job_type = request.data.get('job_type', 'full_export')
    start_date_str = request.data.get('start_date')
    end_date_str = request.data.get('end_date')
    
    # Parse dates (same logic as trigger_etl_export)
    if start_date_str:
        start_date = parse_date(start_date_str)
        if not start_date:
            return JsonResponse({'error': 'Invalid start_date format. Use YYYY-MM-DD'}, status=400)
    else:
        start_date = (datetime.now() - timedelta(days=1)).date()
        
    if end_date_str:
        end_date = parse_date(end_date_str)
        if not end_date:
            return JsonResponse({'error': 'Invalid end_date format. Use YYYY-MM-DD'}, status=400)
    else:
        end_date = (datetime.now() - timedelta(days=1)).date()
    
    # Get all active companies
    from companies.models import Company
    companies = Company.objects.filter(is_active=True)
    
    created_jobs = []
    
    for company in companies:
        # Create ETL job for each company
        etl_job = ETLJob.objects.create(
            company=company,
            job_type=job_type,
            start_date=start_date,
            end_date=end_date
        )
        
        # Queue the job for background processing
        if settings.CLOUD_TASK_HOST:
            task_id = create_task(
                queue="etl-export",
                url=f"{settings.CLOUD_TASK_HOST}/api/etl/process-job/",
                payload={"etl_job_id": etl_job.id}
            )
        
        created_jobs.append({
            'company_id': company.id,
            'company_name': company.name,
            'job_id': etl_job.uuid,
            'status': etl_job.status
        })
    
    return JsonResponse({
        'status': 'success',
        'message': f'Created {len(created_jobs)} ETL jobs',
        'job_type': job_type,
        'start_date': start_date,
        'end_date': end_date,
        'jobs': created_jobs
    })
