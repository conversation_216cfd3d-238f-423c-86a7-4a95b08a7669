"""
ETL URL Configuration
"""

from django.urls import path
from . import views

urlpatterns = [
    # ETL job management
    path('export/', views.trigger_etl_export, name='trigger_etl_export'),
    path('jobs/', views.list_etl_jobs, name='list_etl_jobs'),
    path('jobs/<str:job_id>/', views.get_etl_job_status, name='get_etl_job_status'),
    
    # System endpoints
    path('trigger-all-companies/', views.trigger_etl_for_all_companies, name='trigger_etl_for_all_companies'),
    path('process-job/', views.process_etl_job, name='process_etl_job'),
]
