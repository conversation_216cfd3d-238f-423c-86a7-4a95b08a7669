"""
BigQuery table schemas for orders and order items
SINGLE SOURCE OF TRUTH for all BigQuery schema definitions
"""

from google.cloud import bigquery
from typing import List, Dict, Any
import json
import hashlib

# Schema version for tracking changes
SCHEMA_VERSION = "1.0.0"

class BigQuerySchemaManager:
    """Centralized schema management for BigQuery tables"""
    
    @staticmethod
    def get_orders_schema() -> List[bigquery.SchemaField]:
        """Get orders table schema - SINGLE SOURCE OF TRUTH"""
        return [
            # Primary identifiers
            bigquery.SchemaField("order_id", "INTEGER", mode="REQUIRED", description="Primary key from Django"),
            bigquery.SchemaField("uuid", "STRING", mode="REQUIRED", description="Unique order identifier"),
            bigquery.SchemaField("company_id", "INTEGER", mode="REQUIRED", description="Company foreign key"),
            
            # Order basic info
            bigquery.SchemaField("order_number", "STRING", mode="REQUIRED", description="Human readable order number"),
            bigquery.SchemaField("order_saleschannel", "STRING", mode="NULLABLE", description="Sales channel"),
            bigquery.SchemaField("order_customer", "STRING", mode="NULLABLE", description="Customer name"),
            bigquery.SchemaField("order_customerphone", "STRING", mode="NULLABLE", description="Customer phone"),
            
            # Logistics
            bigquery.SchemaField("order_trackingno", "STRING", mode="NULLABLE", description="Tracking number"),
            bigquery.SchemaField("order_warehousecode", "STRING", mode="NULLABLE", description="Warehouse code"),
            bigquery.SchemaField("order_shippingchannel", "STRING", mode="NULLABLE", description="Shipping channel"),
            
            # Order totals
            bigquery.SchemaField("order_total_quantity", "INTEGER", mode="NULLABLE", description="Total quantity of items"),
            bigquery.SchemaField("order_total_price", "NUMERIC", mode="NULLABLE", description="Total order price"),
            
            # Marketplace info
            bigquery.SchemaField("order_oms", "STRING", mode="NULLABLE", description="Order management system"),
            bigquery.SchemaField("order_marketplace", "STRING", mode="NULLABLE", description="Marketplace name"),
            bigquery.SchemaField("order_marketplaceshop", "STRING", mode="NULLABLE", description="Marketplace shop"),
            
            # Dates
            bigquery.SchemaField("order_date", "DATE", mode="REQUIRED", description="Order date"),
            
            # JSON data (flexible for future fields)
            bigquery.SchemaField("order_json", "JSON", mode="NULLABLE", description="Complete order JSON data"),
            bigquery.SchemaField("packing_json", "JSON", mode="NULLABLE", description="Packing information JSON"),
            
            # URLs
            bigquery.SchemaField("receipt_url", "STRING", mode="NULLABLE", description="Receipt URL"),
            bigquery.SchemaField("short_receipt_url", "STRING", mode="NULLABLE", description="Short receipt URL"),
            
            # Status
            bigquery.SchemaField("ready_to_ship", "BOOLEAN", mode="NULLABLE", description="Ready to ship status"),
            bigquery.SchemaField("ready_to_ship_timestamp", "TIMESTAMP", mode="NULLABLE", description="Ready to ship timestamp"),
            
            # Audit fields
            bigquery.SchemaField("create_date", "TIMESTAMP", mode="REQUIRED", description="Record creation timestamp"),
            bigquery.SchemaField("update_date", "TIMESTAMP", mode="NULLABLE", description="Last update timestamp"),
            bigquery.SchemaField("etl_processed_at", "TIMESTAMP", mode="REQUIRED", description="ETL processing timestamp"),
            bigquery.SchemaField("etl_schema_version", "STRING", mode="REQUIRED", description="Schema version used"),
            
            # Hash for deduplication
            bigquery.SchemaField("record_hash", "STRING", mode="REQUIRED", description="Hash of record for deduplication"),
        ]
    
    @staticmethod
    def get_order_items_schema() -> List[bigquery.SchemaField]:
        """Get order items table schema - SINGLE SOURCE OF TRUTH"""
        return [
            # Primary identifiers
            bigquery.SchemaField("order_item_id", "STRING", mode="REQUIRED", description="Generated UUID for order item"),
            bigquery.SchemaField("order_id", "INTEGER", mode="REQUIRED", description="Foreign key to orders table"),
            bigquery.SchemaField("order_uuid", "STRING", mode="REQUIRED", description="Order UUID reference"),
            bigquery.SchemaField("company_id", "INTEGER", mode="REQUIRED", description="Company foreign key"),
            bigquery.SchemaField("order_number", "STRING", mode="REQUIRED", description="Order number reference"),
            
            # Product info
            bigquery.SchemaField("product_id", "INTEGER", mode="NULLABLE", description="Product ID"),
            bigquery.SchemaField("sku", "STRING", mode="NULLABLE", description="Product SKU"),
            bigquery.SchemaField("name", "STRING", mode="NULLABLE", description="Product name"),
            bigquery.SchemaField("sku_type", "STRING", mode="NULLABLE", description="SKU type"),
            bigquery.SchemaField("product_type", "INTEGER", mode="NULLABLE", description="Product type ID"),
            
            # Quantity and pricing
            bigquery.SchemaField("quantity", "INTEGER", mode="NULLABLE", description="Item quantity"),
            bigquery.SchemaField("unit_text", "STRING", mode="NULLABLE", description="Unit description"),
            bigquery.SchemaField("price_per_unit", "NUMERIC", mode="NULLABLE", description="Price per unit"),
            bigquery.SchemaField("discount", "STRING", mode="NULLABLE", description="Discount description"),
            bigquery.SchemaField("discount_amount", "NUMERIC", mode="NULLABLE", description="Discount amount"),
            bigquery.SchemaField("total_price", "NUMERIC", mode="NULLABLE", description="Total item price"),
            
            # Serial numbers
            bigquery.SchemaField("serial_no_list", "JSON", mode="NULLABLE", description="List of serial numbers"),
            
            # Dates
            bigquery.SchemaField("order_date", "DATE", mode="REQUIRED", description="Order date"),
            
            # Audit fields
            bigquery.SchemaField("etl_processed_at", "TIMESTAMP", mode="REQUIRED", description="ETL processing timestamp"),
            bigquery.SchemaField("etl_schema_version", "STRING", mode="REQUIRED", description="Schema version used"),
            
            # Hash for deduplication
            bigquery.SchemaField("record_hash", "STRING", mode="REQUIRED", description="Hash of record for deduplication"),
        ]
    
    @staticmethod
    def get_schema_as_dict(table_name: str) -> Dict[str, Any]:
        """Get schema as dictionary for JSON serialization"""
        if table_name == "orders":
            schema = BigQuerySchemaManager.get_orders_schema()
        elif table_name == "order_items":
            schema = BigQuerySchemaManager.get_order_items_schema()
        else:
            raise ValueError(f"Unknown table: {table_name}")
        
        return {
            "version": SCHEMA_VERSION,
            "table": table_name,
            "fields": [
                {
                    "name": field.name,
                    "type": field.field_type,
                    "mode": field.mode,
                    "description": field.description or ""
                }
                for field in schema
            ]
        }
    
    @staticmethod
    def export_schema_to_file(table_name: str, file_path: str):
        """Export schema to JSON file for Cloud Function"""
        schema_dict = BigQuerySchemaManager.get_schema_as_dict(table_name)
        with open(file_path, 'w') as f:
            json.dump(schema_dict, f, indent=2)
    
    @staticmethod
    def calculate_record_hash(record_data: Dict[str, Any]) -> str:
        """Calculate hash for record deduplication"""
        # Remove ETL-specific fields from hash calculation
        hash_data = {k: v for k, v in record_data.items() 
                    if not k.startswith('etl_') and k != 'record_hash'}
        
        # Sort keys for consistent hashing
        sorted_data = json.dumps(hash_data, sort_keys=True, default=str)
        return hashlib.sha256(sorted_data.encode()).hexdigest()

# Backward compatibility - keep these for existing code
ORDERS_SCHEMA = BigQuerySchemaManager.get_orders_schema()
ORDER_ITEMS_SCHEMA = BigQuerySchemaManager.get_order_items_schema()

def get_dataset_name(company_id: int) -> str:
    """Generate dataset name for a company"""
    return f"dobybot_company_{company_id}"

def get_orders_table_name() -> str:
    """Get orders table name"""
    return "orders"

def get_order_items_table_name() -> str:
    """Get order items table name"""
    return "order_items"
