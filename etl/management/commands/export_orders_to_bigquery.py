"""
Management command to export orders to BigQuery
Usage: python manage.py export_orders_to_bigquery --company-id 1 --start-date 2024-01-01 --end-date 2024-01-31
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils.dateparse import parse_date
from datetime import datetime, timedelta

from companies.models import Company
from etl.models import ETLJob
from etl.services import ETLService


class Command(BaseCommand):
    help = 'Export orders to BigQuery via CSV files in GCS'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            required=True,
            help='Company ID to export data for'
        )
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date (YYYY-MM-DD). Defaults to yesterday.'
        )
        parser.add_argument(
            '--end-date',
            type=str,
            help='End date (YYYY-MM-DD). Defaults to yesterday.'
        )
        parser.add_argument(
            '--job-type',
            type=str,
            choices=['orders_export', 'order_items_export', 'full_export'],
            default='full_export',
            help='Type of export job to run'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be exported without actually doing it'
        )

    def handle(self, *args, **options):
        company_id = options['company_id']
        job_type = options['job_type']
        dry_run = options['dry_run']
        
        # Validate company exists
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise CommandError(f'Company with ID {company_id} does not exist')
        
        # Parse dates
        if options['start_date']:
            start_date = parse_date(options['start_date'])
            if not start_date:
                raise CommandError('Invalid start date format. Use YYYY-MM-DD')
        else:
            # Default to yesterday
            start_date = (datetime.now() - timedelta(days=1)).date()
            
        if options['end_date']:
            end_date = parse_date(options['end_date'])
            if not end_date:
                raise CommandError('Invalid end date format. Use YYYY-MM-DD')
        else:
            # Default to yesterday
            end_date = (datetime.now() - timedelta(days=1)).date()
            
        if start_date > end_date:
            raise CommandError('Start date cannot be after end date')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Starting ETL job for company {company.name} ({company_id})'
            )
        )
        self.stdout.write(f'Date range: {start_date} to {end_date}')
        self.stdout.write(f'Job type: {job_type}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN - No actual export will be performed'))
            
            # Count orders that would be exported
            from picking.models import PickOrder
            orders_count = PickOrder.objects.filter(
                company_id=company_id,
                order_date__gte=start_date,
                order_date__lte=end_date
            ).count()
            
            self.stdout.write(f'Would export {orders_count} orders')
            return
        
        # Create ETL job
        etl_job = ETLJob.objects.create(
            company=company,
            job_type=job_type,
            start_date=start_date,
            end_date=end_date
        )
        
        self.stdout.write(f'Created ETL job: {etl_job.uuid}')
        
        # Run ETL job
        try:
            etl_service = ETLService()
            etl_service.run_etl_job(etl_job)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'ETL job completed successfully!'
                )
            )
            self.stdout.write(f'Orders exported: {etl_job.total_orders}')
            self.stdout.write(f'Order items exported: {etl_job.total_order_items}')
            
            if etl_job.orders_csv_path:
                self.stdout.write(f'Orders CSV: {etl_job.orders_csv_path}')
            if etl_job.order_items_csv_path:
                self.stdout.write(f'Order items CSV: {etl_job.order_items_csv_path}')
                
            self.stdout.write(f'BigQuery dataset: {etl_job.bigquery_dataset}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'ETL job failed: {str(e)}')
            )
            raise CommandError(f'ETL job failed: {str(e)}')
