"""
Management command for BigQuery schema operations
Usage: 
  python manage.py manage_bigquery_schema --export-schemas
  python manage.py manage_bigquery_schema --add-field orders new_field STRING NULLABLE "Description"
  python manage.py manage_bigquery_schema --validate-schema orders
"""

from django.core.management.base import BaseCommand, CommandError
from google.cloud import bigquery
from google.cloud.exceptions import NotFound

from etl.bigquery_schemas import BigQuerySchemaManager, SCHEMA_VERSION
from etl.services import BigQueryService


class Command(BaseCommand):
    help = 'Manage BigQuery schemas - add fields, validate, export'

    def add_arguments(self, parser):
        parser.add_argument(
            '--export-schemas',
            action='store_true',
            help='Export current schemas to JSON files'
        )
        parser.add_argument(
            '--add-field',
            nargs=5,
            metavar=('TABLE', 'FIELD_NAME', 'FIELD_TYPE', 'MODE', 'DESCRIPTION'),
            help='Add new field to table schema'
        )
        parser.add_argument(
            '--validate-schema',
            type=str,
            help='Validate schema for specific table'
        )
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID for schema operations'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without executing'
        )

    def handle(self, *args, **options):
        if options['export_schemas']:
            self.export_schemas()
        elif options['add_field']:
            self.add_field(*options['add_field'], options.get('company_id'), options.get('dry_run'))
        elif options['validate_schema']:
            self.validate_schema(options['validate_schema'], options.get('company_id'))
        else:
            self.stdout.write(self.style.ERROR('Please specify an action'))

    def export_schemas(self):
        """Export schemas to JSON files for Cloud Function"""
        self.stdout.write('Exporting BigQuery schemas...')
        
        # Export orders schema
        orders_file = 'cloud_functions/bigquery_loader/schemas/orders_schema.json'
        BigQuerySchemaManager.export_schema_to_file('orders', orders_file)
        self.stdout.write(f'✅ Exported orders schema to {orders_file}')
        
        # Export order_items schema
        items_file = 'cloud_functions/bigquery_loader/schemas/order_items_schema.json'
        BigQuerySchemaManager.export_schema_to_file('order_items', items_file)
        self.stdout.write(f'✅ Exported order_items schema to {items_file}')
        
        self.stdout.write(self.style.SUCCESS('Schema export completed!'))

    def add_field(self, table_name, field_name, field_type, mode, description, company_id=None, dry_run=False):
        """Add new field to BigQuery table"""
        if table_name not in ['orders', 'order_items']:
            raise CommandError(f'Invalid table name: {table_name}')
        
        if mode not in ['REQUIRED', 'NULLABLE', 'REPEATED']:
            raise CommandError(f'Invalid mode: {mode}')
        
        self.stdout.write(f'Adding field {field_name} to {table_name} table...')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN - No changes will be made'))
            self.stdout.write(f'Would add: {field_name} ({field_type}, {mode}) - {description}')
            return
        
        # Get current schema
        if table_name == 'orders':
            current_schema = BigQuerySchemaManager.get_orders_schema()
        else:
            current_schema = BigQuerySchemaManager.get_order_items_schema()
        
        # Check if field already exists
        existing_fields = [field.name for field in current_schema]
        if field_name in existing_fields:
            raise CommandError(f'Field {field_name} already exists in {table_name} schema')
        
        # Create new field
        new_field = bigquery.SchemaField(field_name, field_type, mode=mode, description=description)
        
        # Add to schema
        updated_schema = current_schema + [new_field]
        
        # Update BigQuery tables if company_id provided
        if company_id:
            self.update_bigquery_table_schema(table_name, updated_schema, company_id)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Field {field_name} added to {table_name} schema')
        )
        self.stdout.write(
            self.style.WARNING(
                f'⚠️  Remember to update BigQuerySchemaManager.get_{table_name}_schema() '
                f'in etl/bigquery_schemas.py'
            )
        )

    def update_bigquery_table_schema(self, table_name, new_schema, company_id):
        """Update BigQuery table schema"""
        try:
            client = bigquery.Client()
            dataset_name = f"dobybot_company_{company_id}"
            table_id = f"{client.project}.{dataset_name}.{table_name}"
            
            # Get existing table
            table = client.get_table(table_id)
            
            # Update schema
            table.schema = new_schema
            table = client.update_table(table, ["schema"])
            
            self.stdout.write(f'✅ Updated BigQuery table schema: {table_id}')
            
        except NotFound:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Table {table_id} not found - will be created with new schema')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to update BigQuery schema: {e}')
            )

    def validate_schema(self, table_name, company_id=None):
        """Validate schema consistency"""
        if table_name not in ['orders', 'order_items']:
            raise CommandError(f'Invalid table name: {table_name}')
        
        self.stdout.write(f'Validating {table_name} schema...')
        
        # Get current schema from code
        if table_name == 'orders':
            code_schema = BigQuerySchemaManager.get_orders_schema()
        else:
            code_schema = BigQuerySchemaManager.get_order_items_schema()
        
        self.stdout.write(f'✅ Code schema has {len(code_schema)} fields')
        
        # If company_id provided, compare with BigQuery
        if company_id:
            try:
                client = bigquery.Client()
                dataset_name = f"dobybot_company_{company_id}"
                table_id = f"{client.project}.{dataset_name}.{table_name}"
                
                table = client.get_table(table_id)
                bq_schema = table.schema
                
                self.stdout.write(f'✅ BigQuery schema has {len(bq_schema)} fields')
                
                # Compare schemas
                code_fields = {field.name: field for field in code_schema}
                bq_fields = {field.name: field for field in bq_schema}
                
                # Check for missing fields in BigQuery
                missing_in_bq = set(code_fields.keys()) - set(bq_fields.keys())
                if missing_in_bq:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Fields missing in BigQuery: {missing_in_bq}')
                    )
                
                # Check for extra fields in BigQuery
                extra_in_bq = set(bq_fields.keys()) - set(code_fields.keys())
                if extra_in_bq:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Extra fields in BigQuery: {extra_in_bq}')
                    )
                
                if not missing_in_bq and not extra_in_bq:
                    self.stdout.write(self.style.SUCCESS('✅ Schemas are in sync!'))
                
            except NotFound:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  BigQuery table {table_id} not found')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ Failed to validate BigQuery schema: {e}')
                )
        
        # Show current schema
        self.stdout.write(f'\nCurrent {table_name} schema:')
        for field in code_schema:
            self.stdout.write(f'  - {field.name} ({field.field_type}, {field.mode}): {field.description}')
        
        self.stdout.write(f'\nSchema version: {SCHEMA_VERSION}')
