"""
BigQuery table schemas for orders and order items
"""

from google.cloud import bigquery

# Orders table schema
ORDERS_SCHEMA = [
    bigquery.SchemaField("order_id", "INTEGER", mode="REQUIRED"),
    bigquery.SchemaField("uuid", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("company_id", "INTEGER", mode="REQUIRED"),
    bigquery.SchemaField("order_number", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("order_saleschannel", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_customer", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_customerphone", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_trackingno", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_warehousecode", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_shippingchannel", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_total_quantity", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("order_total_price", "NUMERIC", mode="NULLABLE"),
    bigquery.SchemaField("order_oms", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_marketplace", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_marketplaceshop", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_date", "DATE", mode="REQUIRED"),
    bigquery.SchemaField("order_json", "JSON", mode="NULLABLE"),
    bigquery.SchemaField("packing_json", "JSON", mode="NULLABLE"),
    bigquery.SchemaField("receipt_url", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("short_receipt_url", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("ready_to_ship", "BOOLEAN", mode="NULLABLE"),
    bigquery.SchemaField("ready_to_ship_timestamp", "TIMESTAMP", mode="NULLABLE"),
    bigquery.SchemaField("create_date", "TIMESTAMP", mode="REQUIRED"),
    bigquery.SchemaField("update_date", "TIMESTAMP", mode="NULLABLE"),
    bigquery.SchemaField("etl_processed_at", "TIMESTAMP", mode="REQUIRED"),
]

# Order Items table schema (extracted from order_json.list)
ORDER_ITEMS_SCHEMA = [
    bigquery.SchemaField("order_item_id", "STRING", mode="REQUIRED"),  # Generated UUID
    bigquery.SchemaField("order_id", "INTEGER", mode="REQUIRED"),  # FK to orders
    bigquery.SchemaField("order_uuid", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("company_id", "INTEGER", mode="REQUIRED"),
    bigquery.SchemaField("order_number", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("product_id", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("sku", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("name", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("quantity", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("unit_text", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("price_per_unit", "NUMERIC", mode="NULLABLE"),
    bigquery.SchemaField("discount", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("discount_amount", "NUMERIC", mode="NULLABLE"),
    bigquery.SchemaField("total_price", "NUMERIC", mode="NULLABLE"),
    bigquery.SchemaField("product_type", "INTEGER", mode="NULLABLE"),
    bigquery.SchemaField("serial_no_list", "JSON", mode="NULLABLE"),
    bigquery.SchemaField("sku_type", "STRING", mode="NULLABLE"),
    bigquery.SchemaField("order_date", "DATE", mode="REQUIRED"),
    bigquery.SchemaField("etl_processed_at", "TIMESTAMP", mode="REQUIRED"),
]

def get_dataset_name(company_id: int) -> str:
    """Generate dataset name for a company"""
    return f"dobybot_company_{company_id}"

def get_orders_table_name() -> str:
    """Get orders table name"""
    return "orders"

def get_order_items_table_name() -> str:
    """Get order items table name"""
    return "order_items"
