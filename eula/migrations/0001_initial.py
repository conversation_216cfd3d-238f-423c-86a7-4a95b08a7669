# Generated by Django 2.2.24 on 2022-06-03 06:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LicenseAgreement',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('text', models.TextField()),
                ('is_active', models.BooleanField(default=False)),
            ],
        ),
        migrations.AddConstraint(
            model_name='licenseagreement',
            constraint=models.UniqueConstraint(condition=models.Q(is_active=True), fields=('is_active',), name='license_agreement_active_one_at_a_time'),
        ),
    ]
