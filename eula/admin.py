from django.contrib import admin
from typing import Mapping, Optional
from django.contrib import admin
from dotenv import Any
from .models import LicenseAgreement
from django import forms
# Register your models here.


class LicenseAgreementAdminForm(forms.ModelForm):
    class Meta:
        model = LicenseAgreement
        fields = ['title', 'text', 'is_active']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['text'].help_text = 'ใส่ html ได้'

    def clean(self) -> Optional[Mapping[str, Any]]:
        result = super().clean()
        if self.cleaned_data.get('is_active'):
            LicenseAgreement.objects.update(is_active=False)
        return result


class LicenseAgreementAdmin(admin.ModelAdmin):
    search_fields = ['title', 'text']
    list_display = ['title', 'is_active']
    list_filter = ['is_active']
    form = LicenseAgreementAdminForm


admin.site.register(LicenseAgreement, LicenseAgreementAdmin)
