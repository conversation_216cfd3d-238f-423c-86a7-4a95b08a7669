from django.shortcuts import get_object_or_404
from dynamic_rest.viewsets import DynamicModelViewSet
from django.utils import timezone
from eula.models import LicenseAgreement
from eula.serializers import LicenseAgreementSerializer
from rest_framework.response import Response
from rest_framework.status import HTTP_405_METHOD_NOT_ALLOWED
from rest_framework.views import APIView
from rest_framework.status import HTTP_204_NO_CONTENT

from users.models import User


class AnnouncementViewSet(DynamicModelViewSet):
    serializer_class = LicenseAgreementSerializer

    def get_queryset(self):
        return LicenseAgreement.objects.filter(is_active=True)

    def create(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AcceptLicenseAgreementAPI(APIView):
    def post(self, request, agreement_id: int):
        agreement = get_object_or_404(LicenseAgreement, id=agreement_id, is_active=True)

        user: User = request.user
        user.accepted_eula = agreement
        user.accepted_eula_timestamp = timezone.now()
        user.save(update_fields=['accepted_eula', 'accepted_eula_timestamp'])

        return Response(status=HTTP_204_NO_CONTENT)
