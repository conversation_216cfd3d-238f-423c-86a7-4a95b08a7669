from django.db import models
from django.db.models.constraints import UniqueConstraint
from django.db.models import Q


class LicenseAgreement(models.Model):
    title = models.CharField(max_length=200)
    text = models.TextField()
    is_active = models.BooleanField(default=False)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=['is_active'],
                condition=Q(is_active=True),
                name='license_agreement_active_one_at_a_time')
        ]

    def __str__(self) -> str:
        return self.title
