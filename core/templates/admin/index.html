{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}{{ block.super }}<link rel="stylesheet" type="text/css" href="{% static "admin/css/dashboard.css" %}">{% endblock %}

{% block coltype %}colMS{% endblock %}

{% block bodyclass %}{{ block.super }} dashboard{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
<div id="content-main">

<div id="Admin Introduction" style="padding-bottom: 24px;">
    
</div>

<div class="app-companies module">
  <table>
    <caption>
      <a href="#/admin/companies/" class="section" title="Models in the Companies application">Companies</a>
    </caption>
    <tbody>
      <tr class="model-company">
        <th scope="row"><a href="/admin/companies/company/">Companies</a></th>
        <td><a href="/admin/companies/company/add/" class="addlink">Add</a></td>
        <td><a href="/admin/companies/company/" class="changelink">Change</a></td>
      </tr>
      <tr class="model-settingvalue">
        <th scope="row"><a href="/admin/wallets/wallet/">Wallets</a></th>
        <td><a href="/admin/wallets/wallet/add/" class="addlink">Add</a></td>
        <td><a href="/admin/wallets/wallet/" class="changelink">Change</a></td>
        
        <!-- <th scope="row"><a href="/admin/companies/settingvalue/">Setting values</a></th>
        <td><a href="/admin/companies/settingvalue/add/" class="addlink">Add</a></td>
        <td><a href="/admin/companies/settingvalue/" class="changelink">Change</a></td> -->
      </tr>
    </tbody>
  </table>
</div>

<div class="app-auth module">
  <table>
    <caption>
      <a href="#/admin/auth/" class="section" title="Models in the Authentication and Authorization application">Authentication and Authorization</a>
    </caption>
    <tbody>
      <tr class="model-user">
        <th scope="row"><a href="/admin/users/user/">Users</a></th>
        <td><a href="/admin/users/user/add/" class="addlink">Add</a></td>
        <td><a href="/admin/users/user/" class="changelink">Change</a></td>
      </tr>
      <tr class="model-token">
        <th scope="row"><a href="/admin/authtoken/tokenproxy/">Tokens</a></th>
        <td><a href="/admin/authtoken/tokenproxy/add/" class="addlink">Add</a></td>
        <td><a href="/admin/authtoken/tokenproxy/" class="changelink">Change</a></td>
      </tr>
      <tr class="model-group">
        <th scope="row"><a href="/admin/auth/group/">Groups</a></th>
        <td><a href="/admin/auth/group/add/" class="addlink">Add</a></td>
        <td><a href="/admin/auth/group/" class="changelink">Change</a></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="app-reports module">
  <table>
    <caption>
      <a href="#/admin/reports/" class="section" title="Models in the Reports application">Reports</a>
    </caption>
    <tbody>
      <tr class="model-datastudioreport">
        <th scope="row"><a href="/admin/reports/datastudioreport/">Data studio reports</a></th>
        <td><a href="/admin/reports/datastudioreport/add/" class="addlink">Add</a></td>
        <td><a href="/admin/reports/datastudioreport/" class="changelink">Change</a></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="app-announcements module">
  <table>
    <caption>
      <a href="/admin/announcements/" class="section" title="Models in the Announcements application">Announcements</a>
    </caption>
    <tbody>
      <tr class="model-announcement">
        <th scope="row"><a href="/admin/announcements/announcement/">Announcements</a></th>
        <td><a href="/admin/announcements/announcement/add/" class="addlink">Add</a></td>
        <td><a href="/admin/announcements/announcement/" class="changelink">Change</a></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="app-eula module">
  <table>
    <caption>
      <a href="/admin/eula/" class="section" title="Models in the EULA application">EULA</a>
    </caption>
    <tbody>
      <tr class="model-announcement">
        <th scope="row"><a href="/admin/eula/licenseagreement/">License Agreement</a></th>
        <td><a href="/admin/eula/licenseagreement/add/" class="addlink">Add</a></td>
        <td><a href="/admin/eula/licenseagreement/" class="changelink">Change</a></td>
      </tr>
    </tbody>
  </table>
</div>

<div class="app-sms module">
  <table>
    <caption>
        <a href="/admin/sms/" class="section" title="Models in the Sms application">Sms</a>
    </caption>
      <tbody><tr class="model-smsblock">
        <th scope="row"><a href="/admin/sms/smsblock/">Sms blocks</a></th>
        <td><a href="/admin/sms/smsblock/add/" class="addlink">Add</a></td>
        <td><a href="/admin/sms/smsblock/" class="changelink">Change</a></td>
      </tr>
    </tbody>
  </table>
</div>

<div style="text-align: center; padding-bottom: 24px;">
  <button onclick="document.querySelector('#original-django-admin').style.display = 'block'">show original</button>
  <button onclick="document.querySelector('#original-django-admin').style.display = 'none'">hide original</button>
</div>
<div id="original-django-admin" style="display: none;">
{% if app_list %}
    {% for app in app_list %}
        <div class="app-{{ app.app_label }} module">
        <table>
        <caption>
            <a href="{{ app.app_url }}" class="section" title="{% blocktrans with name=app.name %}Models in the {{ name }} application{% endblocktrans %}">{{ app.name }}</a>
        </caption>
        {% for model in app.models %}
            <tr class="model-{{ model.object_name|lower }}">
            {% if model.admin_url %}
                <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
            {% else %}
                <th scope="row">{{ model.name }}</th>
            {% endif %}

            {% if model.add_url %}
                <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
            {% else %}
                <td>&nbsp;</td>
            {% endif %}

            {% if model.admin_url %}
                {% if model.view_only %}
                <td><a href="{{ model.admin_url }}" class="viewlink">{% trans 'View' %}</a></td>
                {% else %}
                <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                {% endif %}
            {% else %}
                <td>&nbsp;</td>
            {% endif %}
            </tr>
        {% endfor %}
        </table>
        </div>
    {% endfor %}
{% else %}
    <p>{% trans "You don't have permission to view or edit anything." %}</p>
{% endif %}
</div>
{% endblock %}
</div>

{% block sidebar %}
<!-- <div id="content-related">
    <div class="module" id="recent-actions-module">
        <h2>{% trans 'Recent actions' %}</h2>
        <h3>{% trans 'My actions' %}</h3>
            {% load log %}
            {% get_admin_log 10 as admin_log for_user user %}
            {% if not admin_log %}
            <p>{% trans 'None available' %}</p>
            {% else %}
            <ul class="actionlist">
            {% for entry in admin_log %}
            <li class="{% if entry.is_addition %}addlink{% endif %}{% if entry.is_change %}changelink{% endif %}{% if entry.is_deletion %}deletelink{% endif %}">
                {% if entry.is_deletion or not entry.get_admin_url %}
                    {{ entry.object_repr }}
                {% else %}
                    <a href="{{ entry.get_admin_url }}">{{ entry.object_repr }}</a>
                {% endif %}
                <br>
                {% if entry.content_type %}
                    <span class="mini quiet">{% filter capfirst %}{{ entry.content_type }}{% endfilter %}</span>
                {% else %}
                    <span class="mini quiet">{% trans 'Unknown content' %}</span>
                {% endif %}
            </li>
            {% endfor %}
            </ul>
            {% endif %}
    </div>
</div> -->
{% endblock %}