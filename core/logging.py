import json
import requests
from typing import Union
from google.cloud import logging
from django.conf import settings

gcp_logging_client = logging.Client()


def logger(name: str):
    return gcp_logging_client.logger(name)


def log_req_res(log_name: str, ref: dict, response: requests.Response):
    ref = {f"$ref_{k}": v for k, v in ref.items()}
    res = response
    req = response.request

    logger(log_name).log_struct(
        {
            **ref,
            "req": {
                "method": req.method,
                "url": req.url,
                "headers": masked_dict(req.headers),
                "data": parse_body(req.body),
            },
            "res": {"status": res.status_code, "data": parse_body(res.text)},
        }
    )


def log_incoming_req(log_name: str, ref: dict, request: requests.Request):
    ref = {f"$ref_{k}": v for k, v in ref.items()}
    req = request

    logger(log_name).log_struct(
        {
            **ref,
            "req": {
                "method": req.method,
                "headers": masked_dict(req.headers),
                "data": parse_body(req.data),
            },
            "host": settings.DEFAULT_HOST,
        }
    )


def parse_body(body: Union[str, bytes]):
    try:
        return json.loads(body)
    except Exception:
        try:
            return body.decode("utf-8")
        except AttributeError:
            return body


def masked_dict(headers: dict):
    masked_headers = {}
    for key in headers:
        masked_headers[key] = headers[key]

        if key.lower() in ["authorization", "secret"]:
            masked_headers[key] = "***"

    return masked_headers
