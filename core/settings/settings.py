"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 3.2.8.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

import os
import io
import json
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from distutils.util import strtobool

import environ
import google.auth
from google.cloud import secretmanager

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Versions
DOBYBOT_VERSION = "25.3"

# Load Environments
env = environ.Env()
env_file = os.path.join(BASE_DIR, ".env")


# Attempt to load the Project ID into the environment, safely failing on error.
try:
    _, os.environ["GOOGLE_CLOUD_PROJECT"] = google.auth.default()
except Exception:
    pass

if os.path.isfile(env_file):
    # Use a local secret file, if provided

    env.read_env(env_file, overwrite=True)
elif os.environ.get("GOOGLE_CLOUD_PROJECT", None):
    # Pull secrets from Google Secret Manager

    project_id = os.environ.get("GOOGLE_CLOUD_PROJECT")
    client = secretmanager.SecretManagerServiceClient()
    settings_name = os.environ.get("SETTINGS_NAME", "dobybot_settings")
    name = f"projects/{project_id}/secrets/{settings_name}/versions/latest"
    payload = client.access_secret_version(name=name).payload.data.decode("UTF-8")
    env.read_env(io.StringIO(payload))
else:
    raise Exception("No local .env or GOOGLE_CLOUD_PROJECT detected. No secrets found.")


# Host
DEFAULT_HOST = env("HOST", default="https://api.dobybot.com")
UI_HOST = env("UI_HOST", default="https://cloud.dobybot.com")


# SECURITY
SECRET_KEY = env("SECRET_KEY")
RECEIPT_SIGINING_KEY = env("RECEIPT_SIGINING_KEY")
DEBUG = strtobool(os.environ.get("DEBUG", "True"))
DEBUG_REPORT = strtobool(os.environ.get("DEBUG_REPORT", "False"))
if not DEBUG:
    ALLOWED_HOSTS = env.list("ALLOWED_HOSTS")
    X_FRAME_OPTIONS = "SAMEORIGIN"
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
else:
    ALLOWED_HOSTS = ["*"]

# Content-Security-Policy
CSP_DEFAULT_SRC = ["'self'", "'unsafe-inline'", "*.googleapis.com", "fonts.gstatic.com"]
CSP_WORKER_SRC = ["'self'", "blob:"]
CSP_IMG_SRC = ["*", "'self'", "blob:", "data:"]


# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    "django_cleanup.apps.CleanupConfig",
    "rest_framework",
    "rest_framework.authtoken",
    "dynamic_rest",
    "corsheaders",
    "django_user_agents",
    "drf_spectacular",
    "drf_spectacular_sidecar",
    "django_ez_report",
    "axes",
    "simple_history",
    "users",
    "companies",
    "picking",
    "printing",
    "logger",
    "reports",
    "sms",
    "fastnotes",
    "fixcases",
    "cloudtasks",
    "importdata",
    "wallets",
    "announcements",
    "drivemanager",
    "eula",
    "datamasking",
    "thaiaddress",
    "stats",
    "fileupload",
    "etax",
    "openapi",
    "report_v2",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "csp.middleware.CSPMiddleware",
    "axes.middleware.AxesMiddleware",
    "simple_history.middleware.HistoryRequestMiddleware",
    "django.middleware.locale.LocaleMiddleware",
]

ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "core/templates/")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"


# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases
DATABASES = {
    "default": env.db(),
}
DATABASES["cypress"] = {
    **DATABASES["default"],
    "NAME": DATABASES["default"]["NAME"] + "_cypress",
}

# If the flag as been set, configure to use proxy
if os.getenv("USE_CLOUD_SQL_AUTH_PROXY", None):
    DATABASES["default"]["HOST"] = "127.0.0.1"
    DATABASES["default"]["PORT"] = 5432


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTH_USER_MODEL = "users.User"


# Django Axes
# https://django-axes.readthedocs.io/en/latest/2_installation.html

AUTHENTICATION_BACKENDS = [
    "axes.backends.AxesStandaloneBackend",
    "django.contrib.auth.backends.ModelBackend",
]

AXES_CLIENT_IP_CALLABLE = "utils.get_client_ip"

AXES_FAILURE_LIMIT = 5

AXES_COOLOFF_TIME = timedelta(minutes=5)

AXES_LOCKOUT_PARAMETERS = [["ip_address", "username"]]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

LOCALE_PATHS = (os.path.join(BASE_DIR, "locale"),)

TIME_ZONE = os.getenv("TIMEZONE", "UTC")

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATICFILES_DIRS = [
    BASE_DIR / "core/static",
]

STATIC_URL = "/static/"

STATIC_ROOT = os.path.join(BASE_DIR, "static/")

MEDIA_URL = "/media/"

MEDIA_ROOT = os.path.join(BASE_DIR, "media/")


# Google Cloud Storage
# https://django-storages.readthedocs.io/en/latest/backends/gcloud.html

# secure storage
GS_SECURE_BUCKET_NAME = env("GS_SECURE_BUCKET_NAME")
GS_SECURE_EXPIRATION = timedelta(hours=6)
DEFAULT_FILE_STORAGE = "core.storages.GoogleCloudSecureStorage"

# public storage
GS_PUBLIC_BUCKET_NAME = env("GS_PUBLIC_BUCKET_NAME")
STATICFILES_STORAGE = "core.storages.GoogleCloudPublicStorage"

# Google Cloud Tasks
CLOUD_TASK_HOST = env("CLOUD_TASK_HOST")
CLOUD_TASK_AUTH_TOKEN = env("CLOUD_TASK_AUTH_TOKEN")


# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


# Django REST Framework
# https://www.django-rest-framework.org/api-guide/settings/

REST_FRAMEWORK = {
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        # "dynamic_rest.renderers.DynamicBrowsableAPIRenderer",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "core.authentication.DobybotJWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
        # "rest_framework_simplejwt.authentication.JWTAuthentication",
        # "rest_framework.authentication.BasicAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "sms": "20/second",
        "input_task": "10/second",
        "output_task": "20/second",
        "openapi": "60/min",
    },
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}


# Django REST Framework Simple JWT
# https://django-rest-framework-simplejwt.readthedocs.io/en/latest/settings.html

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=30),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=3),
    "AUTH_HEADER_TYPES": ("Bearer",),
}


# Django CORS Header
# https://pypi.org/project/django-cors-headers/

if DEBUG:
    CORS_ALLOW_ALL_ORIGINS = True
else:
    CORS_ALLOWED_ORIGINS = env.list("CORS_ALLOWED_ORIGINS")


# ANTS SMS
# https://www.ants.co.th/

ANTS_TOKEN = env("ANTS_TOKEN")
ANTS_NOTIFY_URL = env("ANTS_NOTIFY_URL")

# JERA SMS Service
JERA_SMS_TOKEN = env("JERA_SMS_TOKEN")
JERA_SMS_NOTIFY_URL = env("JERA_SMS_NOTIFY_URL")

CUTTLY_API_KEY = env("CUTTLY_API_KEY")

# PrintNode
# https://www.printnode.com/en/docs/api/curl

# Username & Password for printnode client to download pdf from our system
PRINT_ACCOUNT_USERNAME = env("PRINT_ACCOUNT_USERNAME", default="printeronly")
PRINT_ACCOUNT_PASSWORD = env("PRINT_ACCOUNT_PASSWORD", default="pw123789")

if env("SENTRY_ENABLE", bool, default=False):
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration

    sentry_sdk.init(
        dsn=env("SENTRY_DSN"),
        integrations=[DjangoIntegration()],
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=0.01,
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=0.01,
    )


# Mailjet
# https://app.mailjet.com/dashboard
# https://app.mailjet.com/account/setup-guide
# MAILJET_API_KEY = env("MAILJET_API_KEY")
# MAILJET_API_SECRET = env("MAILJET_API_SECRET")

# Email (Taxi Mail)
TAXIMAIL_APITOKEN = env("TAXIMAIL_APITOKEN")


# Google OAuth2
GOOGLE_OAUTH2_CLIENT_ID = env("GOOGLE_OAUTH2_CLIENT_ID", default="")
GOOGLE_OAUTH2_CLIENT_SECRET = env("GOOGLE_OAUTH2_CLIENT_SECRET", default="")

# Google Drive
try:
    GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS = env(
        "GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS"
    )
    GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS = json.loads(
        GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS, strict=False
    )
    PRIVATE_KEY = env("GOOGLE_DRIVE_SERVICE_ACCOUNT_PRIVATE_KEY", default="")
    if PRIVATE_KEY:
        PRIVATE_KEY = PRIVATE_KEY.replace(":", "\n")
        GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS["private_key"] = PRIVATE_KEY
except:
    GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS = {}


# Override SMS phone number for testing
OVERRIDE_PHONE_NUMBER = env("OVERRIDE_PHONE_NUMBER", default=None)


# MQTT
MQTT_SERVER = env("MQTT_SERVER")
MQTT_PORT = env.int("MQTT_PORT")
MQTT_USERNAME = env("MQTT_USERNAME")
MQTT_PASSWORD = env("MQTT_PASSWORD")


# Dobybot Connect
DOBYBOT_CONNECT_HOST = env("DOBYBOT_CONNECT_HOST", default=None)
DOBYBOT_CONNECT_APIKEY = env("DOBYBOT_CONNECT_APIKEY", default=None)
DOBYSYNC_HOST = env("DOBYSYNC_HOST", default=None)
DOBYSYNC_INTEGRATOR_APIKEY = env("DOBYSYNC_INTEGRATOR_APIKEY", default=None)


DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

# Ants Line Official Account
# https://lon.ants.co.th/#/template
ANTS_LON_TEMPLATES = {"UPDATE_ORDER_STATUS": "d50413a4-b05c-4994-93b6-7ce3d7a0ce5b"}
# ANTS_LON_TEMPLATES = {
#     'UPDATE_ORDER_STATUS': '674f6e47-ab6c-41b2-8e15-bf3748db1888'
# }
ANTS_LON_API_URL = env("ANTS_LON_API_URL")
ANTS_LON_AUTH_TOKEN = env("ANTS_LON_AUTH_TOKEN")
ANTS_LON_NOTIFY_URL = env("ANTS_LON_NOTIFY_URL")
ANTS_LON_NOTIFY_CALLBACK_DATA = env("ANTS_LON_NOTIFY_CALLBACK_DATA")

CYPRESS_TOKEN = None

# XSelly
XSELLY_EXPRESS_BASE_URL = env("XSELLY_EXPRESS_BASE_URL")
XSELLY_SECRET = env("XSELLY_SECRET")

# Dobybot ML
DOBYBOT_ML_BASE_URL = env("DOBYBOT_ML_BASE_URL")
DOBYBOT_ML_API_KEY = env("DOBYBOT_ML_API_KEY")

# DRF Spectacular
# https://drf-spectacular.readthedocs.io/en/latest/settings.html
SPECTACULAR_SETTINGS = {
    "TITLE": "Dobybot OpenAPI",
    "DESCRIPTION": "",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    # OTHER SETTINGS
    "PREPROCESSING_HOOKS": ["openapi.spectarcular_settings.preprocessing_filter_spec"],
    "REDOC_DIST": "SIDECAR",
    "TAGS": [
        {"name": "E-Tax", "description": "E-Tax Document Management"},
    ],
}

# This will show all db queries in the console, only when DEBUG = True
# LOGGING = {
#     'version': 1,
#     'filters': {
#         'require_debug_true': {
#             '()': 'django.utils.log.RequireDebugTrue',
#         }
#     },
#     'handlers': {
#         'console': {
#             'level': 'DEBUG',
#             'filters': ['require_debug_true'],
#             'class': 'logging.StreamHandler',
#         }
#     },
#     'loggers': {
#         'django.db.backends': {
#             'level': 'DEBUG',
#             'handlers': ['console'],
#         }
#     }
# }
if DEBUG:
    print("-" * 80)
    print(" " * (35) + "Settings")
    print("-" * 80)
    print("DEBUG   :", bool(DEBUG))
    print(
        "DATABASE:",
        DATABASES["default"]["HOST"]
        + "/"
        + DATABASES["default"]["NAME"]
        + ":"
        + str(DATABASES["default"]["PORT"] or "5432"),
    )
    print("-" * 80)


# Logging
log_level = os.getenv("LOG_LEVEL")
if log_level:
    import logging

    class ModuleFilter(logging.Filter):
        def __init__(self, module_names: list[str]):
            super().__init__()
            self.module_names = module_names

        def filter(self, record: logging.LogRecord) -> bool:
            for module_name in self.module_names:
                if record.name.startswith(module_name):
                    return False
            return True

    log_level = getattr(logging, log_level)
    handler = logging.StreamHandler()
    handler.setLevel(log_level)
    handler.addFilter(ModuleFilter(["google.auth", "urllib3"]))

    logging.basicConfig(level=log_level, handlers=[handler])
