from typing import Literal
from rest_framework.response import Response
from rest_framework import status

from django.utils.translation import gettext_lazy as _

ERROR_CODE = Literal[
    "ORDER_NOT_FOUND",
    "PRODUCT_NOT_FOUND",
    "DOBYBOT_CONNECT_NOT_SUPPORT_SYNC_PRODUCTS",
    "TAX_DOCUMENT_ALREADY_EXISTS",
    "TAX_DOCUMENT_NO_LONGER_EDITABLE",
    "TAX_ID_NOT_FOUND",
    "TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE",
    "COMPANY_DOES_NOT_EXISTS",
    "ORDER_DOES_NOT_EXISTS",
    "TAX_DOCUMENT_NOT_FOUND",
    "ORDER_IS_NOT_RECEIVED",
    "TAX_DOCUMENT_ALREADY_CREATED",
    "ORDER_IS_NO_LONGER_ELIGIBLE_FOR_ETAX_REQUEST",
    "INVALID_ETAX_RETRIEVAL_DAY_CONFIGURATION",
    "EDITING_ETAX_NOT_ALLOWED_DUE_TO_OVERDUE_DATE",
    "CREDIT_NOTE_HAS_ALREADY_BEEN_CANCELLED",
    "ORDER_IS_VOIDED",
]
ERROR_DICT = {
    "ORDER_NOT_FOUND": _("Order not found"),
    "PRODUCT_NOT_FOUND": _("Product not found"),
    "DOBYBOT_CONNECT_NOT_SUPPORT_SYNC_PRODUCTS": _(
        "Dobybot Connect version 1 not support syncing products."
    ),
    "TAX_DOCUMENT_ALREADY_EXISTS": _("Tax document already exists"),
    "TAX_DOCUMENT_NO_LONGER_EDITABLE": _("Tax document no longer to edit"),
    "TAX_ID_NOT_FOUND": _("Tax ID not found"),
    "TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE": _(
        "Tax document got an error while operate"
    ),
    "COMPANY_DOES_NOT_EXISTS": "Company does not exists",
    "ORDER_DOES_NOT_EXISTS": "Order does not exists",
    "TAX_DOCUMENT_NOT_FOUND": "Tax document not found",
    "ORDER_IS_NOT_RECEIVED": "Order is not received",
    "TAX_DOCUMENT_ALREADY_CREATED": "Tax document has already been created",
    "ORDER_IS_NO_LONGER_ELIGIBLE_FOR_ETAX_REQUEST": _(
        "Order is no longer eligible for etax request"
    ),
    "INVALID_ETAX_RETRIEVAL_DAY_CONFIGURATION": "Invalid e-Tax retrieval day configuration",
    "EDITING_ETAX_NOT_ALLOWED_DUE_TO_OVERDUE_DATE": "Editing e-Tax not allowed due to overdue date",
    "CREDIT_NOTE_HAS_ALREADY_BEEN_CANCELLED": "Credit note has already been cancelled",
    "ORDER_IS_VOIDED": _("Order is voided"),
    "INVALID_TAX_DOCUMENT_STATUS": _("Invalid tax document status"),
}


class ResponseError(Response):

    def __init__(
        self,
        code: ERROR_CODE,
        message: str = None,
        status=status.HTTP_400_BAD_REQUEST,
    ):
        if not message:
            message = ERROR_DICT.get(code)

        super().__init__(
            data={"non_field_errors": [{"code": code, "message": message}]},
            status=status,
        )
