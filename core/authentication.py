from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken, TokenError
from django.utils.translation import gettext_lazy as _


class DobybotJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        user = super().get_user(validated_token)
        if not user:
            return None
        

        device_id: str = validated_token.payload.get('device_id')
        if not device_id:
            raise AuthenticationFailed(
                _('Token has no device_id'),
                code='token_has_no_device_id'
            )

        is_desktop_app = device_id.startswith('DD-')

        # ========================================
        # Web app
        # ========================================
        if not is_desktop_app:
            web_devices = list(user.devices.keys())
            web_devices = [d for d in web_devices if not d.startswith('DD-')]
            if device_id not in web_devices:
                raise AuthenticationFailed(
                    _('Unrecornized device id, Your divice is logged out'),
                    code='unrecornized_device_id'
                )
        
        # ========================================
        # Desktop app
        # ========================================
        if is_desktop_app:
            dsk_devices = list(user.devices.keys())
            dsk_devices = [d for d in dsk_devices if d.startswith('DD-')]
            if device_id not in dsk_devices:
                raise AuthenticationFailed(
                    _('Unrecornized device id, Your divice is logged out'),
                    code='unrecornized_device_id'
                )

        return user
