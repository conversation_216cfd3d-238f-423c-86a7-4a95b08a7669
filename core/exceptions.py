class ExternalServiceAPIException(Exception):
    def __init__(self, response, *args: object) -> None:
        self.response = response
        super().__init__(*args)

    def __str__(self) -> str:
        if self.response.headers.get('content-type') == 'application/json':
            text = self.response.json()
        else:
            text = self.response.text
        return f'[{self.response.status_code}] {text}'


class PatternMismatchException(Exception):
    pass
