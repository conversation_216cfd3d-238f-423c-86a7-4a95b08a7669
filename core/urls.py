"""core URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.conf import settings
from django.contrib import admin
from django.urls import path
from django.urls.conf import include
from django.conf.urls.static import static
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.utils import timezone


admin.site.site_header = ""
admin.site.site_title = "Dobybot Admin Portal"
admin.site.index_title = "Welcome to Dobybot Admin Portal"

urlpatterns = [
    path("admin/", admin.site.urls),
    path("printing/", include("printing.urls")),
    path("api/adminapi/", include("adminapi.urls")),
    path("api/printing/", include("printing.api.urls")),
    path("api/users/", include("users.urls")),
    path("api/companies/", include("companies.urls")),
    path("api/picking/", include("picking.urls")),
    path("api/reports/", include("reports.urls")),
    path("api/sms/", include("sms.urls")),
    path("api/fastnotes/", include("fastnotes.urls")),
    path("api/fixcases/", include("fixcases.urls")),
    path("api/importdata/", include("importdata.urls")),
    path("api/logging/", include("logger.urls")),
    path("api/wallets/", include("wallets.urls")),
    path("api/announcements/", include("announcements.urls")),
    path("api/drivemanager/", include("drivemanager.urls")),
    path("api/eula/", include("eula.urls")),
    path("api/datamasking/", include("datamasking.urls")),
    path("api/debugtools/", include("debugtools.urls")),
    path("api/mockserver/", include("mockserver.urls")),
    path("api/thaiaddress/", include("thaiaddress.urls")),
    path("api/shipping/", include("shipping.urls")),
    path("api/stats/", include("stats.urls")),
    path("api/fileupload/", include("fileupload.urls")),
    # APIs for other companies
    path("api/v1/fixcases/", include("fixcases.api.v1.urls")),
    path("api/cypress/", include("cypress.urls")),
    path("api/report-v2/", include("report_v2.urls")),
    path("api/etax/", include("etax.urls")),
    path("api/etl/", include("etl.urls")),
    path("openapi/", include("openapi.urls")),
]


@api_view(["GET"])
@permission_classes([AllowAny])
def versions(request):
    """For frontend (dobybot-ui) to warm up cloud run instance on page load"""
    return Response(
        {
            "dobybot_version": settings.DOBYBOT_VERSION,
        }
    )


@api_view(["GET"])
@permission_classes([AllowAny])
def trigger_error(request):
    division_by_zero = 1 / 0


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def echo(request):
    # from pprint import pprint
    # pprint({
    #     'params': request.GET,
    #     'headers': request.headers,
    #     'data': request.data
    # })
    return Response({"headers": request.headers, "data": request.data})


@api_view(["GET"])
@permission_classes([AllowAny])
def curlmyip(request):
    import requests

    res = requests.get("https://curlmyip.org")
    return Response({"ip": res.text})


@api_view(["GET"])
@permission_classes([AllowAny])
def empty(request):
    return Response()


@api_view(["GET"])
@permission_classes([AllowAny])
def headers(request):
    return Response(request.headers)


@api_view(["GET"])
@permission_classes([AllowAny])
def server_time(request):
    return Response(timezone.localtime().isoformat())


# def clientip(request):
#     return HttpResponse(
#         f'client ip: {get_client_ip(request)}' +
#         '<br><br>' + str(request.headers) +
#         '<br><br>' + str(request.META)
#     )


urlpatterns += [
    path("", empty),
    path("versions/", versions),
    path("time/", server_time),
    path("sentry-debug/", trigger_error),
    path("echo/", echo),
    path("curlmyip/", curlmyip),
    path("headers/", headers),
    # path("client-ip/", clientip),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
