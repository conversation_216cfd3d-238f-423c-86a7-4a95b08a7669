from rest_framework import serializers


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == "":
            data = self.default

        if type(data) is str:
            data = data.replace(",", "")

        return super().to_internal_value(data)


class BlankableDateField(serializers.DateField):
    def to_internal_value(self, data):
        if not data:
            return self.default

        return super().to_internal_value(data)


class BlankableBooleanField(serializers.CharField):
    def to_internal_value(self, data):
        if data == "":
            return False

        if data.lower() in ["y", "yes", "t", "true", "1"]:
            return True

        return False
