from rest_framework import serializers
from typing import Union
from django.utils import timezone
from datetime import date, datetime

from utils.valid_str import clean_str


class StartDateField(serializers.DateField):
    def to_internal_value(self, value):
        value = super().to_internal_value(value)
        return timezone.make_aware(datetime.combine(value, datetime.min.time()))


class EndDateField(serializers.DateField):
    def to_internal_value(self, value):
        value = super().to_internal_value(value)
        return timezone.make_aware(datetime.combine(value, datetime.max.time()))


def parse_date(datestr: str, input_formats: list[str] = None):
    if not input_formats:
        input_formats = ["%Y-%m-%d", "%d/%m/%Y"]

    for fmt in input_formats:
        try:
            return timezone.make_aware(datetime.strptime(datestr, fmt))
        except ValueError:
            continue

    raise ValueError(f"Invalid date string: {datestr}, valid formats: {input_formats}")


def start_of(dt: Union[datetime, date]):
    d = get_date(dt)
    return timezone.make_aware(datetime.combine(d, datetime.min.time()))


def end_of(dt: Union[datetime, date]):
    d = get_date(dt)
    return timezone.make_aware(datetime.combine(d, datetime.max.time()))


def get_date(dt: Union[datetime, date]):
    if isinstance(dt, str):
        d = parse_date(dt)
    elif isinstance(dt, datetime):
        d = dt.date()
    elif isinstance(dt, date):
        d = dt
    else:
        raise ValueError("Invalid type")

    return d


class KCharField(serializers.CharField):
    def to_internal_value(self, data):
        """
        return only keyboard characters
        """
        data = super().to_internal_value(data)
        return clean_str(data.strip())
