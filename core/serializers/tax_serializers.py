from rest_framework import serializers
import re


class ThaiTaxIDField(serializers.CharField):
    def __init__(self, **kwargs):
        kwargs["max_length"] = 13  # Thai tax IDs are 13 digits long
        kwargs["min_length"] = 13
        super().__init__(**kwargs)

    def validate(self, value):
        value = super().validate(value)
        if not re.match(r"^\d{13}$", value):
            raise serializers.ValidationError("Thai Tax ID must be exactly 13 digits.")

        return value


class BranchNoField(serializers.CharField):
    def __init__(self, **kwargs):
        kwargs["max_length"] = 5  # Branch numbers are typically 5 digits
        kwargs["min_length"] = 5
        super().__init__(**kwargs)

    def validate(self, value):
        value = super().validate(value)
        if not re.match(r"^\d{5}$", value):
            raise serializers.ValidationError("Branch number must be exactly 5 digits.")
        return value


class PostCodeField(serializers.CharField):
    def __init__(self, **kwargs):
        kwargs["max_length"] = 5  # Branch numbers are typically 5 digits
        kwargs["min_length"] = 5
        super().__init__(**kwargs)

    def validate(self, value):
        value = super().validate(value)
        if not re.match(r"^\d{5}$", value):
            raise serializers.ValidationError("Post code must be exactly 5 digits.")
        return value
