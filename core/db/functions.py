from django.db.models import <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Cast
from django.db.models.expressions import Func
from django.db.models.expressions import RawSQL


def jsonfield_increment(field_name: str, key: str, increment_by: int = 1) -> RawSQL:
    sql = f"""
        jsonb_set(
            {field_name},
            '{{{key}}}',
            (COALESCE({field_name}->>'{key}','0')::int + {increment_by})::text::jsonb
        )
    """

    return RawSQL(sql, [])
