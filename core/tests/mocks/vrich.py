import json
from core.tests.mocks.response import MockedResponse
from services.vrich.vrich import VRichService


class MockedVRichService(VRichService):
    def __init__(self, host: str, token: str) -> None:
        pass

    def ready_to_ship(self, *args, **kwargs):
        return MockedResponse(
            text=json.dumps(
                {"id": 200875588, "order_date": "28\/02\/2022", "facebook_name": "La<PERSON>a <PERSON>d",
                 "tracking_number": "VRIC255726639B", "tracking_notify": None,
                 "checkout_time": "2022-03-03T00: 19: 33.363801Z",
                 "checkout_time_text": "03\/ 03\/ 2022 07: 19: 33", "checkout_by": "dobybot"}),
            status_code=200
        )
