class MockGoogleDriveService:
    def __init__(self, *args, **kwargs):
        pass

    def get(self, *args, **kwargs):
        return {
            "id": "file_id_1234",
            "name": "file_name_test",
            "mimeType": "application/pdf",
            "createdTime": "2021-06-16T03:00:00.000Z",
            "size": 1234,
            "parents": ["folder_id_1234"],
        }

    def list(self, *args, **kwargs):
        # TODO: not sure
        return []

    def delete(self, *args, **kwargs):
        # TODO: not sure
        return None

    def create_folder(self, *args, **kwargs):
        return "folder_id_1234"

    def rename_file(self, *args, **kwargs):
        return {
            "id": "file_id_1234",
            "name": "file_name_test",
        }

    def get_or_create_folder(self, *args, **kwargs):
        return ("folder_id_1234", False)

    def upload_file(self, *args, **kwargs):
        return "file_id_1234"
