import json as jsonlib

class MockedResponse():
    def __init__(self, text='', status_code=200, json=None) -> None:
        self.status_code = status_code
        self.text = text or jsonlib.dumps(json)
        self._json = json
        self.headers = {
            'content-type': 'application/json'
        }

    def json(self):
        return self._json

    def __getitem__(self, item):
        return self._json[item]
