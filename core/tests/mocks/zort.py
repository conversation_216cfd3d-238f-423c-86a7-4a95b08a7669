import dacite
import json
from services.zort.zort import ZortService
from services.zort.zort_types import ZortProduct


class MockedZortService(ZortService):
    def __init__(self, storename, apikey, apisecret):
        pass

    def get_orders(self, *args, **kwargs):
        orders = []
        with open('services/zort/example-response/get_orders.json') as f:
            orders = json.load(f)
        return orders

    def get_products(self, warehouse_code="", skulist="", page=1, limit=2000):
        products = []
        with open('services/zort/example-response/get_products.json') as f:
            products = json.load(f)
        return {
            'count': products['count'],
            'list': [dacite.from_dict(ZortProduct, p) for p in products['list']]
        }
