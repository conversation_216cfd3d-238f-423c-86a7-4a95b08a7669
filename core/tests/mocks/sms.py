from typing import List
from logger.models import SmsLog
from sms.services.sms import SmsService
from uuid import uuid4


from sms.services import analyse_sms


class MockedSmsService(SmsService):
    def send_sms(self, messages, shorten_url=False, campaign=None, **kwargs):
        result = {
            'bulkId': str(uuid4()),
            'details': [
                {
                    'messageId': str(uuid4()),
                    'mobile': '66889519856',
                    'to': '66889519856',
                    'from': 'OrderUpdate',
                    'credit': analyse_sms(m['text'])['messages'],
                    'shorturl': 'N',
                    'status': {'code': '101', 'name': 'PENDING', 'description': 'Pending -Message has been accepted by the system'}
                } for m in messages
            ]
        }

        results = []
        bulk_id = result["bulkId"]
        for message, detail in zip(messages, result["details"]):
            smslog = SmsLog.create_from_ants_response(
                self.company,
                bulk_id=bulk_id,
                detail=detail,
                text=message['text'],
                remark=message.get('remark', ''),
                campaign=campaign
            )
            results.append(smslog)
        return results


# def mocked_send_sms(sender: str, messages: List[dict], shorten_url: bool = False, **kwargs):
#     """
#     Mock send_sms function

#     Example
#     -------
#     ```
#     from core.tests.mocks.sms import mocked_send_sms

#     class MockSmsTestCase(TestCase):
#         @mock.patch('services.sms.sms.send_sms', mocked_send_sms)
#         def test_something(self):
#             pass
#     ```
#     """
#     print('mocked send_sms called')
#     return (True, {
#         'bulkId': str(uuid4()),
#         'details': [
#             {
#                 'messageId': str(uuid4()),
#                 'mobile': m['to'],
#                 'to': m['to'],
#                 'from': 'Cusway-SMS',
#                 'credit': '1.00',
#                 'shorturl': 'N',
#                 'status': {'code': '101', 'name': 'PENDING', 'description': 'Pending -Message has been accepted by the system'}
#             } for m in messages
#         ]
#     })
