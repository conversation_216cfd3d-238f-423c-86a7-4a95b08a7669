from companies.models.models import Company
from picking.models import PickOrder
from services.etax_invoice.d1a_schema import PersonalBuyer
from services.etax_invoice.etax_service import ETaxService


def init_etax(company: Company, live_server_url: str):
    company.set_setting(
        "ETAX_SELLER",
        {
            "name": "Cusway",
            "tax_id": "0105559130701",
            "type_tax": "TXID",
            "seller_branch": "00000",
            "seller_branch_name": "สำนักงานใหญ่",
            "address": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230",
            "tenant_code": "dt2024046",
            "tenant_id": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
            "branch_id": "3f329a7a-1496-490c-850e-dff7d3d34bf2",
            "phone_number": "0886604941",
            "post_code": "10230",
            "api_host": f"{live_server_url}/api/mockserver/d1a",
            "api_token": "",
        },
    )
    company.set_setting("ETAX_DOCUMENT_FOLDER_ID", "1d7ECPLD91hS0hL13vKekqI2hHZtRgvpf")
    company.set_setting(
        "ETAX_BRANCHES",
        [
            {"number": "00000", "name": "สำนักงานใหญ่", "address": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230"},
            {"number": "00001", "name": "สาขา 1", "address": "ที่อยู่ xxxx สาขา 1"},
            {"number": "00002", "name": "สาขา 2", "address": "ที่อยู่ xxxx สาขา 2"},
        ],
    )


def create_tax_document(pick_order: PickOrder):
    """
    Require LiveServerTestCase
    """
    buyer = PersonalBuyer(
        buyer_name="John Doe",
        tax_id="0234567891231",
        address="123 Main St, Springfield, IL, 62704",
        email="<EMAIL>",
        phone_number="0886623545",
        post_code="10250",
        is_consent_marketing=True,
    )
    service = ETaxService.from_company(pick_order.company)
    rct, tiv = service.create_tax_documents(pick_order, buyer, "testcase")
    return rct, tiv
