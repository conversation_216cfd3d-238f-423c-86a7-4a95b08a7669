import csv
import json
from datetime import datetime
from typing import List
from uuid import uuid4
import uuid

from django.contrib.auth.models import Group
from django.utils import timezone
from django.contrib.auth.models import Permission

from rest_framework.authtoken.models import Token
from companies.models import Company
from etax.models import TaxDocument, TaxDocumentTransaction
from eula.models import LicenseAgreement
from fixcases.models import FixCase
from logger.models import SmsLog, VideoRecordLog
from services.etax_invoice.d1a_schema import CompanyBuyer, D1aDocument, PersonalBuyer
from services.etax_invoice.etax_service import ETaxService
from thaiaddress.models import ThaiAddress
from wallets.models import Wallet
from reports.models import DataStudioReport
from users.models import User
from picking.models import PickOrder


def make_datetime(date_string: str):
    """
    Parameters
    ----------
    date_string : str
        date string in format 2021-01-01 12:00:00

    Returns
    -------
    datetime
    """
    dt = datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")
    return timezone.make_aware(dt)


def create_test_datastudioreport():
    return DataStudioReport.objects.create(
        report_id="test-report-1",
        sql="""
            select
                *
            from
                logger_videorecordlog lv
            where
                lv.company_id = %(company_id)s
                and lv.record_date between %(start_date)s and %(end_date)s
        """,
        schema="""
            id: NUMBER,
            name: STRING,
            video_url: STRING,
            short_video_url: STRING,
            file_size: NUMBER,
            duration: NUMBER,
            resolution: STRING,
            record_date: YEAR_MONTH_DAY_SECOND,
            upload_date: YEAR_MONTH_DAY_SECOND,
            company_id: NUMBER,
            pick_order_id: NUMBER,
            upload_by_id: NUMBER,
        """,
    )


def create_test_videorecordlog(name: str, pick_order=None, **kwargs) -> VideoRecordLog:
    company = Company.objects.get(name="Cusway")
    user = User.objects.get(username="admin")
    log = VideoRecordLog.objects.create(
        company=company,
        name=name,
        video_url=f"https://drive.google.com/file/d/{name}",
        short_video_url=f"https://cutt.ly/{name}",
        file_size=100000,
        duration=60,
        resolution="1280x720",
        record_date=make_datetime("2021-10-31 19:28:43"),
        upload_by_id=user.id,
        pick_order=pick_order,
        scan_logs=[
            {"barcode": "10013", "time_ms": 4986},
            {"barcode": "10013", "time_ms": 6304},
            {"barcode": "10040", "time_ms": 7049},
            {"barcode": "10051", "time_ms": 7734},
            {"barcode": "10051", "time_ms": 8338},
        ],
    )

    # log.upload_date = make_datetime("2021-10-31 19:29:43")
    for key in kwargs:
        setattr(log, key, kwargs[key])
    log.save()
    return log


def create_test_smslog(
    status="DELIVERED", company=None, credit=1.0, text="test-message", remark=""
):

    if not company:
        company = Company.objects.get(name="Cusway")

    STATUSES = {
        "PENDING": {
            "code": "101",
            "name": "PENDING",
            "description": "Pending -Message has been accepted by the system",
        },
        "DELIVERED": {
            "code": "000",
            "name": "DELIVERED",
            "description": "Successfully sent to phone",
        },
    }

    return SmsLog.objects.create(
        bulk_id=str(uuid4()),
        message_id=str(uuid4()),
        sender="OrderNotice",
        to="0889519856",
        text=text,
        credit=credit,
        status=STATUSES[status],
        company_id=company.id,
        status_timestamp=timezone.now(),
        remark=remark,
    )


def create_test_company(name="Cusway") -> Company:
    return Company.objects.create(name=name)


def create_test_pickorders(company=None, user=None, limit=3) -> List[PickOrder]:
    if not company:
        company = Company.objects.get(name="Cusway")
    if not user:
        user = User.objects.get(username="admin")

    pick_orders = []
    with open(
        "./services/zort/example-response/order_json.json", encoding="utf-8"
    ) as fileobj:
        # breakpoint()
        zort_orders = json.load(fileobj)
        if limit:
            zort_orders = zort_orders[:limit]

        for zort_order in zort_orders:
            pick_orders.append(
                PickOrder.create_from_zort_order(company, zort_order, create_by=user)
            )

    return pick_orders


def create_test_pickorder_from_file(
    company=None,
    user=None,
    filename=".testdata/pick_order/zort_lazada_929755684018356.json",
) -> PickOrder:
    if not company:
        company = Company.objects.get(name="Cusway")
    if not user:
        user = User.objects.get(username="admin")

    with open(filename, encoding="utf-8") as fileobj:
        pick_order = json.load(fileobj)
        zort_order = pick_order["order_json"]
        return PickOrder.create_from_zort_order(company, zort_order, create_by=user)


def create_test_fixcase(company=None, pick_order=None, user=None):
    if not company:
        company = Company.objects.get(name="Cusway")
    if not pick_order:
        pick_order = PickOrder.objects.get(order_number="866196762092556")
    if not user:
        user = User.objects.get(username="admin")

    fixcase = FixCase.objects.create(
        company=company,
        pick_order=pick_order,
        description="test description",
        cost=0,
        create_by=user,
    )

    pick_order.has_fixcases = True
    pick_order.save()

    return fixcase



def create_thai_address():
    addrs = []
    with open("thaiaddress/data/thaiaddress.csv") as f:
        reader = csv.DictReader(f)
        for row in reader:
            addr = ThaiAddress(**row)
            addrs.append(addr)

    ThaiAddress.objects.bulk_create(addrs)


def init(
    company_name="Cusway", username="admin", company_uuid=None, initial_record=1000
):
    owner, _ = Group.objects.get_or_create(name="owner")
    admin_record_only = Group.objects.get_or_create(name="AdminRecordOnly")

    # Create company
    if not company_uuid:
        company_uuid = uuid.uuid4()
    company: Company = Company.objects.create(
        name=company_name,
        uuid=company_uuid,
        account_suffix="@localhost",
        is_setup=True,
        feature_flag={"etax": True},
    )

    # Create wallet
    wallet: Wallet = Wallet.objects.create(company=company)

    # Create user
    user: User = User.objects.create_user(
        username=username,
        password="password",
        company=company,
    )
    user.is_superuser = True
    user.is_staff = True
    user.groups.add(owner)
    user.user_permissions.set(
        Permission.objects.filter(
            codename__in=[
                "add_videorecordlog",
                "view_pickorder",
                "view_orderimportrequest",
                "add_orderimportrequest",
                "view_airwaybill",
                "add_airwaybill",
                "view_fixcase",
                "add_fixcase",
                "view_fastnote",
                "add_smscampaign",
                "view_video_record_report",
                "view_sms_report",
                "view_fixcase_report",
                "view_performance_report",
                "view_billing_report",
                "view_user",
                "add_user",
                "change_user",
                "delete_user",
                "change_settingvalue",
            ]
        )
    )
    user.save()
    company.set_setting("RECORD_PIECE_SCAN_MODE_ENABLE", True)
    company.set_setting("ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST", True)
    company.save()

    # Create token for TestClient.login_with_authtoken()
    if username == "admin":
        key = "686ca6bb0b895c4b0c83af8f6ad4ee638675c2a0"
        token = Token.objects.create(user=user, key=key)

    la = LicenseAgreement.objects.first()
    if not la:
        la = LicenseAgreement.objects.create(
            title="eula1", text="eula1", is_active=True
        )

    user.accepted_eula = la
    user.accepted_eula_timestamp = timezone.now()
    user.save()

    # Deposit Cash
    if initial_record:
        wallet.topup_record_balance(initial_record, user, "Setup")

    return {
        "user": user,
        "company": company,
        "wallet": wallet,
    }


def create_test_user():
    company = Company.objects.first()
    eula = LicenseAgreement.objects.first()

    users_data = [
        {
            "username": "user1",
            "email": "<EMAIL>",
            "nickname": "User One",
            "company": company,
            "google_oauth2": {},
            "devices": {},
            "max_concurrent_login": 2,
            "logged_in_domain": "example.com",
            "accepted_eula": eula,
            "accepted_eula_timestamp": timezone.now(),
            "is_temporary": True,
            "password": "password123",
            "password_history": ["password123"],
        },
        {
            "username": "user2",
            "email": "<EMAIL>",
            "nickname": "User Two",
            "company": company,
            "google_oauth2": {},
            "devices": {},
            "max_concurrent_login": 1,
            "logged_in_domain": "",
            "accepted_eula": eula,
            "accepted_eula_timestamp": timezone.now(),
            "is_temporary": True,
            "password": "password123",
            "password_history": ["password123"],
        },
        {
            "username": "user3",
            "email": "<EMAIL>",
            "nickname": "User Three",
            "company": company,
            "google_oauth2": {},
            "devices": {},
            "max_concurrent_login": 3,
            "logged_in_domain": "company.com",
            "accepted_eula": eula,
            "accepted_eula_timestamp": timezone.now(),
            "is_temporary": True,
            "password": "password123",
            "password_history": ["password123"],
        },
        {
            "username": "user4",
            "email": "<EMAIL>",
            "nickname": "User Four",
            "company": company,
            "google_oauth2": {},
            "devices": {},
            "max_concurrent_login": 1,
            "logged_in_domain": "example.org",
            "accepted_eula": eula,
            "accepted_eula_timestamp": timezone.now(),
            "is_temporary": True,
            "password": "password123",
            "password_history": ["password123"],
        },
    ]

    for user_data in users_data:
        user = User.objects.create_user(
            username=user_data["username"],
            email=user_data["email"],
            password=user_data["password"],
            nickname=user_data["nickname"],
            company=user_data["company"],
            google_oauth2=user_data["google_oauth2"],
            devices=user_data["devices"],
            max_concurrent_login=user_data["max_concurrent_login"],
            logged_in_domain=user_data["logged_in_domain"],
            accepted_eula=user_data["accepted_eula"],
            accepted_eula_timestamp=user_data["accepted_eula_timestamp"],
            is_temporary=user_data["is_temporary"],
            password_history=user_data["password_history"],
        )
        user.save()


def create_record_only_company(
    company_name="EZZone", username="EZAdmin", company_uuid=None, initial_record=1000
):
    company = Company.objects.first()
    eula = LicenseAgreement.objects.first()
    owner, _ = Group.objects.get_or_create(name="owner")
    admin_record_only = Group.objects.get_or_create(name="AdminRecordOnly")

    # Create company
    if not company_uuid:
        company_uuid = uuid.uuid4()
    company: Company = Company.objects.create(
        name=company_name,
        uuid=company_uuid,
        account_suffix="@localhost",
        is_setup=True,
        package=Company.PACKAGE_RECORD_ONLY,
    )

    # Create wallet
    wallet: Wallet = Wallet.objects.create(company=company)

    # Create user
    user: User = User.objects.create_user(
        username=username,
        password="password",
        company=company,
    )
    user.is_superuser = True
    user.is_staff = True
    user.groups.add(owner)
    user.user_permissions.set(
        Permission.objects.filter(
            codename__in=[
                "add_videorecordlog",
                "view_pickorder",
                "view_orderimportrequest",
                "add_orderimportrequest",
                "view_airwaybill",
                "add_airwaybill",
                "view_fixcase",
                "add_fixcase",
                "view_fastnote",
                "add_smscampaign",
                "view_video_record_report",
                "view_sms_report",
                "view_fixcase_report",
                "view_performance_report",
                "view_billing_report",
                "view_user",
                "add_user",
                "change_user",
                "delete_user",
                "change_settingvalue",
            ]
        )
    )
    user.save()

    # Create token for TestClient.login_with_authtoken()
    key = "686ca6bb0b895c4b0c83af8f6ad4ee638675c2a1"
    token = Token.objects.create(user=user, key=key)

    la = LicenseAgreement.objects.first()
    user.accepted_eula = la
    user.accepted_eula_timestamp = timezone.now()
    user.save()

    # Deposit Cash
    if initial_record:
        wallet.topup_record_balance(initial_record, user, "Setup")

    users_data = [
        {
            "username": "rouser",
            "email": "<EMAIL>",
            "nickname": "User One",
            "company": company,
            "google_oauth2": {},
            "devices": {
                # "6d19d5": {
                #     "os": "Mac OS X 10.15.7",
                #     "device": "PC",
                #     "browser": "Chrome 126.0.0",
                #     "login_at": **********,
                # }
            },
            "max_concurrent_login": 2,
            "logged_in_domain": "example.com",
            "accepted_eula": eula,
            "accepted_eula_timestamp": timezone.now(),
            "is_temporary": True,
            "password": "password123",
            "password_history": ["password123"],
        },
    ]

    for user_data in users_data:
        user = User.objects.create_user(
            username=user_data["username"],
            email=user_data["email"],
            password=user_data["password"],
            nickname=user_data["nickname"],
            company=user_data["company"],
            google_oauth2=user_data["google_oauth2"],
            devices=user_data["devices"],
            max_concurrent_login=user_data["max_concurrent_login"],
            logged_in_domain=user_data["logged_in_domain"],
            accepted_eula=user_data["accepted_eula"],
            accepted_eula_timestamp=user_data["accepted_eula_timestamp"],
            is_temporary=user_data["is_temporary"],
            password_history=user_data["password_history"],
        )
        user.save()

    return {
        "user": user,
        "company": company,
        "wallet": wallet,
    }


