from unittest.mock import patch
from django.test import TestCase, tag
from core.tests import setup
from core.tests.mocks.google_drive import MockGoogleDriveService
from core.tests.testutils import TestCaseHelper, TestClient


# TODO.0 - Rename testname into something more descriptive
class testnameTestCase(TestCase, TestCaseHelper):
    def setUp(self):
        # Setup company
        result = setup.init()
        self.company = result["company"]
        self.user = result["user"]
        self.wallet = result["wallet"]

        # Setup Test Client
        self.client = TestClient()

        # TODO.1: - Choose one of the following methods to login
        # self.client.login_with_authtoken()
        # self.client.login()

        # TODO.2: - Mo<PERSON> & Patch
        patcher1 = patch(
            "services.etax_invoice.etax_service.GoogleDriveService",
            return_value=MockGoogleDriveService(),
        )
        patcher1.start()
        self.addCleanup(patcher1.stop)
        patcher2 = patch("openapi.views.base.log_openapi_req_res")
        patcher2.start()
        self.addCleanup(patcher2.stop)

    @tag("testname")
    def test_run(self):
        self.assertEqual(1, 1)

    @tag("testname")
    def test_request_get(self):
        response = self.client.get("/echo/")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['data'], {})
        # self.assert_expected_response(response)

    @tag("testname")
    def test_request_post(self):
        response = self.client.post("/echo/", data={"key": "value"}, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['data'], {"key": "value"})
        # self.assert_expected_response(response)
