import os
import json
import sys
from types import FrameType
import pydash as py_
from pathlib import Path
from django.db import models

from datetime import date, datetime
from pprint import pprint
from typing import Union
from django.http.response import HttpResponse
from django.test.client import MULTIPART_CONTENT
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.authtoken.models import Token as AuthToken
from core.tests.rules import is_str_datetime
from users.models import User
from dataclasses import dataclass
from rest_framework.test import APIClient
from django.conf import settings

from users.serializers import CustomTokenObtainPairSerializer
from django.forms.models import model_to_dict

import logging

logger = logging.getLogger(__name__)


class Abort(Exception):
    pass


@dataclass
class Token:
    access: str
    refresh: str


class TestClient(APIClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.token = None

    def login(self, username="admin", password="password"):
        # user = User.objects.get(username=username)
        # refresh_token = RefreshToken.for_user(user)
        # self.token = Token(
        #     access=str(refresh_token.access_token),
        #     refresh=str(refresh_token),
        # )
        # serializer = CustomTokenObtainPairSerializer(data={
        #     'username': username,
        #     'password': password,
        #     "device_id": "dev001"
        # })
        # serializer.is_valid(raise_exception=True)
        response = self.post(
            "/api/users/login/",
            json.dumps(
                {"username": username, "password": password, "device_id": "dev001"}
            ),
            content_type="application/json",
        )
        self.token = Token(
            access=response.json()["access"], refresh=response.json()["refresh"]
        )
        self.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token.access}")
        return self.token

    def login_with_authtoken(self, username="admin"):
        user = User.objects.get(username=username)
        self.authtoken = AuthToken.objects.get(user=user).key
        self.credentials(HTTP_AUTHORIZATION=f"Token {self.authtoken}")
        return self.authtoken


class TestCaseHelper:
    def compare(self, value, expect, key=""):
        if type(expect) is dict:
            return self.assertMatch(value, expect)
        if type(expect) is list:
            return self.assertListMatch(value, expect)

        if expect == "<type:str-datetime>":
            logger.debug(f"CMP: | {key:<20} | {value} == <type:str-datetime>")
            return self.assertTrue(is_str_datetime(value))

        if expect in [int, str, float, date, datetime]:
            logger.debug(f"CMP: | {key:<20} | type({value}) == {expect}")
            return self.assertEqual(
                type(value),
                expect,
                msg=f"{key} datatype is not matched",
            )

        logger.debug(f"CMP: | {key:<20} | '{value}' == '{expect}'")
        return self.assertEqual(
            value,
            expect,
            msg=f"'{key}' value is not matched",
        )

    def assertMatch(self, data: Union[dict, list], expect: Union[dict, list]):
        self.assertEqual(type(data), type(expect))

        if type(expect) is dict and type(data) is dict:
            expect_keys = set(expect.keys())
            data_keys = set(data.keys())
            self.assertEqual(
                expect_keys,
                data_keys,
                msg=f"keys are not matched, expected: {expect_keys}, found: {data_keys}",
            )

            for key in expect:
                value = data[key]
                expect_value = expect[key]
                self.compare(value, expect_value, key=key)
        elif type(expect) is list and type(data) is list:
            if len(data) != len(expect):
                self.assertEqual(
                    len(data),
                    len(expect),
                    msg=f"list length is not matched, expected: {len(expect)}, found: {len(data)}",
                )

            for i, (value, expect_value) in enumerate(zip(data, expect)):
                self.compare(value, expect_value, key=f"item#{i}")

    def assertListMatch(self, data: list, expects: list):
        for item, expect in zip(data, expects):
            self.assertMatch(item, expect)

    def load_testdata(self, filename: str = None):
        frame = sys._getframe(1)
        fn_name, tdata_dir, tdata_file = self.get_testdata_dir(frame)

        if not tdata_file.exists():
            print(f"[ERROR] File not found: {tdata_file}, creating empty file")
            choice = input("Do you want to create empty file? (y/n): ")
            if choice.lower() == "n":
                return

            with open(tdata_file, "w") as f:
                f.write("{}")
                raise Abort(f"\n\nEmpty file created: {tdata_file}\nPlease fill data")

        with open(tdata_file, "r") as f:
            return json.load(f)

    def override_expected_data(self, data: Union[dict, list], override: dict):
        if isinstance(data, list):
            for data_i in data:
                self.override_expected_data(data_i, override)
            return data
        else:
            for key, value in override.items():
                key0 = key.split(".")[0]
                if key0 not in data:
                    continue

                py_.set_(data, key, value)

            return data

    def assert_response(self, response: HttpResponse, frame=None, override=None):
        frame = frame or sys._getframe(1)
        fn_name, tdata_dir, tdata_file = self.get_testdata_dir(frame)

        logger.debug(f"ASSERT: 'response' vs '{tdata_file}'")
        expected = None
        if tdata_file.exists():
            with open(tdata_file, "r") as f:
                expected = json.load(f).get("response")

        if not expected:
            print(f"[ERROR] File not found: {tdata_file}")
            self.record_expected_response(response, frame=frame, override=override)
            return

        if response.status_code != expected["status_code"]:
            print(
                f"[ERROR] status_code is not matched, expected: {expected['status_code']}, found: {response.status_code}"
            )
            print(f"[ERROR] response: {response.json()}")
            print(f"[ERROR] expected: {expected['data']}")

        self.assertEqual(response.status_code, expected["status_code"])
        self.assertMatch(response.json(), expected["data"])

    def model_to_dict_filtered(self, instance, fields):
        # if some field are connect with __ then we need to split it and first one is json field second is key
        json_fields = {}
        _fields = []
        for field in fields:
            if "__" in field:
                field, key = field.split("__")
                _fields.append(field)
                if field not in json_fields:
                    json_fields[field] = [key]
                else:
                    json_fields[field].append(key)
            else:
                _fields.append(field)

        data = model_to_dict(instance, fields=list(set(_fields)))

        if json_fields:
            for field, keys in json_fields.items():
                if field in data and isinstance(data[field], dict):
                    data[field] = {key: data[field].get(key) for key in keys}

        return data

    def assert_db_object(
        self,
        obj_key: str,
        obj: models.Model,
        fields: list = None,
        frame: FrameType = None,
        override: dict = None,
    ):
        frame = frame or sys._getframe(1)
        fn_name, tdata_dir, tdata_file = self.get_testdata_dir(frame)

        data = self.model_to_dict_filtered(obj, fields)
        self.assert_dict(
            data_key=obj_key,
            data=data,
            fields=fields,
            frame=frame,
            override=override,
        )

    def get_nested_value(self, obj, path, default=None):
        """Retrieve a value from a nested dictionary or object using a dot-separated path."""
        keys = path.split("__")
        result = obj.copy()
        for key in keys:
            if isinstance(result, dict):
                result = result.get(key, default)
            elif hasattr(result, key):
                result = getattr(result, key, default)
            else:
                return default  # Return default if key/attribute is not found
        return result

    def assert_dict(
        self,
        data_key: str,
        data: dict,
        fields: list = None,
        frame: FrameType = None,
        override: dict = None,
    ):
        frame = frame or sys._getframe(1)
        fn_name, tdata_dir, tdata_file = self.get_testdata_dir(frame)

        if fields is not None:
            data = {key: self.get_nested_value(data, key) for key in fields}

        expected = None
        if tdata_file.exists():
            with open(tdata_file, "r") as f:
                expected = json.load(f).get(data_key)

        if not expected:
            print(f"[ERROR] File not found: {tdata_file}")
            self.record_assert_data(data_key, data, frame=frame, override=override)
            return

        logger.debug(f"ASSERT: '{data_key}' vs '{tdata_file}'")
        self.assertMatch(data, expected)

    def record_expected_response(
        self,
        response: HttpResponse,
        frame=None,
        override=None,
    ):
        override = override or {}
        data = {
            "status_code": response.status_code,
            "data": self.override_expected_data(response.json(), override),
        }
        self.record_assert_data("response", data, frame=frame)

    def record_assert_data(self, data_key: str, data: dict, frame=None, override=None):
        frame = frame or sys._getframe(1)
        fn_name, tdata_dir, tdata_file = self.get_testdata_dir(frame)
        override = override or {}

        # Prepare expected data
        expected = self.override_expected_data(data, override)

        # Ask user for confirmation
        while True:
            choice = input("Do you want to overwrite test file? (y/n/s=show): ")
            if choice.lower() == "s":
                print("[SHOW]")
                print("-" * 40)
                pprint(expected)
                print("-" * 40)
            elif choice.lower() == "n":
                print("[ABORT] Skip writing to file")
                return
            elif choice.lower() == "y":
                break
            else:
                print("[ERROR] Invalid choice")

        # Write to file, (Create directory if not exist)
        tdata_dir.mkdir(parents=True, exist_ok=True)
        if not tdata_file.exists():
            with open(tdata_file, "w") as f:
                json.dump({}, f)

        with open(tdata_file, "r+") as f:
            filedata = json.load(f)
            filedata[data_key] = expected

            f.seek(0)
            json.dump(
                filedata,
                f,
                ensure_ascii=False,
                indent=4,
            )
            print(f"[SUCCESS] result written to: {tdata_file}")

    def get_testdata_dir(self, frame):
        test_filepath = frame.f_code.co_filename.replace(".py", "")
        test_funcname = frame.f_code.co_name
        testdata_dir = Path(
            os.path.relpath(Path(test_filepath).parent, settings.BASE_DIR)
        )
        testdata_file = Path(testdata_dir) / f"{test_funcname}.json"
        return (test_funcname, testdata_dir, testdata_file)
