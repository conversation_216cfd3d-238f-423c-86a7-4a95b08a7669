from dataclasses import asdict, dataclass
import shutil
import uuid

from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.response import Response
from core.storages import default_secure_storage as secure_storage
from core.storages import default_public_storage as public_storage
from picking.permissions import CanViewProductSetPage


@dataclass
class UploadFileResponse:
    file_url: str
    file_path: str


class UploadFileAPI(APIView):
    permission_classes = [CanViewProductSetPage]

    class Validator(serializers.Serializer):
        file_folder = serializers.CharField()
        file = serializers.FileField()
        is_public = serializers.BooleanField(default=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        file = validator.validated_data["file"]
        file_folder = validator.validated_data["file_folder"]
        is_public = validator.validated_data["is_public"]

        file_response = self.upload_file(file_folder, file, is_public)
        return Response(asdict(file_response))

    @staticmethod
    def upload_file(folder, file, is_public=False) -> UploadFileResponse:
        # Generate file name
        generated_uuid = uuid.uuid4()
        content_type = file.content_type
        file_path = f"{folder}/{generated_uuid}.{content_type.split('/')[1]}"

        if is_public:
            storage = public_storage
        else:
            storage = secure_storage

        with storage.open(file_path, "wb") as fp:
            file_obj = file.file
            file_obj.seek(0)
            shutil.copyfileobj(file_obj, fp)

        file_url = storage.url(file_path)
        return UploadFileResponse(file_url=file_url, file_path=file_path)


class DeleteFileAPI(APIView):
    permission_classes = [CanViewProductSetPage]

    class Validator(serializers.Serializer):
        file_path = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        file_path = validator.validated_data["file_path"]

        self.delete_file(file_path)
        return Response({"status": "File deleted"})

    @staticmethod
    def delete_file(file_path):
        secure_storage.delete(file_path)
        public_storage.delete(file_path)
