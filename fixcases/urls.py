from dynamic_rest.routers import DynamicRouter
from django.urls import path
from fixcases import views

router = DynamicRouter()
router.register('resource/fixcases', views.FixCaseDynamicViewSet)

# path = /api/fixcases/
urlpatterns = router.urls + [
    # Add other url here
    path('fixcases/close/', views.FixCaseCloseAPI.as_view()),
    path('fix-order/create/', views.FixOrderCreateAPI.as_view()),
]
