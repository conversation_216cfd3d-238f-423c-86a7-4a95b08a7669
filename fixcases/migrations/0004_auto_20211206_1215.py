# Generated by Django 2.2.24 on 2021-12-06 05:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('logger', '0006_auto_20211121_2011'),
        ('fixcases', '0003_fixcase_sms_log'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='fixcase',
            name='sms_log',
        ),
        migrations.AddField(
            model_name='fixcase',
            name='close_case_sms_log',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fixcaseclose_set', to='logger.SmsLog'),
        ),
        migrations.AddField(
            model_name='fixcase',
            name='open_case_sms_log',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fixcaseopen_set', to='logger.SmsLog'),
        ),
    ]
