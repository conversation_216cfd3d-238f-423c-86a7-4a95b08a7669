# Generated by Django 2.2.24 on 2021-12-05 04:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0003_auto_20211204_0244'),
        ('picking', '0007_auto_20211123_1213'),
    ]

    operations = [
        migrations.CreateModel(
            name='FixCase',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('tracking_code', models.CharField(blank=True, max_length=50, null=True)),
                ('complete_date', models.DateTimeField(blank=True, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('pick_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='picking.PickOrder')),
            ],
        ),
    ]
