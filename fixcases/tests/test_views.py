import json
from unittest.mock import patch
from django.utils import timezone
from django.test import TestCase
from django.test.utils import tag

from core.tests.testutils import Test<PERSON>ase<PERSON>elper, TestClient
from core.tests import setup

from companies.models import Company
from fastnotes.models import FastNote
from fixcases.models import FixCase
from fixcases.views import Fix<PERSON>ase<PERSON>lose<PERSON>I
from picking.models import PickOrder


class FixCaseTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        setup.init()
        setup.create_test_pickorders()
        setup.create_test_fixcase()

        self.company: Company = Company.objects.get(name="Cusway")
        self.company.set_setting("FIXCASE_SEND_OPEN_MESSAGE", "0")
        self.company.set_setting("FIXCASE_SEND_CLOSE_MESSAGE", "0")
        self.client = TestClient()
        self.client.login()

    @tag("fixcases")
    def test_module_is_defined(self):
        self.assertEqual(1, 1)
        self.assertEqual(PickOrder.objects.all().count(), 3)

    @tag("fixcases")
    def test_create_fixcase(self):
        pick_order = PickOrder.objects.get(order_number="866196762092556")
        res = self.client.post(
            "/api/fixcases/resource/fixcases/",
            data={
                "pick_order": pick_order.id,
                "description": "test create pick_order",
                "cost": 240,
            },
        )

        self.assertEqual(res.status_code, 201)

    @tag("fixcases")
    def test_close_fixcase(self):
        pick_order = PickOrder.objects.get(order_number="866196762092556")
        fixcase = FixCase.objects.get(pick_order=pick_order)

        res = self.client.post(
            "/api/fixcases/fixcases/close/",
            data=json.dumps(
                [
                    {
                        "fixcase": fixcase.id,
                        "tracking_code": "test tracking code",
                        "remark": "test remark",
                    }
                ]
            ),
            content_type="application/json",
        )

        fixcase: FixCase = FixCase.objects.get(pick_order=pick_order)
        self.assertEqual(res.status_code, 204)
        self.assertEqual(fixcase.tracking_code, "test tracking code")
        self.assertEqual(fixcase.remark, "test remark")
        self.assertEqual(fixcase.close_case_sms_log, None)
        self.assertNotEqual(fixcase.complete_date, None)

    @tag("fixcases")
    def test_close_fixcase_without_remark(self):
        pick_order = PickOrder.objects.get(order_number="866196762092556")
        fixcase = FixCase.objects.get(pick_order=pick_order)

        res = self.client.post(
            "/api/fixcases/fixcases/close/",
            data=json.dumps(
                [
                    {
                        "fixcase": fixcase.id,
                        "tracking_code": "test tracking code",
                    }
                ]
            ),
            content_type="application/json",
        )

        fixcase: FixCase = FixCase.objects.get(pick_order=pick_order)
        self.assertEqual(res.status_code, 204)
        self.assertEqual(fixcase.tracking_code, "test tracking code")
        self.assertEqual(fixcase.remark, "")
        self.assertEqual(fixcase.close_case_sms_log, None)
        self.assertNotEqual(fixcase.complete_date, None)

    @tag("fixcases")
    @patch("services.imind.imind.requests")
    def test_close_fixcase_with_imind(self, mocked_request):
        self.company.set_setting("IMIND_HOST", "http://imind-test.com")
        self.company.set_setting("IMIND_TOKEN", "imind-token")
        self.company.set_setting(
            "POST_CLOSE_FIXCASE_ACTION_ID", "IMindService.close_case"
        )

        pick_order = PickOrder.objects.get(order_number="866196762092556")
        fixcase = FixCase.objects.get(pick_order=pick_order)
        fixcase.json_data = {
            "create_by": {"id": 1, "name": "test imind user"},
            "ticket_id": "tk00001",
        }
        fixcase.save()

        res = self.client.post(
            "/api/fixcases/fixcases/close/",
            data=json.dumps(
                [
                    {
                        "fixcase": fixcase.id,
                        "tracking_code": "test tracking code",
                        "remark": "test remark",
                    }
                ]
            ),
            content_type="application/json",
        )

        fixcase: FixCase = FixCase.objects.get(pick_order=pick_order)
        self.assertEqual(res.status_code, 204)
        self.assertEqual(fixcase.tracking_code, "test tracking code")
        self.assertEqual(fixcase.remark, "test remark")
        self.assertEqual(fixcase.close_case_sms_log, None)
        self.assertNotEqual(fixcase.complete_date, None)

        mocked_request.post.assert_called_once_with(
            f"http://imind-test.com/imindws/UpdateToCloseStatus",
            json={
                "tokenId": "imind-token",
                "saveBy": "admin",
                "closeDescription": "test tracking code",
                "requestNumber": "tk00001",
                "remark": "test remark",
            },
        )


class FixCaseImindTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        result = setup.init()
        setup.create_test_pickorders()
        setup.create_test_fixcase()

        self.user = result["user"]
        self.company: Company = result["company"]
        self.company.set_setting("FIXCASE_SEND_OPEN_MESSAGE", "0")
        self.company.set_setting("FIXCASE_SEND_CLOSE_MESSAGE", "0")
        self.company.set_setting("IMIND_HOST", "http://imind-test.com")
        self.company.set_setting("IMIND_TOKEN", "imind-token")
        self.company.set_setting(
            "POST_CLOSE_FIXCASE_ACTION_ID", "IMindService.close_case"
        )

        self.client = TestClient()
        self.client.login()

    @tag("fixcases-imind")
    @patch("services.imind.imind.requests")
    def test_perform_post_close_fixcase_action(self, mocked_request):
        fixcase = setup.create_test_fixcase()
        fixcase.json_data = {
            "create_by": {"id": 1, "name": "test imind user"},
            "ticket_id": "tk00001",
        }
        fixcase.complete_by = self.user
        fixcase.complete_date = timezone.now()
        fixcase.tracking_code = "tracking_code1234"
        fixcase.remark = "remark1234"
        fixcase.save()

        FixCaseCloseAPI.perform_post_close_fixcase_action(self.company, fixcase)
        mocked_request.post.assert_called_once_with(
            f"http://imind-test.com/imindws/UpdateToCloseStatus",
            json={
                "tokenId": "imind-token",
                "saveBy": "admin",
                "closeDescription": "tracking_code1234",
                "requestNumber": "tk00001",
                "remark": "remark1234",
            },
        )
