from dynamic_rest.fields.fields import DynamicRelationField
from fixcases.models import FixCase
from dynamic_rest.serializers import DynamicModelSerializer


class FixCaseSerializer(DynamicModelSerializer):
    class Meta:
        ref_name = 'FixCaseSerializer'
        model = FixCase
        name = 'fixcase'
        fields = [
            'id', 'pick_order', 'description', 'cost', 'tracking_code', 'create_date',
            'is_complete', 'complete_date', 'open_case_sms_log', 'close_case_sms_log',
            'customer_phone', 'customer_name',
            'remark', 
            'fix_order'
        ]

    pick_order = DynamicRelationField(
        'picking.serializers.PickOrderSerializer', embed=True)
    fix_order = DynamicRelationField(
        'picking.serializers.PickOrderSerializer', embed=True, read_only=True)
    open_case_sms_log = DynamicRelationField(
        'logger.serializers.SmsLogSerializer', embed=True, read_only=True)
    close_case_sms_log = DynamicRelationField(
        'logger.serializers.SmsLogSerializer', embed=True, read_only=True)
