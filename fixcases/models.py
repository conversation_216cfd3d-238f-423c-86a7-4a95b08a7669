from django.db import models
from django.contrib.postgres.fields import JSO<PERSON>ield


class FixCase(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    pick_order = models.ForeignKey("picking.PickOrder", on_delete=models.CASCADE)
    fix_order = models.OneToOneField(
        "picking.PickOrder",
        on_delete=models.PROTECT,
        related_name="source_fixcase",
        null=True,
        blank=True,
    )
    description = models.TextField()
    cost = models.DecimalField(max_digits=10, decimal_places=2)
    create_date = models.DateTimeField(auto_now_add=True, db_index=True)
    create_by = models.ForeignKey(
        "users.User", on_delete=models.PROTECT, related_name="fixcase_create_by"
    )
    open_case_sms_log = models.ForeignKey(
        "logger.SmsLog",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="fixcaseopen_set",
    )
    close_case_sms_log = models.Foreign<PERSON>ey(
        "logger.SmsLog",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="fixcaseclose_set",
    )

    customer_name = models.CharField(max_length=200, blank=True, default="")
    customer_phone = models.CharField(max_length=15, blank=True, default="")

    tracking_code = models.TextField(null=True, blank=True)
    remark = models.TextField(blank=True, default="")
    is_complete = models.BooleanField(default=False)
    complete_date = models.DateTimeField(null=True, blank=True)
    complete_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="fixcase_complete_by",
        blank=True,
        null=True,
    )

    json_data = models.JSONField(blank=True, default=dict)

    def __str__(self) -> str:
        return f"FixCase {self.pick_order.order_number}"
