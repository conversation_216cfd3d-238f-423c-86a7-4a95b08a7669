from django.utils import timezone
from django.utils.decorators import method_decorator
from django.db import transaction

from dynamic_rest.viewsets import DynamicModelViewSet
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import serializers
from companies.models import Company
from core.permissions import ModelPermissions
from fixcases.models import FixCase
from django.contrib.postgres.search import SearchVector, SearchQuery
from fixcases.permissions import CanViewFixCasePage
from fixcases.serializers import FixCaseSerializer
from picking.models import PickOrder
from picking.serializers.zort import ZortOrderSerializer
from sms.services.messaging import MessagingService


class FixCaseDynamicViewSet(DynamicModelViewSet):
    permission_classes = [ModelPermissions]
    serializer_class = FixCaseSerializer

    def get_queryset(self, queryset=None):
        company = self.request.user.company
        queryset = FixCase.objects.filter(company=company)

        search_keyword = self.request.GET.get('search')
        if search_keyword:
            queryset = queryset\
                .annotate(
                    search_fields=SearchVector(
                        'description',
                        'remark',
                        'tracking_code',
                        'pick_order__order_number',
                        'pick_order__order_customer',
                        'pick_order__order_customerphone',
                    ))\
                .filter(search_fields=SearchQuery(search_keyword))

        return queryset

    @method_decorator(transaction.atomic)
    def perform_create(self, serializer):
        user = self.request.user
        company: Company = user.company
        fixcase: FixCase = serializer.save(create_by=user, company=company)

        # Update has_fixcases flag
        pick_order: PickOrder = fixcase.pick_order
        pick_order.has_fixcases = True
        pick_order.save()

        if company.get_setting('FIXCASE_SEND_OPEN_MESSAGE'):
            sms_service = company.get_sms_service()
            sms_log = sms_service.send_sms_open_fixcase(fixcase)
            fixcase.open_case_sms_log = sms_log
            fixcase.save(update_fields=['open_case_sms_log'])

        if company.get_setting('FIXCASE_LON_SEND_OPEN_MESSAGE'):
            msg_service = MessagingService(company)
            msg_service.send_lon_message_open_fixcase(fixcase)

        return fixcase

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if request.GET.get('return') == '0':
            return Response(status=HTTP_200_OK)

        return Response(serializer.data)


class FixCaseCloseAPI(APIView):
    permission_classes = [ModelPermissions]
    queryset = FixCase.objects.all()

    class Validator(serializers.Serializer):
        fixcase = serializers.PrimaryKeyRelatedField(queryset=FixCase.objects.all())
        tracking_code = serializers.CharField()
        remark = serializers.CharField(allow_blank=True, default='')

    @method_decorator(transaction.atomic)
    def post(self, request):
        validator = self.Validator(data=request.data, many=True)
        validator.is_valid(raise_exception=True)

        company: Company = request.user.company
        sms_service = company.get_sms_service()

        for data in validator.validated_data:
            fixcase: FixCase = data['fixcase']
            tracking_code: str = data['tracking_code']
            remark: str = data['remark']

            fixcase.tracking_code = tracking_code
            fixcase.remark = remark
            fixcase.is_complete = True
            fixcase.complete_date = timezone.now()
            fixcase.complete_by = request.user
            if company.get_setting('FIXCASE_SEND_CLOSE_MESSAGE'):
                sms_log = sms_service.send_sms_close_fixcase(fixcase)
                fixcase.close_case_sms_log = sms_log
            fixcase.save()

            if company.get_setting('FIXCASE_LON_SEND_CLOSE_MESSAGE'):
                msg_service = MessagingService(company)
                msg_service.send_lon_message_close_fixcase(fixcase)

            # Update has_fixcases flag
            pick_order: PickOrder = fixcase.pick_order
            if not pick_order.fixcase_set.filter(is_complete=False).exists():
                pick_order.has_fixcases = False
                pick_order.save()

            # On fixcase closed hook
            self.perform_post_close_fixcase_action(company, fixcase)

        return Response(status=204)

    @staticmethod
    def perform_post_close_fixcase_action(company: Company, fixcase: FixCase):
        if company.get_setting('POST_CLOSE_FIXCASE_ACTION_ID') == 'IMindService.close_case':
            imind_service = company.get_imind_service()
            ticket_id = fixcase.json_data.get('ticket_id')
            if ticket_id:
                imind_service.close_case(
                    request_number=ticket_id,
                    close_description=fixcase.tracking_code,
                    close_by=fixcase.complete_by.full_name(),
                    remark=fixcase.remark
                )


class FixOrderCreateAPI(APIView):
    permission_classes = [CanViewFixCasePage]

    class Validator(serializers.Serializer):
        order_json = ZortOrderSerializer()
        fixcase = serializers.PrimaryKeyRelatedField(queryset=FixCase.objects.all()) # TODO: limit scope to company
    
        def validate(self, attrs):
            attrs = super().validate(attrs)
            request = self.context['request']

            order_number = attrs['order_json']['number']
            if PickOrder.objects.filter(
                    order_number=order_number, 
                    company_id=request.user.company_id
                ).exists():
                raise serializers.ValidationError({
                    'order_json': {
                        'number': 'ORDER_NUMBER_ALREADY_EXISTS'
                    }
                })
    
            return attrs

    @method_decorator(transaction.atomic)
    def post(self, request):
        validator = self.Validator(data=request.data, context={'request': request})
        validator.is_valid(raise_exception=True)

        order_json = validator.validated_data['order_json']
        fixcase = validator.validated_data['fixcase']
        order_json["orderManagementSystem"] = "fixcase"

        pick_order = PickOrder.create_from_zort_order(
            company=request.user.company,
            order=order_json,
            order_oms="fixcase"
        )

        fixcase.fix_order = pick_order
        fixcase.save(update_fields=['fix_order'])
        fixcase.refresh_from_db()

        serializer = FixCaseSerializer(fixcase)
        return Response(serializer.data, status=HTTP_201_CREATED)