from rest_framework import serializers
from fixcases.models import FixCase
from picking.models import <PERSON><PERSON><PERSON><PERSON>
from picking.serializers.serializers import Pick<PERSON>rderSerializer
from dynamic_rest.serializers import DynamicModelSerializer, DynamicRelationField


class ExternalUserSerializer(serializers.Serializer):
    id = serializers.CharField()
    name = serializers.CharField()


class FixCaseSerializer(DynamicModelSerializer):
    pick_order = DynamicRelationField(
        'picking.serializers.PickOrderSerializer', read_only=True)

    class Meta:
        model = FixCase
        ref_name = 'FixCaseSerializerV1'
        fields = [
            'id',
            'description',
            'cost',
            'is_complete',
            'complete_date',
            'pick_order',
            'json_data',
            'tracking_code',
            'remark'
        ]
