from django.urls import path
from dynamic_rest.routers import DynamicRouter
from . import views


router = DynamicRouter()
router.register('resources/fixcases', views.FixCaseViewSet)

# /api/v1/fixcases/
urlpatterns = router.urls + [
    # Add other url here
    # path('fixcases/close/', views.FixCaseCloseAPI.as_view())
    path('fixcases/open/', views.FixCaseOpenAPI.as_view()),
    path('fixcases/<int:pk>/close/', views.FixCaseCloseAPI.as_view()),
]
