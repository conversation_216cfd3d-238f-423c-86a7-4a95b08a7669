from django.utils import timezone
from dynamic_rest.pagination import DynamicPageNumberPagination
from dynamic_rest.viewsets import DynamicModelViewSet
from rest_framework import serializers
from rest_framework.authentication import TokenAuthentication
from rest_framework.generics import GenericAPIView, get_object_or_404
from rest_framework.response import Response
from rest_framework.views import APIView

from companies.models import Company
from core.permissions import ModelPermissions
from fixcases.api.v1.serializers import (ExternalUserSerializer,
                                         FixCaseSerializer)
from fixcases.models import FixCase
from picking.models import PickOrder


class FixCaseOpenAPI(APIView):
    """Open FixCase API For iMind"""

    authentication_classes = [TokenAuthentication]
    permission_classes = [ModelPermissions]
    queryset = FixCase.objects.all()

    class Validator(serializers.ModelSerializer):
        order_number = serializers.CharField(max_length=50)
        create_by = ExternalUserSerializer()
        ticket_id = serializers.CharField(allow_blank=True, required=False)

        class Meta:
            model = FixCase
            fields = ['id', 'order_number', 'description', 'cost', 'create_by', 'ticket_id']

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        company: Company = request.user.company
        pick_order: PickOrder = get_object_or_404(
            PickOrder,
            company=company,
            order_number=data['order_number']
        )
        fix_case: FixCase = FixCase.objects.create(
            company=company,
            pick_order=pick_order,
            description=data['description'],
            cost=data['cost'],
            create_by=request.user,
            json_data={
                'create_by': data['create_by'],
                'ticket_id': data.get('ticket_id')
            }
        )

        pick_order.has_fixcases = True
        pick_order.save()

        if company.get_setting('FIXCASE_SEND_OPEN_MESSAGE'):
            sms_service = company.get_sms_service()
            sms_log = sms_service.send_sms_open_fixcase(fix_case)
            fix_case.open_case_sms_log = sms_log
            fix_case.save()

        serializer = FixCaseSerializer(fix_case)
        return Response(serializer.data)


class FixCaseCloseAPI(GenericAPIView):
    """Close FixCase API For iMind"""

    authentication_classes = [TokenAuthentication]
    permission_classes = [ModelPermissions]
    queryset = FixCase.objects.all()

    class Validator(serializers.Serializer):
        close_by = ExternalUserSerializer()
        tracking_code = serializers.CharField()

    def get_queryset(self):
        return FixCase.objects.filter(company_id=self.request.user.company_id)

    def post(self, request, *args, **kwargs):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        fix_case: FixCase = self.get_object()
        fix_case.is_complete = True
        fix_case.complete_date = timezone.now()
        fix_case.complete_by = request.user
        fix_case.tracking_code = data['tracking_code']
        fix_case.json_data = {
            **fix_case.json_data,
            'close_by': data['close_by']
        }
        fix_case.save()

        # Update has_fixcases flag
        pick_order: PickOrder = fix_case.pick_order
        if not pick_order.fixcase_set.filter(is_complete=False).exists():
            pick_order.has_fixcases = False
            pick_order.save()

        company: Company = request.user.company
        if company.get_setting('FIXCASE_SEND_CLOSE_MESSAGE'):
            sms_service = company.get_sms_service()
            sms_log = sms_service.send_sms_close_fixcase(fix_case)
            fix_case.close_case_sms_log = sms_log
            fix_case.save()

        serializer = FixCaseSerializer(fix_case)
        return Response(serializer.data)


class FixCaseViewSet(DynamicModelViewSet):
    class Pagination(DynamicPageNumberPagination):
        max_page_size = 2000
        page_size = 50

    serializer_class = FixCaseSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [ModelPermissions]
    pagination_class = Pagination

    def get_queryset(self):
        company = self.request.user.company
        return FixCase.objects.filter(company=company)
