from django.urls import path
from . import views


# /api/reports/
urlpatterns = [
    # V1
    # path("<str:report_id>/schema/", views.DataStudioReportSchemaAPI.as_view()),
    # path("<str:report_id>/data/", views.DataStudioReportDataAPI.as_view()),
    # V2
    path("v2/video-record-log/", views.VideoRecordLogDataStudioReportAPI.as_view()),
    path("v2/sms/", views.SmsDataStudioReportAPI.as_view()),
    path("v2/fixcase/", views.FixCaseDataStudioReportAPI.as_view()),
    path("v2/record-transaction/", views.RecordTransactionReportAPI.as_view()),
    path("v2/sms-transaction/", views.SmsTransactionReportAPI.as_view()),
    path("v2/company/", views.CompanyDetailReportAPI.as_view()),
    path("v2/pick-order/", views.PickOrderDataStudioReportAPI.as_view()),
    path("v2/pick-order-detail/", views.PickOrderDataDetailStudioReportAPI.as_view()),
    path("v2/video-record-diff/", views.VideoRecordDiffItemReport.as_view()),
    path("v2/pick-item/", views.PickItemDataStudioReportAPI.as_view()),
    path(
        "v2/pick-item-daily-summary/",
        views.PickItemDailySummaryDataStudioReportAPI.as_view(),
    ),
    path(
        "v2/pick-item-return-daily-summary/",
        views.PickItemReturnDailySummaryDataStudioReportAPI.as_view(),
    ),
    path("v2/image-capture-log/", views.ImageCaptureLogDataStudioReport.as_view()),
]
