from io import BytesIO
import json
from django.http import FileResponse
import pandas as pd
from django.shortcuts import get_object_or_404
from django.conf import settings
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import renderers, serializers
from rest_framework.exceptions import AuthenticationFailed
from rest_framework_simplejwt.authentication import JWTAuthentication

from rest_framework.views import APIView
from core.serializers import EndDateField, StartDateField
from datamasking.services import mask
from logger.models import ImageCaptureLog, VideoRecordLog
from reports.generics import GenericDataStudioReportAPI
from reports.models import DataStudioReport
from django.db import connection

from reports.permissions import (
    CanViewBillingReport,
    CanViewDataStudioReport,
    CanViewPickOrderDetailReport,
    CanViewFixcaseReport,
    CanViewImageCaptureLogReport,
    CanViewPickOrderReport,
    CanViewSmsReport,
    CanViewVideoRecordDiffReport,
    CanViewVideoRecordReport,
    CanViewPickItemReport,
)


class AlreadyJSONRenderer(renderers.BaseRenderer):
    media_type = "application/json"
    format = "json"

    def render(self, data, media_type=None, renderer_context=None):
        if renderer_context["response"].status_code == 200:
            return data
        return json.dumps(data)


class DataStudioTokenAuthentication(TokenAuthentication):
    def authenticate(self, request):
        if not settings.DEBUG_REPORT:
            msg = "Unauthorized, EDR01"
            raise AuthenticationFailed(msg)
        return super().authenticate(request)


class DataStudioReportDataAPI(APIView):
    renderer_classes = [AlreadyJSONRenderer]
    permission_classes = [CanViewDataStudioReport]
    authentication_classes = [
        JWTAuthentication,
        DataStudioTokenAuthentication,
    ]

    class Validator(serializers.Serializer):
        start_date = StartDateField()
        end_date = EndDateField()

    def get(self, request, report_id):
        report = get_object_or_404(DataStudioReport, report_id=report_id)
        fields = request.GET.get("fields", "")
        fields = fields.split(",") if fields else []

        validator = self.Validator(data=request.GET)
        validator.is_valid(raise_exception=True)

        df = pd.read_sql_query(
            report.sql,
            connection,
            params={
                "start_date": validator.validated_data["start_date"],
                "end_date": validator.validated_data["end_date"],
                "company_id": request.user.company_id,
            },
        )

        schema = report.get_schema()
        datetime_columns = [f for f in schema if schema[f] == "YEAR_MONTH_DAY_SECOND"]
        for col in datetime_columns:
            df[col] = pd.to_datetime(df[col])
            if df[col].dt.tz:
                df[col] = df[col].dt.tz_convert(settings.TIME_ZONE)
            df[col] = df[col].dt.strftime("%Y%m%d%H%M%S")

        date_cols = [f for f in schema if schema[f] == "YEAR_MONTH_DAY"]
        for col in date_cols:
            df[col] = pd.to_datetime(df[col])
            df[col] = df[col].dt.strftime("%Y%m%d")

        if fields:
            df = df[fields]

        return Response(df.to_json(orient="split", default_handler=str))


class DataStudioReportSchemaAPI(APIView):
    permission_classes = [AllowAny]

    def get(self, request, report_id):
        report = get_object_or_404(DataStudioReport, report_id=report_id)
        return Response(report.get_schema())


class VideoRecordLogDataStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewVideoRecordReport]

    sql = """
        select
            lv.id ,
            lv.uuid ,
            lv."name" as "recordname",
            lv.video_url,
            lv.short_video_url ,
            lv.file_size,
            lv.duration,
            lv.resolution,
            lv.record_date,
            lv.pick_order_id,
            lv.company_id,
            lv.filetype,
            lv.speed,
            lv.is_return,
            lv.audio,
            lv.weight,
            lv.extra,
            cc."name" as "comname",
            pp.order_number,
            pp.order_trackingno,
            pp.order_saleschannel,
            pp.order_customer,
            pp.order_customerphone,
            pp.order_date,
            pp.ready_to_ship_timestamp,
            pp.remark,
            pp.order_total_quantity,
            pp.order_total_price,
            pp.short_receipt_url,
            uu.username as "recusername"
        from
            logger_videorecordlog lv
        left join companies_company cc on
            lv.company_id = cc.id
        left join picking_pickorder pp on
            lv.pick_order_id = pp.id
        left join users_user uu on
            lv.upload_by_id = uu.id
        where %(company_id)s = lv.company_id and lv.record_date between %(start_date)s and %(end_date)s
    """
    schema = {
        "id": "NUMBER",
        "recordname": "TEXT",
        "video_url": "URL",
        "video_url2": "URL",
        "filetype": "TEXT",
        "speed": "TEXT",
        "audio": "BOOLEAN",
        "weight": "NUMBER",
        "extra": "TEXT",
        "is_return": "BOOLEAN",
        "short_video_url": "URL",
        "file_size": "NUMBER",
        "duration": "NUMBER",
        "resolution": "TEXT",
        "record_date": "YEAR_MONTH_DAY_SECOND",
        "pick_order_id": "NUMBER",
        "company_id": "NUMBER",
        "comname": "TEXT",
        "order_number": "TEXT",
        "order_trackingno": "TEXT",
        "order_saleschannel": "TEXT",
        "order_customer": "TEXT",
        "order_customerphone": "TEXT",
        "order_date": "YEAR_MONTH_DAY",
        "ready_to_ship_timestamp": "YEAR_MONTH_DAY_SECOND",
        "remark": "TEXT",
        "order_total_quantity": "NUMBER",
        "order_total_price": "NUMBER",
        "short_receipt_url": "URL",
        "recusername": "TEXT",
    }

    def get_data_frame(self, request, start_date=None, end_date=None) -> pd.DataFrame:
        df = super().get_data_frame(request, start_date, end_date)
        df["video_url2"] = df.apply(
            func=lambda x: VideoRecordLog.get_dobybot_video_url(
                x["video_url"], x["recordname"], x["filetype"], x["uuid"]
            ),
            axis=1,
            result_type="reduce",
        )
        df["order_total_quantity"].fillna(0, inplace=True)
        df["order_total_price"].fillna(0, inplace=True)
        return df


class SmsDataStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewSmsReport]

    sql = """
        select 
            ls.id,
            ls.sender,
            ls.to,
            ls.text,
            ls.credit,
            ls.status->>'name' as status_name,
            ls.status->>'description' as status_desc,
            ls.timestamp,
            ls.message_id,
            ls.bulk_id,
            ls.company_id,
            ls.campaign_id,
            ss.name as campaign_name,
            cc.name as company_name
        from logger_smslog ls
        left join sms_smscampaign ss on ls.campaign_id = ss.id
        left join companies_company cc on ls.company_id = cc.id
        where %(company_id)s = ls.company_id and ls.timestamp between %(start_date)s and %(end_date)s
    """
    schema = {
        "id": "NUMBER",
        "sender": "TEXT",
        "to": "TEXT",
        "text": "TEXT",
        "credit": "NUMBER",
        "status__name": "TEXT",
        "status__desc": "TEXT",
        "timestamp": "YEAR_MONTH_DAY_SECOND",
        "message_id": "TEXT",
        "bulk_id": "TEXT",
        "company_id": "NUMBER",
        "campaign_id": "NUMBER",
        "campaign_name": "TEXT",
        "company_name": "TEXT",
    }


class FixCaseDataStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewFixcaseReport]

    sql = """
        with t_fixcase as (
            select
                ff.id,
                ff.description,
                ff.cost,
                ff.create_date,
                ff.tracking_code,
                ff.remark,
                ff.complete_date,
                pp.order_number,
                pp.order_saleschannel,
                ff.pick_order_id,
                ff.customer_name as order_customer,
                ff.customer_phone as order_customerphone,
                pp.order_json->>'shippingaddress' as shipping_address
            from
                fixcases_fixcase ff
            left join picking_pickorder pp on
                ff.pick_order_id = pp.id
            where 
                %(company_id)s = ff.company_id and ff.create_date between %(start_date)s and %(end_date)s
        ),
        t_vlog as (
            select
                lv.id, 
                lv.upload_by_id,
                lv.pick_order_id,
                row_number() over (partition by lv.pick_order_id order by lv.id) as rownum
            from
                logger_videorecordlog lv
            where
                lv.company_id = %(company_id)s
                and lv.pick_order_id in (select pick_order_id from t_fixcase)
        )
        select
            tf.*,
            uu.username as packed_by
        from
            t_fixcase tf
        left join t_vlog tv on
            tf.pick_order_id = tv.pick_order_id and tv.rownum = 1
        left join users_user uu on
            uu.id = tv.upload_by_id
    """
    schema = {
        "id": "NUMBER",
        "description": "TEXT",
        "cost": "NUMBER",
        "create_date": "YEAR_MONTH_DAY_SECOND",
        "tracking_code": "TEXT",
        "remark": "TEXT",
        "complete_date": "YEAR_MONTH_DAY_SECOND",
        "order_number": "TEXT",
        "order_saleschannel": "TEXT",
        "order_customer": "TEXT",
        "order_customerphone": "TEXT",
        "shipping_address": "TEXT",
        "packed_by": "TEXT",
    }


class CompanyDetailReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewBillingReport]

    sql = """
        select
            cc.id,
            cc.name,
            cc.create_date,
            cc.expire_date,
            cc.is_active,
            ww.sms_balance,
            ww.record_balance
        from
            companies_company cc
        left join wallets_wallet ww on
            ww.company_id = cc.id
        where 
            cc.id = 1
    """
    schema = {
        "id": "NUMBER",
        "name": "TEXT",
        "create_date": "YEAR_MONTH_DAY",
        "expire_date": "YEAR_MONTH_DAY",
        "is_active": "BOOLEAN",
        "sms_balance": "NUMBER",
        "record_balance": "NUMBER",
    }


class SmsTransactionReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewBillingReport]

    sql = """
        select
            ws.id,
            ws.type,
            ws.description,
            ws.value ,
            ws.running_balance,
            ws.create_date,
            ws.wallet_id,
            ws.date
        from
            wallets_smstransaction ws
        where 
            ws.wallet_id = (
                select ww.id 
                from wallets_wallet ww 
                where ww.company_id = %(company_id)s
            )
    """

    schema = {
        "id": "NUMBER",
        "type": "TEXT",
        "description": "TEXT",
        "value": "NUMBER",
        "running_balance": "NUMBER",
        "create_date": "YEAR_MONTH_DAY_SECOND",
        "wallet_id": "NUMBER",
        "date": "YEAR_MONTH_DAY",
    }


class RecordTransactionReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewBillingReport]

    sql = """
        select
            wr.id,
            wr.type,
            wr.description,
            wr.value ,
            wr.running_balance,
            wr.create_date,
            wr.wallet_id,
            wr.date
        from
            wallets_recordtransaction wr
        where 
            wr.wallet_id = (
                select ww.id 
                from wallets_wallet ww 
                where ww.company_id = %(company_id)s
            )
    """

    schema = {
        "id": "NUMBER",
        "type": "TEXT",
        "description": "TEXT",
        "value": "NUMBER",
        "running_balance": "NUMBER",
        "create_date": "YEAR_MONTH_DAY_SECOND",
        "wallet_id": "NUMBER",
        "date": "YEAR_MONTH_DAY",
    }


class PickOrderDataStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewPickOrderReport]

    sql = """
        with 
            t_video_record_logs as (
            select 
                pick_order_id, 
                upload_by_id,
                upload_date
            from logger_videorecordlog lv where lv.company_id = %(company_id)s and lv.upload_date > %(start_date)s
        )

        select
            pp.order_saleschannel,
            pp.order_shippingchannel,
            pp.order_date,
            pp.create_date,
            pp.order_number, 
            pp.order_trackingno,
            pp.order_customer,
            pp.has_videos,
            pp.order_json->>'status' as order_status,
            tu1.username as video_upload_by,
            lv.upload_date as video_update_date,
            pp.has_fixcases,
            pp.has_return_videos,
            pp.ready_to_ship,
            pp.ready_to_ship_timestamp,
            tu4.username as ready_to_ship_by,
            pp.print_count,
            pp.print_timestamp,
            pp.airway_bill_print_count,
            pp.airway_bill_print_timestamp,
            pp.remark,
            et.doc_id as etax_doc_id,
            et.status as etax_status,
            et.doc_url as etax_url,
            et.edit_count as etax_edit_count,
            et.buyer->>'buyer_name' as etax_buyer_name,
            et.buyer->>'tax_id' as etax_buyer_tax_id
        from picking_pickorder pp 
        left join t_video_record_logs lv on pp.id = lv.pick_order_id
        left join users_user tu1 on upload_by_id = tu1.id
        left join users_user tu4 on ready_to_ship_by_id = tu4.id
        left join etax_taxdocument et on pp.id = et.pick_order_id
        where pp.company_id = %(company_id)s and pp.order_date between %(start_date)s and %(end_date)s
    """

    schema = {
        "order_number": "TEXT",
        "order_trackingno": "TEXT",
        "order_saleschannel": "TEXT",
        "order_shippingchannel": "TEXT",
        "order_customer": "TEXT",
        "has_videos": "BOOLEAN",
        "has_fixcases": "BOOLEAN",
        "has_return_videos": "BOOLEAN",
        "order_date": "YEAR_MONTH_DAY",
        "create_date": "YEAR_MONTH_DAY_SECOND",
        "ready_to_ship": "BOOLEAN",
        "ready_to_ship_timestamp": "YEAR_MONTH_DAY_SECOND",
        "video_upload_by": "TEXT",
        "video_update_date": "YEAR_MONTH_DAY_SECOND",
        "ready_to_ship_by": "TEXT",
        "print_count": "NUMBER",
        "print_timestamp": "YEAR_MONTH_DAY_SECOND",
        "airway_bill_print_count": "NUMBER",
        "airway_bill_print_timestamp": "YEAR_MONTH_DAY_SECOND",
        "remark": "TEXT",
        "order_status": "TEXT",
        "etax_doc_id": "TEXT",
        "etax_status": "TEXT",
        "etax_url": "URL",
        "etax_edit_count": "NUMBER",
        "etax_buyer_name": "TEXT",
        "etax_buyer_tax_id": "TEXT",
    }

    def get_data_frame(self, request, start_date=None, end_date=None) -> pd.DataFrame:
        df = super().get_data_frame(request, start_date, end_date)
        df["order_customer"] = df["order_customer"].apply(mask)
        return df


class PickOrderDataDetailStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewPickOrderDetailReport]

    sql = """
        with 
            t_video_record_logs as (
            select 
                pick_order_id, 
                upload_by_id,
                upload_date
            from logger_videorecordlog lv where lv.company_id = %(company_id)s and lv.upload_date > %(start_date)s
        ) select
            pp.order_saleschannel,
            pp.order_shippingchannel,
            pp.order_date,
            pp.create_date,
            pp.order_number, 
            pp.order_trackingno,
            pp.order_customer,
            pp.has_videos,
            order_item -> 'sku' as item_sku,
            order_item -> 'name' as item_name,
            order_item -> 'unittext' as item_unit,
            order_item -> 'pricepernumber' as item_price,
            order_item -> 'number' as item_amount,
            order_item -> 'totalprice' as item_total_price,
            order_item -> 'seller_discount' as item_discount,
            pp.order_json -> 'shippingamount' as shipping_amount,
            pp.order_json -> 'discountamount' as order_discount,
            pp.order_json -> 'amount' as order_amount,
            pp.order_json -> 'customerphone' as customer_phone,
            pp.order_json -> 'customeremail' as customer_email,
            pp.order_json -> 'shippingaddress' as shipping_address,
            pp.order_json->>'status' as order_status,
            tu1.username as video_upload_by,
            lv.upload_date as video_update_date,
            pp.has_fixcases,
            pp.has_return_videos,
            pp.ready_to_ship,
            pp.ready_to_ship_timestamp,
            tu4.username as ready_to_ship_by,
            pp.print_count,
            pp.print_timestamp,
            pp.airway_bill_print_count,
            pp.airway_bill_print_timestamp,
            pp.remark,
            et.doc_id as etax_doc_id,
            et.status as etax_status,
            et.doc_url as etax_url,
            et.edit_count as etax_edit_count,
            et.buyer->>'buyer_name' as etax_buyer_name,
            et.buyer->>'tax_id' as etax_buyer_tax_id,
            et.buyer->>'address' as etax_buyer_address,
            et.buyer->>'phone_number' as etax_buyer_phone_number,
            et.buyer->>'email' as etax_buyer_email
        from picking_pickorder pp 
        left join t_video_record_logs lv on pp.id = lv.pick_order_id
        left join users_user tu1 on upload_by_id = tu1.id
        left join users_user tu4 on ready_to_ship_by_id = tu4.id
        left join etax_taxdocument et on pp.id = et.pick_order_id
        cross join lateral 
        	jsonb_array_elements(pp.order_json -> 'list') as order_item
        where pp.company_id = %(company_id)s and pp.order_date between %(start_date)s and %(end_date)s
    """

    schema = {
        "order_number": "TEXT",
        "order_trackingno": "TEXT",
        "order_saleschannel": "TEXT",
        "order_shippingchannel": "TEXT",
        "order_customer": "TEXT",
        "item_sku": "TEXT",
        "item_name": "TEXT",
        "item_unit": "TEXT",
        "item_price": "NUMBER",
        "item_amount": "NUMBER",
        "item_total_price": "NUMBER",
        "item_discount": "NUMBER",
        "shipping_amount": "NUMBER",
        "order_discount": "NUMBER",
        "order_amount": "NUMBER",
        "customer_phone": "TEXT",
        "customer_email": "TEXT",
        "shipping_address": "TEXT",
        "has_videos": "BOOLEAN",
        "has_fixcases": "BOOLEAN",
        "has_return_videos": "BOOLEAN",
        "order_date": "YEAR_MONTH_DAY",
        "create_date": "YEAR_MONTH_DAY_SECOND",
        "ready_to_ship": "BOOLEAN",
        "ready_to_ship_timestamp": "YEAR_MONTH_DAY_SECOND",
        "video_upload_by": "TEXT",
        "video_update_date": "YEAR_MONTH_DAY_SECOND",
        "ready_to_ship_by": "TEXT",
        "print_count": "NUMBER",
        "print_timestamp": "YEAR_MONTH_DAY_SECOND",
        "airway_bill_print_count": "NUMBER",
        "airway_bill_print_timestamp": "YEAR_MONTH_DAY_SECOND",
        "remark": "TEXT",
        "order_status": "TEXT",
        "etax_doc_id": "TEXT",
        "etax_status": "TEXT",
        "etax_url": "TEXT",
        "etax_edit_count": "NUMBER",
        "etax_buyer_name": "TEXT",
        "etax_buyer_tax_id": "TEXT",
        "etax_buyer_address": "TEXT",
        "etax_buyer_phone_number": "TEXT",
        "etax_buyer_email": "TEXT",
    }

    def get_data_frame(self, request, start_date=None, end_date=None) -> pd.DataFrame:
        df = super().get_data_frame(request, start_date, end_date)
        df["order_customer"] = df["order_customer"].apply(mask)
        df["customer_phone"] = df["customer_phone"].apply(mask)
        df["customer_email"] = df["customer_email"].apply(mask)
        df["etax_buyer_name"] = df["etax_buyer_name"].apply(mask)
        df["etax_buyer_phone_number"] = df["etax_buyer_phone_number"].apply(mask)
        df["etax_buyer_email"] = df["etax_buyer_email"].apply(mask)
        return df


class VideoRecordDiffItemReport(GenericDataStudioReportAPI):
    permission_classes = [CanViewVideoRecordDiffReport]
    sql = """
    with temp1 as (
        select
            lv.id,
            lv.uuid,
            lv.record_date,
            lv.upload_date,
            pp.order_number,
            pp.order_trackingno ,
            pp.order_saleschannel,
            pp.order_customer,
            uu1.username as upload_by,
            lv.name as record_name,
            lv.video_url,
            lv.diff_logs
        from
            logger_videorecordlog lv
        left join users_user uu1 on
            uu1.id = lv.upload_by_id
        left join picking_pickorder pp on
            pp.id = lv.pick_order_id
        where
            lv.diff_logs is not null 
            and lv.company_id = %(company_id)s 
            and lv.record_date between %(start_date)s and %(end_date)s
    )

    select
        id,
        uuid,
        record_date,
        upload_date,
        order_number,
        order_trackingno,
        order_saleschannel,
        order_customer,
        upload_by,
        record_name,
        video_url,
        item.data->>'sku' as item_sku,
        item.data->>'name' as item_name,
        item.data->>'diff' as diff,
        item.data->>'total' as total,
        item.data->>'packed' as packed
    from
        temp1,
        jsonb_array_elements(temp1.diff_logs) with ordinality as item(data, position)

    """

    schema = {
        "id": "NUMBER",
        "uuid": "TEXT",
        "record_date": "YEAR_MONTH_DAY_SECOND",
        "upload_date": "YEAR_MONTH_DAY_SECOND",
        "order_number": "TEXT",
        "order_trackingno": "TEXT",
        "order_saleschannel": "TEXT",
        "order_customer": "TEXT",
        "upload_by": "TEXT",
        "record_name": "TEXT",
        "video_url": "TEXT",
        "item_sku": "TEXT",
        "item_name": "TEXT",
        "diff": "NUMBER",
        "total": "NUMBER",
        "packed": "NUMBER",
    }


class PickItemDataStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewPickItemReport]
    sql = """
        WITH temp1 AS (
            SELECT
                lv.id,
                u1.username as record_by,
                record_date,
                upload_date,
                pp.order_number,
                pp.order_trackingno,
                name AS scan_number,
                lv.is_return,
                json_item->>'barcode' AS product_barcode,
                json_item->>'name' AS product_name,
                json_item->'amount' AS amount
            FROM
                logger_videorecordlog lv
            LEFT JOIN 
                picking_pickorder pp ON pp.id = lv.pick_order_id
            LEFT JOIN
                users_user u1 ON u1.id = lv.upload_by_id
            CROSS JOIN LATERAL 
                jsonb_array_elements(scan_logs) AS json_item
            WHERE
                lv.company_id = %(company_id)s 
                and lv.record_date between %(start_date)s and %(end_date)s
        )

        SELECT
            id,
            record_date,
            upload_date,
            order_number,
            order_trackingno,
            scan_number,
            product_name,
            product_barcode,
            record_by,
            is_return,
            SUM(amount::integer) AS sum_amount
        FROM
            temp1
        GROUP BY
            id,
            record_date,
            upload_date,
            order_number,
            order_trackingno,
            scan_number,
            product_name,
            product_barcode,
            is_return,
            record_by
    """

    schema = {
        "id": "NUMBER",
        "record_date": "YEAR_MONTH_DAY_SECOND",
        "upload_date": "YEAR_MONTH_DAY_SECOND",
        "order_number": "TEXT",
        "order_trackingno": "TEXT",
        "scan_number": "TEXT",
        "product_name": "TEXT",
        "product_barcode": "TEXT",
        "record_by": "TEXT",
        "sum_amount": "NUMBER",
        "is_return": "BOOLEAN",
    }


class PickItemDailySummaryDataStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewPickItemReport]
    sql = """
        with temp1 as (
            select
                lv.id,
                record_date,
                name as scan_number,
                json_item->>'barcode' AS barcode,
                json_item->>'name' AS name,
                json_item->'amount' AS amount
            from
                logger_videorecordlog lv
            left join picking_pickorder pp on pp.id = lv.pick_order_id
            cross join lateral jsonb_array_elements(scan_logs) as json_item
            where
                lv.company_id = %(company_id)s 
                and lv.name not like 'RT-%%'
                and lv.record_date between %(start_date)s and %(end_date)s
        )

        select
            record_date::date,
            barcode, 
            name,
            SUM(amount::integer) as sum_amount
        from
            temp1
        group by 
            id, record_date::date, name, barcode
    """
    schema = {
        "record_date": "YEAR_MONTH_DAY",
        "barcode": "TEXT",
        "name": "TEXT",
        "sum_amount": "NUMBER",
    }


class PickItemReturnDailySummaryDataStudioReportAPI(GenericDataStudioReportAPI):
    permission_classes = [CanViewPickItemReport]
    sql = """
        with temp1 as (
            select
                lv.id,
                record_date,
                name as scan_number,
                json_item->>'barcode' AS barcode,
                json_item->>'name' AS name,
                json_item->'amount' AS amount
            from
                logger_videorecordlog lv
            left join picking_pickorder pp on pp.id = lv.pick_order_id
            cross join lateral jsonb_array_elements(scan_logs) as json_item
            where
                lv.company_id = %(company_id)s 
                and lv.name like 'RT-%%'
                and lv.record_date between %(start_date)s and %(end_date)s
        )

        select
            record_date::date,
            barcode, 
            name,
            SUM(amount::integer) as sum_amount
        from
            temp1
        group by 
            id, record_date::date, name, barcode
    """
    schema = {
        "record_date": "YEAR_MONTH_DAY",
        "barcode": "TEXT",
        "name": "TEXT",
        "sum_amount": "NUMBER",
    }


class ImageCaptureLogDataStudioReport(GenericDataStudioReportAPI):
    permission_classes = [CanViewImageCaptureLogReport]
    sql = """
        select
            li.uuid,
            li.name,
            li.create_date,
            uu.username as "image_taken_by",
            li.images,
            li.drive_folder_id,
            li.drive_account,
            pp.order_number,
            pp.order_trackingno 
        from
            logger_imagecapturelog li
        left join users_user uu on
            uu.id = li.create_by_id
        left join picking_pickorder pp on
            pp.id = li.pick_order_id 
        where li.company_id = %(company_id)s and li.create_date between %(start_date)s and %(end_date)s
    """
    schema = {
        "uuid": "TEXT",
        "name": "TEXT",
        "create_date": "YEAR_MONTH_DAY",
        "image_taken_by": "TEXT",
        "images": "TEXT",
        "drive_folder_id": "TEXT",
        "drive_account": "TEXT",
        "order_number": "TEXT",
        "order_trackingno": "TEXT",
        "url": "URL",
    }

    def get_data_frame(self, request, start_date=None, end_date=None) -> pd.DataFrame:
        df = super().get_data_frame(request, start_date, end_date)
        df["url"] = df.apply(
            func=lambda x: ImageCaptureLog.get_dobybot_image_capture_url(
                x["drive_folder_id"]
            ),
            axis=1,
            result_type="reduce",
        )
        return df
