from rest_framework import permissions
from distutils.util import strtobool


class CanViewDataStudioReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("reports.view_datastudioreport")


class CanViewVideoRecordReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm(
            "reports.view_video_record_report"
        ) or request.user.has_perm("users.view_video_record_report_no_video_link")


class CanViewSmsReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("reports.view_sms_report")


class CanViewFixcaseReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("reports.view_fixcase_report")


class CanViewPerformanceReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("reports.view_performance_report")


class CanViewBillingReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("users.view_billing_report")


class CanViewPickOrderReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("users.export_pickorder")


class CanViewPickOrderDetailReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("users.export_pickorder_detail")


class CanViewVideoRecordDiffReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("users.view_video_record_diff_report")


class CanViewPickItemReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("users.view_pick_item_report")


class CanViewPickItemDailySummaryReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("users.view_pick_item_daily_summary_report")


class CanViewImageCaptureLogReport(permissions.BasePermission):
    def has_permission(self, request, view):
        if strtobool(request.GET.get("schema", "0")):
            return True
        return request.user.has_perm("users.view_image_capture_log_report")
