# Generated by Django 2.2.28 on 2025-05-20 11:20

from django.db import migrations
from django.core.management.sql import emit_post_migrate_signal


def create_permssions(apps, schema_editor):
    emit_post_migrate_signal(0, False, "default")

    Permission = apps.get_model("auth", "Permission")
    ContentType = apps.get_model("contenttypes", "ContentType")

    User = apps.get_model("users", "User")

    old_perm = Permission.objects.filter(codename="export_etax_pickorder")
    if old_perm.exists():
        old_perm.delete()

    perms = [
        # Add permission to create
        {
            "codename": "export_pickorder_detail",
            "name": "ส่งออกข้อมูลคำสั่งซื้อออกเป็นไฟล์ .xlsx (ละเอียด)",
            "model": User,
        },
    ]

    for perm in perms:
        Permission.objects.create(
            codename=perm["codename"],
            name=perm["name"],
            content_type=ContentType.objects.get_for_model(perm["model"]),
        )


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0037_create_export_etax_permissiongs"),
    ]

    operations = [
        migrations.RunPython(create_permssions),
    ]
