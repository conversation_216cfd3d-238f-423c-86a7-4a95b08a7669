from django.contrib.auth import authenticate
from django.contrib.auth.models import Permission
from django.contrib.postgres.search import SearchVector
from django.utils import timezone
from dynamic_rest.viewsets import DynamicModelViewSet
from rest_framework import serializers
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK, HTTP_204_NO_CONTENT
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.views import TokenObtainPairView

from companies.models import Company
from core.permissions import ModelPermissions
from users.models import User
from users.serializers import CustomTokenObtainPairSerializer, UserSerializer
from utils.query_string import parse_qs


class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            raise InvalidToken(e.args[0]) from e

        return Response(serializer.validated_data, status=HTTP_200_OK)


class LogoutAPI(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [AllowAny]

    class Validator(serializers.Serializer):
        device_id = serializers.CharField(required=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        device_id = None

        if validator.validated_data.get("device_id"):
            device_id = validator.validated_data.get("device_id")

        if not device_id and request.auth:
            device_id = request.auth.get("device_id")

        if device_id:
            request.user.remove_login_device(device_id)

        return Response(device_id, status=HTTP_200_OK)


class UserViewSet(DynamicModelViewSet):
    serializer_class = UserSerializer
    permission_classes = [ModelPermissions]

    def get_queryset(self, queryset=None):
        company = self.request.user.company
        queryset = (
            User.objects.filter(company=company)
            .exclude(is_superuser=True)
            .exclude(is_staff=True)
            .exclude(username__startswith="openapi@")
            .exclude(username__startswith="dobybot-connect@")
        )

        queryset = self.filter_queryset_by_get_params(queryset)
        queryset = self.search_queryset(queryset)
        return queryset

    def search_queryset(self, queryset):
        search_keyword = self.request.GET.get("search")
        if not search_keyword:
            return queryset

        return queryset.annotate(
            search=SearchVector("username", "first_name", "last_name")
        ).filter(search__icontains=search_keyword)

    def filter_queryset_by_get_params(self, queryset):
        params = parse_qs(self.request.GET)

        if "user_permissions" in params:
            queryset = queryset.filter(
                user_permissions__codename__in=params["user_permissions"]
            )

        return queryset

    def perform_create(self, serializer: UserSerializer):
        data = serializer.validated_data

        user: User = serializer.save(company=self.request.user.company)
        user.set_password(data["password"])
        user.save()

        user.user_permissions.clear()
        user.user_permissions.set(data["user_permissions"])
        return user

    def perform_update(self, serializer: UserSerializer):
        data = serializer.validated_data
        data.pop("username", None)
        user: User = serializer.save(company=self.request.user.company)

        if "password" in data:
            user.set_password(data["password"])
            user.save()

        if "user_permissions" in data:
            user.user_permissions.clear()
            user.user_permissions.set(data["user_permissions"])

        return user

    def perform_destroy(self, instance: User):
        instance.is_active = False
        instance.save()


class PagePermissionListAPI(APIView):
    def get(self, *args, **kwargs):

        page_permissions = [
            {
                "page": "Record",
                "page_url": "/record",
                "permissions": [
                    {"codename": "add_videorecordlog", "depends_on": []},
                    {
                        "codename": "add_videorecordlog_return",
                        "depends_on": ["add_videorecordlog"],
                    },
                    {
                        "codename": "add_videorecordlog_transfer",
                        "depends_on": ["add_videorecordlog"],
                    },
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
            {
                "page": "Image Capture",
                "page_url": "/image-capture",
                "permissions": [
                    {"codename": "add_imagecapturelog", "depends_on": []},
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
            {
                "page": "Order Center",
                "page_url": "/order-center",
                "permissions": [
                    {
                        "codename": "view_pickorder",
                        "depends_on": [],
                    },
                    {
                        "codename": "add_pickordertrackingno",
                        "depends_on": ["view_pickorder"],
                    },
                    {
                        "codename": "add_pickorder",
                        "depends_on": ["view_pickorder"],
                    },
                    {
                        "codename": "export_pickorder",
                        "depends_on": ["view_pickorder"],
                    },
                    {
                        "codename": "export_pickorder_detail",
                        "depends_on": ["view_pickorder"],
                    },
                    # {
                    #     "codename": "add_taxdocument",
                    #     "depends_on": ["view_pickorder"],
                    # },
                    # {'codename': 'perform_checkout',   # <------------------- ADD
                    #  'depends_on': ['add_orderimportrequest']},
                ],
                "packages": [Company.PACKAGE_FULL_INTEGRATION],
            },
            {
                "page": "e-Tax",
                "page_url": "/easy-etax/create",
                "permissions": [
                    {
                        "codename": "view_taxdocument",
                        "depends_on": [],
                    },
                    {
                        "codename": "add_taxdocument",
                        "depends_on": ["view_taxdocument"],
                    },
                    {
                        "codename": "change_taxdocument",
                        "depends_on": ["view_taxdocument"],
                    },
                    {
                        "codename": "delete_taxdocument",
                        "depends_on": ["view_taxdocument"],
                    },
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                ],
            },
            {
                "page": "Order Import",
                "page_url": "/order-import",
                "permissions": [
                    {"codename": "view_orderimportrequest", "depends_on": []},
                    {
                        "codename": "add_orderimportrequest",
                        "depends_on": ["view_orderimportrequest"],
                    },
                ],
                "packages": [Company.PACKAGE_FULL_INTEGRATION],
            },
            {
                "page": "Airway Bill",
                "page_url": "/airway-bill",
                "permissions": [
                    {"codename": "view_airwaybill", "depends_on": []},
                    {"codename": "add_airwaybill", "depends_on": ["view_airwaybill"]},
                ],
                "packages": [Company.PACKAGE_FULL_INTEGRATION],
            },
            {
                "page": "Fix Case",
                "page_url": "/fixcase",
                "permissions": [
                    {"codename": "view_fixcase", "depends_on": []},
                    {"codename": "add_fixcase", "depends_on": ["view_fixcase"]},
                    {"codename": "change_fixcase", "depends_on": ["view_fixcase"]},
                    # {'codename': 'close_fixcase', 'depends_on': [ 'view_fixcase']},  # <------------------- ADD
                ],
                "packages": [Company.PACKAGE_FULL_INTEGRATION],
            },
            {
                "page": "Fast Note",
                "page_url": "/fastnote",
                "permissions": [
                    {"codename": "view_fastnote", "depends_on": []},
                    # {'codename': 'add_fastnote', 'depends_on': ['view_fastnote']},
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
            {
                "page": "SMS",
                "page_url": "/sms",
                "permissions": [
                    {"codename": "add_smscampaign", "depends_on": []},
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
            {
                "page": "Report",
                "page_url": "/report/*",
                "permissions": [
                    {"codename": "view_video_record_report", "depends_on": []},
                    {
                        "codename": "view_video_record_report_no_video_link",
                        "depends_on": [],
                    },
                    {"codename": "view_video_record_diff_report", "depends_on": []},
                    {"codename": "view_sms_report", "depends_on": []},
                    {"codename": "view_fixcase_report", "depends_on": []},
                    {"codename": "view_performance_report", "depends_on": []},
                    {"codename": "view_billing_report", "depends_on": []},
                    {"codename": "view_pick_item_report", "depends_on": []},
                    {
                        "codename": "view_pick_item_daily_summary_report",
                        "depends_on": [],
                    },
                    {"codename": "view_image_capture_log_report", "depends_on": []},
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
            {
                "page": "User Management",
                "page_url": "/settings/users",
                "permissions": [
                    {"codename": "view_user", "depends_on": []},
                    {"codename": "add_user", "depends_on": ["view_user"]},
                    {"codename": "change_user", "depends_on": ["view_user"]},
                    {"codename": "delete_user", "depends_on": ["view_user"]},
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
            {
                "page": "Product Management",
                "page_url": "/settings/product/*",
                "permissions": [
                    {"codename": "view_product", "depends_on": []},
                    {"codename": "view_productset", "depends_on": []},
                    {"codename": "view_productserialno", "depends_on": []},
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
            {
                "page": "Setting",
                "page_url": "/settings",
                "permissions": [
                    {"codename": "change_settingvalue", "depends_on": []},
                ],
                "packages": [
                    Company.PACKAGE_FULL_INTEGRATION,
                    Company.PACKAGE_RECORD_ONLY,
                ],
            },
        ]

        perms_map = {p.codename: p.name for p in Permission.objects.all()}
        company: Company = self.request.user.company

        results = []
        for page in page_permissions:
            for perm in page["permissions"]:
                perm["name"] = perms_map[perm["codename"]]

            if company.package in page["packages"]:
                results.append(page)

        return Response(results)


class LoggedInUserAPI(APIView):
    def get(self, request):
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data)

    def post(self, request):
        user: User = request.user
        user.logged_in_domain = request.data.get("domain", "")
        user.save(update_fields=["logged_in_domain"])
        return Response()


class ChangePasswordAPI(APIView):
    class Validator(serializers.Serializer):
        old_password = serializers.CharField()
        new_password = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        form = validator.validated_data
        user = request.user

        if not user.check_password(form["old_password"]):
            raise serializers.ValidationError(
                {"code": "INVALID_OLD_PASSWORD", "detail": "รหัสผ่านเก่าไม่ถูกต้อง"}
            )

        if user.check_password_history(form["new_password"]):
            raise serializers.ValidationError(
                {"code": "DO_NOT_REUSE_PASSWORD", "detail": "ห้ามใช้รหัสผ่านซ้ำกับของเก่า"}
            )

        user.set_password(form["new_password"])
        user.save()

        return Response(status=HTTP_204_NO_CONTENT)


class DeviceStatusUpdateAPI(APIView):
    class Validator(serializers.Serializer):
        device_id = serializers.CharField()
        videos = serializers.JSONField()
        settings = serializers.JSONField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data
        device_id = data["device_id"]

        user: User = request.user
        if device_id in user.devices:
            user.devices[device_id]["videos"] = data["videos"]
            user.devices[device_id]["settings"] = data["settings"]
            user.devices[device_id]["server_timestamp"] = timezone.localtime().strftime(
                "%Y-%m-%dT%H:%M:%S%z"
            )
            user.save(update_fields=["devices"])

        return Response(status=HTTP_204_NO_CONTENT)
