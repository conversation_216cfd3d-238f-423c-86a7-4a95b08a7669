from dynamic_rest.serializers import DynamicModelSerializer, DynamicRelationField
from rest_framework import serializers
from etax.models import TaxDocument
from logger.models import ImageCaptureLog, ReadyToShipLog, SmsLog, VideoRecordLog
from picking.models import PickOrderTrackingNo


class VideoRecordLogSerializer(DynamicModelSerializer):
    upload_by = DynamicRelationField("users.serializers.SimpleUserSerializer")
    download_url = serializers.ReadOnlyField(source="get_download_url")

    class Meta:
        model = VideoRecordLog
        name = "video_record_log"
        fields = [
            "id",
            "company",
            "pick_order",
            "name",
            "video_url",
            "short_video_url",
            "file_size",
            "duration",
            "resolution",
            "record_date",
            "upload_date",
            "upload_by",
            "filetype",
            "speed",
            "drive_id",
            "drive_account",
            "drive_file_id",
            "drive_folder_id",
            "is_return",
            "scan_logs",
            "audio",
            "download_url",
            "weight",
            "diff_logs",
            "extra",
        ]
        read_only_fields = ["company", "pick_order", "upload_by"]


class ImageCaptureLogDynamicSerializer(DynamicModelSerializer):
    class Meta:
        model = ImageCaptureLog
        name = "image_capture_log"
        fields = [
            "id",
            "name",
            "create_date",
            "create_by",
            "drive_folder_id",
            "drive_id",
            "drive_account",
            "images",
        ]

    create_by = DynamicRelationField("users.serializers.SimpleUserSerializer")


class TaxDocumentDynamicModelSerializer(DynamicModelSerializer):
    class Meta:
        model = TaxDocument
        name = "tax_document"
        fields = [
            "id",
            "uuid",
            "order_number",
            "create_date",
            "status",
            "doc_info",
            "doc_url",
            "doc_type",
            "edit_count",
            "credit_count",
            "buyer",
            "log",
        ]


class ImageCaptureLogSerializer(serializers.ModelSerializer):
    create_by_name = serializers.CharField(source="create_by.username")

    class Meta:
        model = ImageCaptureLog
        name = "image_capture_log"
        fields = [
            "id",
            "name",
            "create_date",
            "create_by",
            "create_by_name",
            "drive_folder_id",
            "drive_id",
            "drive_account",
            "images",
        ]


class PickOrderTrackingNoDynamicSerializer(DynamicModelSerializer):
    class Meta:
        model = PickOrderTrackingNo
        name = "pick_order_tracking_no"
        fields = [
            "id",
            "uuid",
            "pick_order",
            "tracking_no",
            "shipping_status",
            "shipping_message",
            "status",
            "is_active",
            "create_date",
            "update_date",
            "shipping_provider",
            "webhook_logs",
            "tracking_info",
            "cod_amount",
            "is_cod_refunded",
            "cod_refunded_pno",
        ]
        read_only_fields = [
            "uuid",
            "company",
            "create_date",
            "update_date",
            "is_cod_refunded",
            "cod_refunded_pno",
        ]


class SmsLogSerializer(DynamicModelSerializer):
    class Meta:
        model = SmsLog
        fields = "__all__"


class ReadyToShipLogSerializer(DynamicModelSerializer):
    create_by = DynamicRelationField(
        "users.serializers.SimpleUserSerializer", embed=True
    )

    class Meta:
        model = ReadyToShipLog
        fields = ["id", "company", "pick_order", "barcode", "create_by", "create_date"]
        read_only_fields = [
            "id",
            "company",
            "pick_order",
            "barcode",
            "create_by",
            "create_date",
        ]
