# Generated by Django 2.2.28 on 2023-03-25 09:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0037_auto_20230215_2202'),
        ('picking', '0033_product_product_oms'),
        ('logger', '0022_auto_20221029_1113'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrintLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('file_path', models.FilePathField(null=True)),
                ('print_type', models.CharField(max_length=10)),
                ('print_job_id', models.CharField(max_length=40)),
                ('print_key', models.CharField(db_index=True, max_length=40)),
                ('airway_bill', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='picking.AirwayBill')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('pick_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='picking.PickOrder')),
            ],
        ),
    ]
