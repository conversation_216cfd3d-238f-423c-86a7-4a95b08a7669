# Generated by Django 3.2.19 on 2024-04-03 20:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import nanoid.generate


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("companies", "0052_alter_settingvalue_key"),
        ("picking", "0043_auto_20240404_0301"),
        ("wallets", "0012_wallet_sms_credit_onhold"),
        ("logger", "0034_smslog_sms_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="ImageCaptureLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uuid",
                    models.CharField(
                        db_index=True,
                        default=nanoid.generate,
                        max_length=21,
                        unique=True,
                    ),
                ),
                ("create_date", models.DateTimeField(auto_now_add=True)),
                ("name", models.Char<PERSON>ield(max_length=255)),
                ("images", models.J<PERSON><PERSON>ield(default=list)),
                ("drive_folder_id", models.Char<PERSON>ield(max_length=255)),
                ("drive_id", models.CharField(max_length=255)),
                ("drive_account", models.CharField(max_length=255)),
                ("drive_file_deleted", models.BooleanField(default=False)),
                (
                    "drive_file_deleted_timestamp",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                    ),
                ),
                (
                    "create_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "deduct_from_package",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to="wallets.recordpackage",
                    ),
                ),
                (
                    "pick_order",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="picking.pickorder",
                    ),
                ),
            ],
        ),
    ]
