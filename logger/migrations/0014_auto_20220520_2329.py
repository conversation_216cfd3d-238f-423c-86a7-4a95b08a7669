# Generated by Django 2.2.24 on 2022-05-20 16:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('logger', '0013_fill_smslog_status_timestamp'),
    ]

    operations = [
        migrations.AddField(
            model_name='videorecordlog',
            name='drive_account',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='videorecordlog',
            name='drive_file_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='videorecordlog',
            name='drive_file_deleted_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='videorecordlog',
            name='drive_file_id',
            field=models.CharField(blank=True, db_index=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='videorecordlog',
            name='drive_id',
            field=models.CharField(blank=True, db_index=True, max_length=100, null=True),
        ),
    ]
