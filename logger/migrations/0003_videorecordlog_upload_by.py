# Generated by Django 2.2.24 on 2021-10-31 14:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('logger', '0002_auto_20211030_1410'),
    ]

    operations = [
        migrations.AddField(
            model_name='videorecordlog',
            name='upload_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
    ]
