# Generated by Django 2.2.24 on 2022-05-14 05:19

from django.db import migrations
from django.core.paginator import Paginator
from django.core.management.sql import emit_post_migrate_signal


def fill_status_timestamp(apps, schema_editor):
    emit_post_migrate_signal(0, False, "default")

    SmsLog = apps.get_model('logger', 'SmsLog')
    smslogs = SmsLog.objects.all().order_by('id')
    if smslogs.count() == 0:
        return

    paginator = Paginator(smslogs, 1000)
    if paginator.num_pages > 0:
        print(f'\SmsLog: total page {paginator.num_pages}')

    for i in range(paginator.num_pages):
        updatelist = []
        for sms_log in paginator.page(i+1):
            sms_log.status_timestamp = sms_log.timestamp
            updatelist.append(sms_log)

        SmsLog.objects.bulk_update(updatelist, fields=['status_timestamp'])
        print(f'finish page {i+1}')


class Migration(migrations.Migration):

    dependencies = [
        ('logger', '0012_smslog_status_timestamp'),
    ]

    operations = [
        migrations.RunPython(fill_status_timestamp)
    ]
