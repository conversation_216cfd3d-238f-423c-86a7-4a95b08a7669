# Generated by Django 2.2.24 on 2021-10-30 12:25

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0011_auto_20211030_1225'),
        ('picking', '0006_auto_20211030_1225'),
    ]

    operations = [
        migrations.CreateModel(
            name='VideoRecordLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('video_url', models.URLField()),
                ('short_video_url', models.URLField(blank=True, null=True)),
                ('file_size', models.PositiveIntegerField()),
                ('duration', models.PositiveIntegerField()),
                ('resolution', models.CharField(max_length=20)),
                ('record_date', models.DateTimeField()),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('pick_order', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='picking.PickOrder')),
            ],
        ),
        migrations.CreateModel(
            name='SmsLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_id', models.UUIDField()),
                ('sender', models.CharField(max_length=60)),
                ('to', models.CharField(max_length=20)),
                ('message', models.TextField()),
                ('credit', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', django.contrib.postgres.fields.jsonb.JSONField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
            ],
        ),
    ]
