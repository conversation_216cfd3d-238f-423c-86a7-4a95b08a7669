# Generated by Django 2.2.24 on 2022-02-06 02:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('companies', '0008_auto_20220206_0927'),
        ('picking', '0015_auto_20220206_0927'),
        ('logger', '0006_auto_20211121_2011'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReadyToShipLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('barcode', models.CharField(max_length=100)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('create_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('pick_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='picking.PickOrder')),
            ],
        ),
    ]
