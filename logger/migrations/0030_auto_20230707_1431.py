# Generated by Django 3.2.19 on 2023-07-07 07:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('logger', '0029_auto_20230523_2056'),
    ]

    operations = [
        migrations.AddField(
            model_name='smslog',
            name='channel',
            field=models.CharField(blank=True, choices=[('SHOPEE_CHAT', 'Shopee Chat'), ('LAZADA_CHAT', 'Lazada Chat'), (
                'SMS', 'SMS'), ('VRICH_FACEBOOK_CHAT', 'Vrich Facebook Chat')], max_length=20, null=True),
        ),
        # migrations.AlterField(
        #     model_name='printlog',
        #     name='id',
        #     field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        # ),
        # migrations.AlterField(
        #     model_name='readytoshiplog',
        #     name='id',
        #     field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        # ),
        # migrations.AlterField(
        #     model_name='smslog',
        #     name='id',
        #     field=models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        # ),
        migrations.AlterField(
            model_name='smslog',
            name='status',
            field=models.JSONField(),
        ),
        migrations.AlterField(
            model_name='videorecordlog',
            name='diff_logs',
            field=models.JSONField(blank=True, null=True),
        ),
        # migrations.AlterField(
        #     model_name='videorecordlog',
        #     name='id',
        #     field=models.BigAutoField(auto_created=True, primary_key=True,
        #                               serialize=False, verbose_name='ID'),
        # ),
        migrations.AlterField(
            model_name='videorecordlog',
            name='scan_logs',
            field=models.JSONField(default=list),
        ),
    ]
