import json
import requests
import jwt
import urllib.parse
import time
import typing
import json

from picking.models import <PERSON><PERSON>rder

if typing.TYPE_CHECKING:
    from companies.models import Company

from decimal import Decimal
from uuid import uuid4
from django.db import models
from django.conf import settings
from django.utils import timezone
import nanoid
from services.sms.sms import format_phone_number
from sms.models import CustomerMessagingInfo

from utils.random import random_chars


class VideoRecordLog(models.Model):
    uuid = models.CharField(
        unique=True, db_index=True, default=nanoid.generate, max_length=21
    )
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    pick_order = models.ForeignKey(
        "picking.PickOrder",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )

    name = models.CharField(max_length=200, db_index=True)
    video_url = models.CharField(max_length=200, blank=True)
    short_video_url = models.URLField(blank=True, null=True)
    file_size = models.BigIntegerField()
    duration = models.DecimalField(max_digits=10, decimal_places=2)
    resolution = models.CharField(max_length=20)
    record_date = models.DateTimeField(db_index=True)
    filetype = models.CharField(max_length=10, default="webm")
    speed = models.CharField(max_length=10, default="1x")
    audio = models.BooleanField(default=False)
    upload_date = models.DateTimeField(auto_now_add=True, db_index=True)
    upload_by = models.ForeignKey("users.User", on_delete=models.CASCADE)

    # Google Drive - Share Drive
    drive_id = models.CharField(max_length=100, blank=True, null=True, db_index=True)
    drive_folder_id = models.CharField(max_length=100, blank=True, null=True)
    drive_file_id = models.CharField(
        max_length=100, blank=True, null=True, db_index=True
    )
    drive_account = models.CharField(max_length=100, blank=True, null=True)
    drive_file_deleted = models.BooleanField(default=False)
    drive_file_deleted_timestamp = models.DateTimeField(blank=True, null=True)

    # Return Mode
    is_return = models.BooleanField(default=False)
    scan_logs = models.JSONField(default=list)
    diff_logs = models.JSONField(blank=True, null=True)
    weight = models.DecimalField(
        max_digits=8, decimal_places=2, blank=True, null=True
    )  # grams

    extra = models.JSONField(default=list)

    # บันทึก RecordPackage ที่ใช้ในการตัดเครดิต
    deduct_from_package = models.ForeignKey(
        "wallets.RecordPackage", on_delete=models.PROTECT, blank=True, null=True
    )

    @staticmethod
    def get_dobybot_video_url(video_url: str, name="", type="", uuid=""):
        if video_url and video_url.startswith("file:///"):
            return video_url

        if uuid:
            return f"{settings.UI_HOST}/video-viewer?uuid={uuid}"

        if not video_url:
            return None

        file_id = video_url.split("/")[-1]
        return f"{settings.UI_HOST}/video-viewer?name={name}&file_id={file_id}&type={type}&uuid={uuid}"

    @staticmethod
    def get_signed_video_url(video_record_log: "VideoRecordLog"):
        # key = settings.RECEIPT_SIGINING_KEY
        # payload = {
        #     'iat': int(time.time()),
        #     'sub': video_record_log.id,
        #     'ref': video_record_log.company_id
        # }
        # token = jwt.encode(payload, key, algorithm='HS256')
        token = VideoRecordLog.get_video_token(video_record_log)
        return f"{settings.UI_HOST}/video-viewer/?t={token}"

    @staticmethod
    def get_video_token(video_record_log: "VideoRecordLog"):
        key = settings.RECEIPT_SIGINING_KEY
        payload = {
            "iat": int(time.time()),
            "sub": video_record_log.id,
            "ref": video_record_log.company_id,
        }
        token = jwt.encode(payload, key, algorithm="HS256")
        return token

    @staticmethod
    def get_credit_used(company: "Company", vLog: "VideoRecordLog"):
        if not vLog.is_return:
            return 1

        if company.package == company.PACKAGE_FULL_INTEGRATION:
            return 0.5
        if company.package == company.PACKAGE_RECORD_ONLY:
            return 1

        raise Exception(f'Invalid company package "{company.package}"')

    def get_drive_api_download_url(self):
        """
        Direct download via Drive API, Use this url to bypass virus scan warning for file
        larger than 100MB

        Returns
        -------
        str
            Download URL
        """
        drive_api_key = "AIzaSyCzGqTNIX7YsD_k_im2t0G5o6tiefj5jiw"
        return f"https://www.googleapis.com/drive/v3/files/{self.drive_file_id}/?alt=media&key={drive_api_key}"

    def get_download_url(self):
        if self.file_size > 100e6:
            return self.get_drive_api_download_url()
        return f"https://drive.google.com/uc?export=download&id={self.drive_file_id}&confirm=true"


class SmsLog(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    campaign = models.ForeignKey("sms.SmsCampaign", on_delete=models.CASCADE, null=True)
    bulk_id = models.CharField(db_index=True, max_length=100)
    message_id = models.CharField(db_index=True, unique=True, max_length=100)
    sender = models.CharField(max_length=60)
    to = models.CharField(max_length=200)
    text = models.TextField()
    remark = models.TextField(blank=True, default="")
    credit = models.DecimalField(max_digits=10, decimal_places=2)
    timestamp = models.DateTimeField(auto_now_add=True)

    PAID_SMS = "paid"
    READY_TO_SHIP_SMS = "ready_to_ship"
    FIXCASE_SMS = "fixcase"
    SMS_TYPES = (
        (PAID_SMS, "Paid"),
        (READY_TO_SHIP_SMS, "Ready to ship"),
        (FIXCASE_SMS, "Fixcase"),
    )
    sms_type = models.CharField(
        max_length=20, choices=SMS_TYPES, blank=True, null=True, default=None
    )

    status = models.JSONField()
    status_timestamp = models.DateTimeField(null=True, blank=True)

    CHANNEL_SHOPEE_CHAT = "SHOPEE_CHAT"
    CHANNEL_LAZADA_CHAT = "LAZADA_CHAT"
    CHANNEL_SMS = "SMS"
    CHANNEL_VRICH_FACEBOOK_CHAT = "VRICH_FACEBOOK_CHAT"
    CHANNEL_CHOICES = [
        (CHANNEL_SHOPEE_CHAT, "Shopee Chat"),
        (CHANNEL_LAZADA_CHAT, "Lazada Chat"),
        (CHANNEL_SMS, "SMS"),
        (CHANNEL_VRICH_FACEBOOK_CHAT, "Vrich Facebook Chat"),
    ]
    channel = models.CharField(
        max_length=20, choices=CHANNEL_CHOICES, blank=True, null=True
    )

    @staticmethod
    def create_from_ants_response(
        company,
        bulk_id,
        text,
        remark,
        detail,
        campaign=None,
        commit=True,
        sms_type=None,
    ):
        """Create SmsLog from Ants Response

        Parameters
        ----------
        company: Company
            sender company
        text: str
        remark: str
        bulk_id: str
            bulkId from ants response
        detail: dict
            detail object from ant response
        campaign: SmsCampaign
            default to None
        type:
            'fixcase' | 'paid' | 'ready_to_ship'
            default to None
        Returns
        -------
        SmsLog
        """
        if "BLOCKED" in detail["to"]:
            detail["status"] = {
                "code": "209",
                "name": "UNDELIVERED",
                "description": "PDPA - Receipient requested to block",
            }

        sms_log = SmsLog(
            company=company,
            bulk_id=bulk_id,
            message_id=detail["messageId"],
            sender=detail["from"],
            to=detail["to"],
            credit=Decimal(detail["credit"]),
            status=detail["status"],
            text=text,
            remark=remark,
            campaign=campaign,
            sms_type=sms_type,
        )

        if commit:
            sms_log.save()

        return sms_log

    @staticmethod
    def update_status_from_ants_callback(message_id: str, status: dict) -> "SmsLog":
        """Update SmsLog from ants response

        Parameters
        ----------
        bulk_id : str
        message_id : str
        status : dict
            Example {"code": "000", "name": "DELIVERED", "description": "Successfully sent to phone"}

        Returns
        -------
        [type]
            [description]
        """
        try:
            log = SmsLog.objects.get(message_id=message_id)
            old_status = log.status

            log.status = status
            log.status_timestamp = timezone.now()

            mobile = format_phone_number(log.to)
            msg_info = CustomerMessagingInfo.objects.filter(mobile=mobile).first()
            if not msg_info:
                msg_info = CustomerMessagingInfo(mobile=mobile)
            msg_info.lon_status_code = status["code"]
            msg_info.lon_status_desc = status["description"]
            msg_info.save()

            if (
                not old_status["code"] == status["code"]
                and log.sms_type == SmsLog.PAID_SMS
            ):
                company = log.company
                wallet = company.wallet
                refund_mode = company.get_setting("API_SMS_REFUND_UNDELIVERED")

                if refund_mode:
                    if status["code"] in ["000", "200"]:
                        wallet.sms_balance -= log.credit
                    wallet.sms_credit_onhold -= log.credit
                    wallet.save()
                else:
                    # Do nothing because the credit is deducted when sending
                    pass

            log.save()
            return log
        except SmsLog.DoesNotExist:
            return None

    @staticmethod
    def create_from_taximail_response(
        company: "Company", pick_order: PickOrder, response: requests.Response
    ):
        """
        Example response.text:
        {
            "status": "success",
            "code": 202,
            "data": {
                "message_id": "68016619839f2b6a4c018bf9",
                "claimed": 1
            }
        }
        """

        response_data = json.loads(response.text)

        if response.status_code == 202:
            return SmsLog.objects.create(
                company=company,
                sender=company.get_setting("EMAIL_SENDER"),
                bulk_id=uuid4(),
                message_id=response_data["data"]["message_id"],
                credit=0,
                to=pick_order.order_json.get("shippingemail")
                or pick_order.order_json.get("customeremail"),
                status={
                    "code": 200,
                    "name": "EMAIL SENT",
                    "description": "Email has been sent by the system",
                    "Message": response.text,
                },
                text="",
                remark="AUTO-RTS-EMAIL",
                campaign=None,
                status_timestamp=timezone.now(),
            )
        else:
            return SmsLog.objects.create(
                company=company,
                sender=company.get_setting("EMAIL_SENDER"),
                bulk_id=uuid4(),
                message_id=uuid4(),
                credit=0,
                to=pick_order.order_json.get("shippingemail")
                or pick_order.order_json.get("customeremail"),
                status={
                    "code": 400,
                    "name": "EMAIL FAIL",
                    "description": "Failed to send email",
                    "Message": response.text,
                },
                text="",
                remark="AUTO-RTS-EMAIL",
                campaign=None,
                status_timestamp=timezone.now(),
            )


class ReadyToShipLog(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    pick_order = models.ForeignKey("picking.PickOrder", on_delete=models.CASCADE)
    barcode = models.CharField(max_length=100)
    create_by = models.ForeignKey("users.User", on_delete=models.PROTECT)
    create_date = models.DateTimeField(auto_now_add=True)


class PrintLog(models.Model):
    """ """

    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    # create_by = models.ForeignKey('users.User', on_delete=models.PROTECT)
    create_date = models.DateTimeField(auto_now_add=True)

    pick_order = models.ForeignKey(
        "picking.PickOrder", on_delete=models.CASCADE, blank=True, null=True
    )
    airway_bill = models.ForeignKey(
        "picking.AirwayBill", on_delete=models.CASCADE, blank=True, null=True
    )

    file_path = models.FilePathField(null=True)

    PRINT_TYPE_PICK_ORDER = "PICK"
    PRINT_TYPE_AIRWAY_BILL = "AWB"
    PRINT_TYPE_RECEIPT = "RECEIPT"
    PRINT_TYPE_WEIGHT_SLIP = "WEIGHT"
    PRINT_TYPE_CHOICES = (
        (PRINT_TYPE_PICK_ORDER, "Pick Slip"),
        (PRINT_TYPE_AIRWAY_BILL, "Airway Bill"),
        (PRINT_TYPE_RECEIPT, "Receipt"),
        (PRINT_TYPE_WEIGHT_SLIP, "Weight Slip"),
    )
    print_type = models.CharField(max_length=10)
    print_job_id = models.CharField(max_length=100)

    # A string key to prevent printnode from printing a document twice
    #
    # Normally this field should be
    #   print_type + pick_order.order_number
    #
    # But if a user want to print the same document twice, this field will be
    #   print_type + pick_order.order_number + random_str(5)
    #
    # See: https://www.printnode.com/en/docs/api/curl#printjob-creating, idempotency
    print_key = models.CharField(max_length=100, db_index=True)

    @staticmethod
    def get_awb_print_key(order_number, idempotency=True):
        order_number = urllib.parse.quote(order_number)
        print_key = f"{PrintLog.PRINT_TYPE_AIRWAY_BILL}:{order_number}"

        if not idempotency:
            print_key += "-" + random_chars(5)

        return print_key

    @staticmethod
    def get_pick_order_print_key(order_number, idempotency=True):
        order_number = urllib.parse.quote(order_number)
        print_key = f"{PrintLog.PRINT_TYPE_PICK_ORDER}:{order_number}"

        if not idempotency:
            print_key += "-" + random_chars(5)

        return print_key

    @staticmethod
    def get_weight_slip_print_key(order_number, idempotency=True):
        order_number = urllib.parse.quote(order_number)
        print_key = f"{PrintLog.PRINT_TYPE_WEIGHT_SLIP}:{order_number}"

        if not idempotency:
            print_key += "-" + random_chars(5)

        return print_key


class ImageCaptureLog(models.Model):

    uuid = models.CharField(
        unique=True, db_index=True, default=nanoid.generate, max_length=21
    )
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    create_by = models.ForeignKey("users.User", on_delete=models.PROTECT)
    create_date = models.DateTimeField(auto_now_add=True)

    # order_number/tracking_no/document_no
    name = models.TextField()
    images = models.JSONField(default=list)

    pick_order = models.ForeignKey(
        "picking.PickOrder",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )

    # id เข้าถึง drive https://drive.google.com/drive/folders/{drive_folder_id}
    drive_folder_id = models.CharField(max_length=255)
    drive_id = models.CharField(max_length=255)
    drive_account = models.CharField(max_length=255)
    drive_file_deleted = models.BooleanField(default=False)
    drive_file_deleted_timestamp = models.DateTimeField(null=True, blank=True)

    deduct_from_package = models.ForeignKey(
        "wallets.RecordPackage", on_delete=models.PROTECT, blank=True, null=True
    )

    @staticmethod
    def get_dobybot_image_capture_url(drive_folder_id):
        # return f"{settings.UI_HOST}/image-viewer?uuid={uuid}"
        return f"https://drive.google.com/drive/folders/{drive_folder_id}"
