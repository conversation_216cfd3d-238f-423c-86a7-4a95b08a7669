from dynamic_rest.viewsets import DynamicModelViewSet
from logger.models import ReadyToShipLog
from logger.serializers import ReadyToShipLogSerializer
from core.permissions import ModelPermissions


class ReadyToShipLogViewSet(DynamicModelViewSet):
    permission_classes = [ModelPermissions]
    serializer_class = ReadyToShipLogSerializer

    def get_queryset(self, queryset=None):
        company_id = self.request.user.company_id
        return ReadyToShipLog.objects.filter(company_id=company_id)
