from decimal import Decimal
from pprint import pprint
from datetime import datetime
from django.test import TestCase
from django.test.utils import tag, override_settings
from core.tests.testutils import TestCaseHelper, TestClient
from core.tests import setup
from logger.models import SmsLog, VideoRecordLog


# python manage.py test --tag=video-record-log-model
class VideoRecordLogTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        self.user, self.company, self.wallet = setup.init().values()
        self.client = TestClient()
        self.client.login()

    @tag("video-record-log-model")
    @override_settings(UI_HOST="https://cloud.dobybot.com")
    def test_get_dobybot_video_url(self):
        url = VideoRecordLog.get_dobybot_video_url(
            video_url="https://drive.google.com/file/d/1zIItwjB5bPYzY8QV5EeWk5LmEkkqd-6G",
            name="video-1",
            type="webm",
        )
        self.assertEqual(
            url,
            "https://cloud.dobybot.com/video-viewer?name=video-1&file_id=1zIItwjB5bPYzY8QV5EeWk5LmEkkqd-6G&type=webm&uuid=",
        )

    @tag("video-record-log-model")
    @override_settings(UI_HOST="https://cloud.dobybot.com")
    def test_get_dobybot_video_url_null(self):
        url = VideoRecordLog.get_dobybot_video_url(None)
        self.assertEqual(url, None)

    @tag("video-record-log-model")
    @override_settings(UI_HOST="https://cloud.dobybot.com")
    def test_get_signed_video_url(self):
        pick_order = setup.create_test_pickorders()[0]
        video_record_log = setup.create_test_videorecordlog("video-1", pick_order)
        url = VideoRecordLog.get_signed_video_url(video_record_log)
        # print(url)
        # https://cloud.dobybot.com/video-viewer/?t=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NjYxMTUzNDUsInN1YiI6NzgsInJlZiI6MjQ5fQ.PUVgUwHnxBZN1WwSNqJ_CpHWyaYG8dCOQg7QmhVh460

    @tag("video-record-log-model")
    def test_get_bypass_virus_scan_url(self):
        pick_order = setup.create_test_pickorders()[0]
        video_record_log = setup.create_test_videorecordlog("video-1", pick_order)
        video_record_log.drive_file_id = "1Pe4nKI2IZx90Kar-GHtN4lyYWbQffiD_"
        result = video_record_log.get_drive_api_download_url()
        print(result)
