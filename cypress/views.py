from django.shortcuts import render
from django.apps import apps
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from core.tests import setup
from rest_framework.permissions import AllowAny
from rest_framework.authentication import TokenAuthentication
from companies.models import Company
from django.db import connections
from django.conf import settings
from django.contrib.auth.models import Group
from eula.models import LicenseAgreement
from users.models import User


class ResetDBCypress(APIView):
    permission_classes = [AllowAny]
    authentication_classes = []

    def post(self, request):
        cypress_token = settings.CYPRESS_TOKEN

        if not cypress_token:
            return Response(status=status.HTTP_404_NOT_FOUND)

        auth = request.headers["Authorization"]
        if auth != f"CYPRESS {cypress_token}":
            return Response("Invalid Token", status=status.HTTP_401_UNAUTHORIZED)

        tables = []
        all_models = apps.get_models()
        tables_to_exclude_from_deletion = [
            "auth_permission",
            "auth_group_permissions",
            "auth_group",
            "django_content_type",
            "users_user_groups",
            "users_user",
            "companies_company",
            "eula_licenseagreement",
        ]

        for model in all_models:
            print(model)
            if model._meta.db_table in tables_to_exclude_from_deletion:
                continue
            tables.append(model._meta.db_table)

        sql = f"TRUNCATE {', '.join(tables)}"
        with connections["cypress"].cursor() as cursor:
            cursor.execute(sql)

        models = [LicenseAgreement, User, Company, Group]
        for model in models:
            model.objects.using("cypress").all().delete()
        # LicenseAgreement.objects.using("cypress").all().delete()
        # User.objects.using("cypress").all().delete()
        # Company.objects.using("cypress").all().delete()

        setup.init()
        setup.create_record_only_company()
        setup.create_test_pickorders(limit=None)
        setup.create_test_user()
        setup.create_test_fixcase()
        # TODO setup data

        return Response({"response": "OK"}, status=status.HTTP_204_NO_CONTENT)
