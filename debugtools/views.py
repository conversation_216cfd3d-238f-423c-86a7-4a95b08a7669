from json import JSONDecodeError
import requests
from typing import Dict
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.response import Response

from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAdminUser

from companies.permissions import CanChangeSetting
from core.authentication import DobybotJWTAuthentication
# Create your views here.


class SendRequestAPI(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        config = serializers.JSONField()

    def post(self, request):
        validator = self.Validator(data={'config': request.data})
        validator.is_valid(raise_exception=True)
        config = validator.validated_data['config']

        try:
            response = self.send_request(config)
        except Exception as e:
            return Response({
                'status_code': None,
                'headers': None,
                'data': None,
                'error': str(e)
            })

        return Response({
            'status_code': response.status_code,
            'headers': response.headers,
            'data': self.get_response_data(response)
        })

    @staticmethod
    def send_request(config: Dict):
        url = config.get('url')
        method = config.get('method')
        headers = config.get('headers')
        body = config.get('json') or config.get('body') or config.get('data')
        params = config.get('params')
        response = requests.request(method, url, headers=headers, params=params, json=body)
        return response

    @staticmethod
    def get_response_data(response: requests.Response):
        if 'application/json' in response.headers['Content-Type']:
            try:
                return response.json()
            except JSONDecodeError:
                return response.text
        return response.text


class SendWebhookRequestAPI(SendRequestAPI):
    permission_classes = [CanChangeSetting]
    authentication_classes = [DobybotJWTAuthentication]
