{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["postgres://postgres:postgres@localhost/dobybot_uat\n"]}], "source": ["import os\n", "os.ch<PERSON>('..')\n", "\n", "import pandas as pd\n", "import psycopg2\n", "\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "print(os.getenv('DATABASE_URL'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mThe kernel failed to start due to the missing module 'imp'. Consider installing this module.\n", "\u001b[1;31mClick <a href='https://aka.ms/kernelFailuresMissingModule'>here</a> for more info."]}], "source": ["conn = psycopg2.connect(os.getenv('DATABASE_URL'))\n", "df = pd.read_sql_query('select order_json from picking_pickorder pp where pp.company_id = 4', conn)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["df['item_count'] = df['order_json'].apply(lambda x: len(x['list']))\n", "df['item_list'] = df['order_json'].apply(lambda x: ', '.join([y['name'] for y in x['list']]))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["26799"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df['item_count'].sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mThe kernel failed to start due to the missing module 'imp'. Consider installing this module.\n", "\u001b[1;31mClick <a href='https://aka.ms/kernelFailuresMissingModule'>here</a> for more info."]}], "source": ["import django\n", "import os\n", "\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"core.settings\")\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()"]}], "metadata": {"interpreter": {"hash": "cd30efeb5ec14a65921be75e3ef2e9a89333b08e5e9c7d2e746ada9180a424ef"}, "kernelspec": {"display_name": "Python 3.9.9 64-bit ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}