{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import django\n", "import os\n", "import sys\n", "os.ch<PERSON>('..')\n", "\n", "PROJECT_DIR = os.path.dirname(os.getcwd())\n", "sys.path.append(PROJECT_DIR)\n", "\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"core.settings\")\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from companies.models import Company\n", "\n", "for c in Company.objects.all():\n", "    c.set_setting('ETAX_ORDER_OPEN_DAYS', 10, commit=True)\n", "    print('Company', c.name, c.get_setting('ETAX_ORDER_OPEN_DAYS'))\n", "    "]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}