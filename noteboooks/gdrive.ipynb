{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import django\n", "import os\n", "import sys\n", "os.ch<PERSON>('..')\n", "\n", "PROJECT_DIR = os.path.dirname(os.getcwd())\n", "sys.path.append(PROJECT_DIR)\n", "\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"core.settings\")\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs',\n", " 'auth_uri': 'https://accounts.google.com/o/oauth2/auth',\n", " 'client_email': '<EMAIL>',\n", " 'client_id': '117332503021676297957',\n", " 'client_x509_cert_url': 'https://www.googleapis.com/robot/v1/metadata/x509/drivemanager%40dobybot1.iam.gserviceaccount.com',\n", " 'private_key': '-----BEGIN PRIVATE KEY-----\\n'\n", "                'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCoZlJpxuVMLZRu\\n'\n", "                'uKxwQx2yPaIzTkuxbjnczUuPZA7IcY7LBBnbJOvhrAsgwFP1p5mj/iUXf1t0oeD1\\n'\n", "                'U6z1F2XUA+cNzcUFBb8mQcxwz1gJTpvwJFSGpYICPtIykBP3+mxGGxc5XiQ9NanH\\n'\n", "                '497lzkxcfxapO3yCgQi7y8eiZe74QcbuFEdE0Wum3drlDxl2UI1pFLLlYyjaAvJC\\n'\n", "                'K9FK8YjrMY/9G5/LhxrXKcClsujnZPO0AjNd44jKaaOUPdyZJnGMxx543zuX8z5w\\n'\n", "                '/bVARkh0BDES/Q5wX6SyEsRyM7zngC7EeIyaf/GelQkHuunZGhjaINw3G3M5ZE8O\\n'\n", "                'jKVpmYwJAgMBAAECggEAATBBSKWQcg6oS4avvJqP3AaUqlhMlYcPF55hK1ca6GeD\\n'\n", "                's64I7gOuKSuTrEMZ5alHJ2HPZkXa5i8pflpKB6v+eB6X/4jPC0s69KqQ4iUSWEGr\\n'\n", "                'xaxgO89O9VIqUNA9XoF4fAWluYNkchVYm2iukWBfOVQpn9jwkGvClK+oKfhRqsch\\n'\n", "                'qWppgmX5VLLQD2K5Fk25vGuj21a9YNNcAtmlVrQxG3G2dDfEkpooMeB1B4Y0nmEv\\n'\n", "                'HDn7X5XsHofos2n13ITqxObb8DmNkqjwpfIRY7nvweawErakLipZIXhS5nBty9iD\\n'\n", "                '03RPUS/FzEVc3upekwB9zkrk+2d+lx/nIViVJU0NAQKBgQDefXsi3THG02r6DVn8\\n'\n", "                'LORam80RqTBq07xCzWosLO+oWpSUvQSfcZfElbesE37hnOXvwG+EGmjrU7SUo1CU\\n'\n", "                '1d4ibpUhcnsyRY6xhSqXMD0em+e3yVIjocQ06Om9ykIYQmN8nAq7dlrSftWXcibR\\n'\n", "                'cbnI2TkWyDC+CcN836Eg01C0eQKBgQDBw0h8u74M4DW5U1Nw/oMgkS+Q+YZmTLZk\\n'\n", "                'lZB3Pl5f/dIr2MyxjaGhgmUIjXX/StOlq65bvnHZW2MNi3WwNu73ZoAK9oLGH3wI\\n'\n", "                'HSrJYmWMFVZYMHoDFRXitrfD0R/uShir1BQFvdmsWKqqU823tZnellMak6AHWQzd\\n'\n", "                'mD0o7CYQEQKBgGqCy8Ye+ktivt3CMofkvdUarQt9YW5iuseIKwHG6VxBMMwbyPDD\\n'\n", "                'opI70lgQZTeAsxsZlsk5E21E/zgBDvyRfewh4EbI1W11myAkzgTe1QOoQz/aovXn\\n'\n", "                '5VQnBG8VOpr+fT4UPZcMy+HlUj9UKH0v4m7iftAHOj5f0jQforViNEqRAoGAYvbc\\n'\n", "                'EiFN64OkzPg7OQ41aS/ebJyVwbBSxEMD23O2fi7E5vI/cjxVnolcEyc1amQMf6SV\\n'\n", "                'VHSL76lWiTd+r7cPDLuEkJThrFGuZvKGxP8FwKuxQy/XUbW2TOpoJ5KrTWj4+uJC\\n'\n", "                'K8CQ6g8LdB2Dh94wLdj3ry5RYQ9hm9jC/CDJPBECgYBSguN8DiR9xFd9ezO/fRov\\n'\n", "                'tMswAAa+MJiX4NHo+k4MtoL3Q/JqcQrUsmPmELhjoJk1Cmb3CkJvFq5sz4Xxp9f1\\n'\n", "                'WPc/qlQ99p74hf2w1q9FkrC3/h5/BZe9mZdiZWnkZuRc4o7fiGc5tGT/gB9JEPV5\\n'\n", "                'cRngq3q2PYTSuHIxQIqN2Q==\\n'\n", "                '-----END PRIVATE KEY-----\\n',\n", " 'private_key_id': '5e4868a5b58d616dcce73766dfcd2fe73f2894c3',\n", " 'project_id': 'dobybot1',\n", " 'token_uri': 'https://oauth2.googleapis.com/token',\n", " 'type': 'service_account'}\n"]}], "source": ["from django.conf import settings\n", "from pprint import pprint\n", "pprint(settings.GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'files': [{'mimeType': 'application/vnd.google-apps.folder', 'parents': ['1dsZprWxpoQQpXrWnDv6QvO34Ae0xZZah'], 'id': '11WEwWEfEA3v-SZtFnkKbi9RsQ3xIVNey', 'name': '2024-06-12', 'createdTime': '2024-06-12T02:51:34.371Z'}]}\n"]}], "source": ["from services.google.drive import GoogleDriveService\n", "\n", "drive = GoogleDriveService()\n", "result = drive.list(driveId=\"0ALw-rYfVUFqaUk9PVA\", q=\"name = '2024-06-12'\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["~!!~AI9FV7QJsSHhAfjkrA2axN7aITjSjtGK_krHfImO1TOWHm9mzJldILzna5t4wHgwHtgqJatQMMt-7-aMaF5YvDmpDHL0tFKh288CdzM2VMWqW72FIWirK-h9IvQeQY5XKeleSITQ7Rqh1qvW9n-3ykGOInuH6fRifF9mVQpromR0nRzjFORCc3UhrMk-z6LYLuZgeiJi0voesmipmu1nKX_XwwlRRu0aQPSVqpFaKzGkZPaiciXHRPjYTxapaYJB3OM5luHp3BPbYxwgBdTV6mdyuOM6uacFfJD8FpxmGyZtVv95dvpNBOwXd8kkGkR9qr7yfhrOf4ocFZPEKT8ULvTUZ5Cadu0OajuuuKkf8sfjKwOOoSUEs9_hgpLEgwGp_M2pndqtP8Jz\n", "25\n", "~!!~AI9FV7QLovP1XoQyWNKS4mC2ztNa445zMjjyUe6UlCh2BX_-Oukup01daG9YintEQbEfFqwUxNqEp6cwvaYe-EFQ2L72ncuiYHYhIreuI7Pj9xo4ptOtkNcrexbxAaTEt6W80wTV4fYO6_CFP92w8rzC-M_hZDwDT8Z6C1W0ge9ZejzGJk13UtPDuBmK5X8pPpg19Rw2L6aa_UK3sSQtmQ68j-zZ2R9rr5MAQ9vECNRPFOAa622BNr8kxbcistAbJ-QyU-eI_Ds3XC90Hy074Zk804mxAEDgR3UFNMqsuKIN_piotOJZaJDpnkfpXG1lfw0QhSra6XDD7AsRMpTwU92286Hl34bwTUOQgeqn1s1lIwlTbpqr5J_mcRudgtDcO6vq-PXp2EKo\n", "50\n", "~!!~AI9FV7RRw9zsW5Dmmmqw5-Y59RKQ2qF0jKZv9o7P5Y5cFMi0JQOqeNS1HC_WqdYphsbU7-0qWhZDpt6NiQLuf6eCkjMTTvF8OAPIcGtL65cy8LkbCSQ1XCWgqhulO-LoJbStTJaDHatP6oet0HIcwX3YvouAO9VYhqvbpheNub34yYuneTdPJFuN533XHgoLZ2h-0izY5jTcbJjo4J3BQHjuRp4yrTASiAtIxmC3753OQqBO5FCJkDF4muIgbb-HmvihpTXStcN4MRPaJMFzIhGX3mN_cYBOAbiMq1FEiag-N2EkdOtTuTRbG2_phbrMo0ETxgbEcC2e-22uHLOZYqglYtIsJWBoZRg3-kqfpYQzwQy3piBiFnP2_auRbEbS9PNs4O4qllN3\n", "75\n", "~!!~AI9FV7SLkIbdza6IeVf4A4BTAX4g6R-EHgj7XI1KqyVuInko2A0QyFIF1bmzDB_M9c11JfOsyysxLCAM-hAFK6Ss_G0fEODQ7Nyp9UnD7KzC_us8TIN2QiyeZt_KVJfBnOhJPRU-lBeIDCqoetNsJL1BhyZCWwNT4PfdVVoGqLCVvb3kx-eQgzDy8o-UKLySkcIKfyB52_1qMjU6I0Yw_ooVNJ_rXuJ7NheX5wPdOkCKQYL2DtUYaDDjRF4x-cHVj7XYyCxqCmNzOIJWKLoJrcqHHU0kLgdhMw5pukm3JcZ8jepycJCjdl2Z72J2u5RJfMeE1rUu5EyvCVVfhxNB-3cgID3v2hiaNQGCXVEQb0s97kyRoAEnbFqPmobPqxyEz8MuODpFSwzS\n", "100\n", "~!!~AI9FV7RB6qHmSPOVldyiNMrxrHRid2yn7daaw5RCAJOuMj-Kfg4-vhYQlBk5uxBFcm_uglTLlA46ZK2B_pRpnJP1MMXWpWMs0-mgOa6JDP2Qiqbtw4LRFZ2Wyd4X95GYVNqLq0PMZdHPV6j3RlQZG2tg7RDbp0_9JHmae8EiTPBTD9tkn8OPkJ-TiR_X9j1ZuydYOAhlPfLyNgtvS3LhJbWCY_8H9kXafSi4zXZkLetc7fKRiH9eQM5bxCPd3Bk599uQJ2gv_POk-wEEURMJqTuhZSk9HgO_YzlU1mcU-FYBv0mcCukwP5m66zInSpNJkoUWGr-XuKIXJsw6Rxj_N1rL-fsUPChzwv9AAI6JLezQ3iUEpxqZ4iA-VIEiwufQ5PTFvEJ2MrjY\n", "125\n", "~!!~AI9FV7TDgWEMSHqpa-_6jwvARPfPvJvSC9PzKTI_lomuJfwTL2O8QJzimE9UaELvH1M9YNnW2nJOf19b2zJ6Axey8Q8Nxe50senVpc9AqMjy_i4zzKwbPLvKqeabtmpHvXI1vKRGemLTOsB5Lbkrs5xttJHXh1UzYu-5YxOfGaW6gcO_Kgalzl3IegOgGcGV26qh28RNHM3KZ4K1fAjvLYjJGjOGN5j2KRKEbg9o-z0dViEs_18-1MAJCkUFb7ryjzPwQMQvpjaBT1S7pZJeQdUBOFZNRSfDVTbq4T4faO0Eu0meJZ8rMHqcXWVjDcw63JZT23Eu14QhcuTcsQdHNkoV5nKWBoxAFhpQmecIRgxRxQncVCrxcJzIx7a2ZqPBkMj0oxvXg-as\n", "150\n", "~!!~AI9FV7REwWvTVTi6J05XBZ9wPYBCs101iLmMmxSEcp8zuR79Z79eiiMuDe5Fx440kbp3TYdmQrx3iDx4ueJPg6K6S6OyEDIbB18RzD5-5k1fJhyMBj0l-X9b4VBlcEb4a72a3vd598tFsbF7EJoys10129irB9rksOzmWt5fmlRmSXQhXYU03veYxMsK4Nbvx8e5qMkxwAZLZXHrNhh653nfo_YWX6bj6IpkigzY3jGKH1qzyaqunptGrgGGrnFyVSGWJ2ga5jz7C5vrmXRRTxguIPfwwmt9hTiUELvEb396M6PHTMTZfbTleniZbu-eC2HLY6R34D06yxNiBGPKS0ma8Ev13Q3K2nMd8HNvdCw3r1N2KON51_iJ5EkasiTXmNBGM4WB_5zG\n", "175\n", "~!!~AI9FV7RpF4O2NGDzhZ-AyiV8pBPncMn5DpBS4tlomC-Fq7D5mgxBSR79vYHkc1xtxNFhu_ZA1b9hiUG8AM_mQaPv6Wi015ZXXa_LLQW7sGWlVY58Hskf3fekNer1ZnMHSCTQvxpiZC8oklQwJMCTDMOSXtRWnqcp-Yp_EXesvlq7PVCiYlO8XLPOF_GGWJkx27D9lDO_AHqE0BnohMU6fw5-aPOQBhFrYXWKrah6iXzqQmWGASLp3kio-O2rIWtz4MGQUtdDcyZhNBGsxeju1a5eHsq6WAlrV10mZWudJEaSDrM7wp4u7X-LwNeIw9luwMmYEeDkp_7MhlJoR4PQAY4XqefqQW-79zfpCXDUrZH8oLyMTrbtrP3I8u5FaPD0Z5DJhoUeQipM\n", "200\n", "~!!~AI9FV7Rc0j8OYkplxycL2S21K7Xm4fpYcJbYURgZqeB_AHQihniT5U1-MYtK5OoWFS-Bpc4EW9oXZYPNtrqQUpSmtBfEDkVuhyAiOfTmFw-NZVqS5JBptGR7EsYB-fYIRkfaTsW3AfDPOFVxhWgXDhzTe6o71JatHsfSxKv5f12-p2JQ0ftzTsO4AOWQvKEbA1v3ZQBVUCRDcp2l8TDrBwPZQ2rZwSI1ZPKiPpb0gGupKOT5FvkxmUVSIUDoRgCJcEOhtN7x_BhasAKV0t9dyAOfjmpt-bc83LhUm97QwolYPSy3rlTNnhZBoRVIX0l323GME94qB4tlgF3Ft17ZZShHUza5R7Lx-igD8GKAE5hKHbzBlISOWWy1nqFU1gCMTsp4yY9edrVW\n", "225\n", "~!!~AI9FV7S2XjNE357MoLvKOsrkMzEJ5xQwyFUVn2g5nwUTP6l8xPRB3St2bAjnS51w4VU7spWUiML9lrYWEvuqir2SWQ3n6hf3zKjtPt4SwrUjX8Xp3d1r4N6vl8Le3vmQfDjca3o1mwXnZVKdT7PS8HPqo_DqJS_TFgc5jT6hVXNavRm9q0DhDUETsT5hLotBp0tXix0i_kHK7WKMHSxXXcn8HtW_YeWE7aoNeTHESUPO65OQKOgW87wCpFgnQvE1-LQsEtwL1fOyi2LWCNml6qTyV6nXksDaxawG83Y9mp6SXPBdwIpQUey3kVyCSsOiOncrIPJ5X8MSbgwrKsvTqF6Eqjn7C_pVNI56oIoh-CFtLIbgMVeNRjYoOGYRx-X4rt-yPIh3UCcQ\n", "250\n", "~!!~AI9FV7RS43Yx359tiggDvqvalsOL5CtO_3HCQYcV-vBPONG5HVmeNVGHI-0UBBc2_cGRIu_POxbausReYeeI-ykjHJ7puvpbAaYoKLPZa1yqAW_wmGN9Lvgttx3KzkvkjDvba93pCwpeRAStSa-zwg6rjjKa4pfU9RzVxzqlq_kbGSr6_aiRjuf5bGldJJV9jS3aY-ROGbwJrW8btlqoJ5pK8KJOyxX5xYZTdD3LsYtS6ivxA7BO-OuSPl_RsjHqhOS6lIT0TCd604XUf8jVtiQLZM2lepM2R3DoGvh5DIzM5LlOyWaCX3nLvtscoKkACDtEPV_swq_qsKEbfpmWk3-o-tG4nNiYN-jO63IRNIBtcquooSJ5fCQyiL5dKbpNzZjac_t15Psb\n", "275\n", "~!!~AI9FV7SXeayV9HlAlPRMmPg3PPiVhb3mZf3tGjs3-f5GLOLo1r2j3j_IzzJk38MRP8KwwIsD3GxOXZH1lwWYkAIreUvdYniCnpcu8b39jwvjAqSCBG8dZzc57UKJ4vtxS9Pn2yN5y6mXEIOgN9JVlfVa8W3d8ImaNLf8GCn7Dlbhf4l8iPryXhOcNMVkjJdnW20wgeqyapDBXuynHazErelYXUy8eH1Xep-CbP3owyBCKDK_gob4APhy-CvgNSiKBFuZMPE-Mwax-3ZfYWfBnRvksUJjJBTGhGKVfNHKYbFf3rcwuotCEMkWDFb7soKnO8S3qIoMpFp8noEZ8CBsDSvYNxu-Eoulx1zgcN3xE_2eOC682xQ3VWUdec04fY_7ckXHYKD5Oaj4\n", "300\n", "~!!~AI9FV7Qr6hJDMOQQFiXMmoAF5rlDmUCAgHUq9J7hPupulfkLqaFW_ylqdAKRUcw1OZwtzyiLm4GweK4Yi8ZAYJKq3V4ngfbxSTUe9Ozdcqz2rNnImJnojGqggQohurtpLz8nKCPNPyy1aMC8tmZ8i_zeJM1-vM1HhJBIjalsSeaT2bG_SIlOmhYAWuFWDjlTrG1pNpecAFWPz7dGPP-NIupmL9ivyk0MxoM2y5TkNUT-VHq_FVeQwKxRhVQRfPiETLGXkBiIrs531vAvHp3lkMHyfZeB5t_9WvcxrQ_pdCFEjuP2oGPFsmKg5RrL8NZssEb17Zq9hO22CPBoB3-BmANqeN4-DaXRX7p2-sBBPumxN3Np0rcoB8OsaOQsc3nuKntvVTxl08aq\n", "325\n", "~!!~AI9FV7TVf51eM0Fn9KPgzU64ntVm-U8om04g5ADy6CU6uND4YDdys7LLyfF2ubcIWhjM-rQOZwln88EYramR2GFsKB8i-DI-lndbLtjHRdzACA0S7aNoaMDezKbieGpTI91TOC7m-IARmgBXOGWCBFvapAiQQ-DfdN6-92TZq7sUdFanJzTk_KcbTP5wNgkXDPqfvXJQzjc_yb26t2q_cQJwSSgeIT6Mu8SJ0-2kaka-5Zd81WmIJ05CQhuLGtK9VLbG_9IeIvtPNkfdZYtIgXmkrVYG4UmY9j2pQ1NeD5y7q5Xdkw7qa8SKFnx7v2ECP6xhYPCJk27ZC46zDxb-IrCd3OsCDW_J-3tEoyh6uuH_kCwV_Gjh7paNYLXlaqtJOW91kvVX_Leo\n", "350\n", "~!!~AI9FV7TkqZmUayl9tnkvKdF4B0-NW8Kcv_pQ9s-eGzSHdU-pui2DotD-7iaFbHR-qr__h9qyYH3D8Fbzm6mYe3RwQkxNBCKFuy_Ju3inh6Aly9Lis3rCifd9SRMQiPrsB-BjnifNsHqvv8mkVkceGJMj7mN-3GPMLdl7bIggeYd8Jl6i1msNbRRdI0UwPdPgBmQWZ73zfmV1CoYC15gChvoiiTjtpyxFoI3yu0E84Zf9UwPCVROT8sFhN9aF5_-BB67-Zhgi_BOCzqfems3tixzR7vYu8jw_jrqwCBBfBLiqxesBTPRchBIpI3_hkuAltJQ6CoV1mzqeLoGBd4EzJoQ4hhESJD8Kh1zmo1H0r9nPdquIpxhL7qycCpX3YC-8CCYCWfqzSeXr\n", "375\n", "~!!~AI9FV7SUk5MZOMINGSHEG94tyVrFm3O_PXWlJH1X_ipra75D8AmcsyDK2GrBvOmlxahe23TOnfjST02E7B7ceyRkd7QrB5daPZojpecIccGgHLj27CK573SbKM2zhOz3ow4g3JwbIvPan13QNcu2MWFfGpmC4nAXp_ziqPcDcwaD8kixpwPnmHdBStg5OWhnQOmVOVpcPDASsIorVucRYsZPRkCT7I5x6tHAgXfY3PcECLzEFswDq3MpjKUpxt-ELpYAVd7ajTBwIgX6HygspfhNNZrQbWDtYo5H6Qg637FsTG2Drx13SoxZNeJFv8B2dgsMeiR4ZcUHvuWGxPbPQgBVv95oZKKSpVBCrlTSjuTiJ3kJJ0AlJ-ZJIrfS5GWle3Ao34mTgePE\n", "400\n", "~!!~AI9FV7Tfd_1M9lqsgGXhKe-aGX9aO7GpNtt8znbbCM1Uys7F_rwTrbBFLsiwaEOTxkYOKUcow_IaYbmr14ykIX0UbiCuALwWOPv2kDQO6lQlCWl0K057Bm-21I6_CLs7rVX4ssL7UMM85lrFZc53mqzrwNssA6iqdZqkmiXCQU7wAwOXgjyhbBuLmWl0jvYRTWadEskL8NLY5_BeS-kk1CORkwJG7ZnlW5J6jsBPdSCV2IfQwBlo_ak2SxtweigRjR01dhKkgD93K5E5L9zJx12UVMAHqcBArkm0RBTaFWd4RtIUvJozgNdgexBixDY5MwIp8Te0zA6BtiQ7Ka-vO6SDVDY9jGJgp_PamGZRCIo8lSk474RS_qBtjnvS6ErA9dOGDMHbYYs8\n", "425\n", "~!!~AI9FV7S_68rbaT2Xb_Ytb73hYuvTN_MgU6KBeqIoB9hqWLEeXhH_WayUw5oGwhgsy1vO7njgE3KNSciq9NMbW3lgmMO3kYDTudOFABcww5rkB8wGI09KD-m2TSTzXV4tdTtOfKKpSyzQcuxq04x-aa89EhAvOZiFlk9_q-yHnrxKf7qIn_v4_9EwsvdqSW7g2Ft6Uq4gmnANv4Vb9ld-233atSRjU4xtIVAUyNZI3dfq4FN7poNOvq5vrNkpZEtqNF-wwdwWRgAyr8SoH1PMYBL8K7NlT2K0nX35Jh_mrndNZPGELL5tlGn5YAXV3wG9aYofKRqzz7TyBwoUmb1tA0rfhvr7w1HcrFsbibVHHUMiy-j-H25Jp637DGCeCieSA7j743IO-45Y\n", "450\n", "~!!~AI9FV7TLIug0IR5kgUEMRxP91wESTpIBJBsRTleNWJM4Dv4uu20SkKtlJfG6Etk3POvLYEchecgUvan834rHuIsMqNZiPm6b1-J1RjzHTG_pqYXwsYbi-_5JPpaX2c1CriQ8Uv3m8bRffiR22o_pwq8m6a7w62IvricnqMmZDwRy2WeJb51AYefKG7XX3rHE0UEZLWBQIt6IRlOMS2fwoFZcp4p3Ez5Ka7Lqy-P7Rfn01mx0-tTLiMKfnZIb1roTGuXPFIfG-xNDY0ySsv41Sm6eXeqXpuVv9tToCh0eXm0nqUGUVqwIY5uKvgiLLRTiQyhLZGpEjuqIAXVek648xJ9JzkCNyo0AekGunyeoUyzprSxmhtOlv7MUknsz7z9hJG2s5Huul-52\n", "475\n", "~!!~AI9FV7SpL4fWFVoC8Zr0DwiTLT_sRw1oDRgZL5CI522jG2XlKlWiVPM-H4eW2-mVGABFNa5wLPFExzdVIGD34WAQOQQ3oYj7X8hStXszLjFxNzb6A5SwQ9LbIf4NmZESiZQoXVLbAlscORzmGuIEo1oiy6MS8KECmtSeJbri85lE8ZI6-qEDQCAr_B5OIG-DRgXj8q83thb3oGpG5Iqaw9KLhAliEPOu6yu9DhXm6b5p-MYEJSP0a24JHij1GpzSUmvUVRIWXCUVVLEnbPGkWhcDtHuvIUrJRW_Skif3PybjcCZBBVa4V0A7uGsw9rnRi-3oIFSFhJScPo7idpPQAv8fzb-7uMIZT6h8hRrJRv3BEe6sGRFRyj-piFoncBV92-i44hQWhJlb\n", "500\n", "~!!~AI9FV7TstpjsnA0cuqtf7Vs9AjAO9P_nPL8ANxNncNIEJYJy9iT1wNfYyUNlHt4Tr8wNoL_Zq2PiRvLlKqbjfgGRfHIOOFyGG7M_r7xEndZShe2P5dgpOFJsgYHXnZef0O5NnCDQrxX3sw5770qJWYrbUjtr96MguYu9dukOlGeNYnQY7Iu32Vzzm7vVKFo30jzPjCCxLbhywNwDkfkNRnmW-xRcOfUDe0q3ZSChLpB1FBlaNTvEP24cAPVHRblpaeotRpZUVip-zPPKf99QbcWm3zTyf4iJh5k0tB247miiBWFCWHKamBWRJHPoenCUSt2HYARLN3ncG0uRcGMwCNO8UAQZrZX9o_kg-9kRGeSH_z2OhQhcSvVLv2hOZ4-cepE46T_oP4NI\n", "525\n", "~!!~AI9FV7Rv7alr8bQxurTZ9JleIGVbUny5lY8VhH7urJd16uZ1U6VgIioC3-tOIbmZHa9yAx_s7TOg4TEQiuPT3rMlRD1pWxj-PoCUO-WmWUTmQpm5xWMxKt7pQKNCveSA56fQuLV6ufUr2BUVpNxgyuqRmOTWSIhB4a76GDBOnMeus_VsQN3HShR0JkdYAtaS9xbB8KVeA-jaKf_Ta2Dou2ERJ33wN_puoDJCLd25JxZ4r5v3tRUj4vU_8eJlfBiBHeIFH5QTERlAqVU5xdYWFKYihIP7bo8Xv-AQmfIFMl6-FBCyb1yY2oQwKsUAEfVA2rxgu9ByoUt8lwEBYqjpbyut_3Ur1SI19QZ2tuSZjaWRiPTVyXx9rJCWzc1It7HL9eD_S06hM8IZ\n", "550\n", "~!!~AI9FV7TkGsAilaSM674-ooJb_JLV_XiYruX1863LW4ItGtj6SYtCWtuD9uJbS2uv02qI60uQiBEkaUkr91ru8bI5aAnxHoUZGd-Mpf25WwEUgEsDMUqUBv3-7p238r5G_hXeutZl012CSQdx3Fr5mQZcUzw2wMZwD13svTLK1vAka3i175-lHjKa3mP6kSMWaKtVnPWJcjlPKi3LK_6GycQiQpRuJdhPzX3MMEZHrLIhmyMg0TVwWjT3Mw4G36T7xuzxbMccGztvCzUmZ-yOYByVrf27UWqKsc5hA4XWaYcLoLgWyvAy4GmrHeosTHXZY1AXdvu7pJlRlmkyHx5lVyly82oB_d48F5aq_nSY0YZHNq_hxg1DfyjOJWs3RtNi8y7yqtxNJF-f\n", "575\n", "None\n", "576\n"]}], "source": ["results = []\n", "page_token = None\n", "while True:\n", "    result = drive.list(\n", "        driveId=\"0ALw-rYfVUFqaUk9PVA\", \n", "        q=\"'11WEwWEfEA3v-SZtFnkKbi9RsQ3xIVNey' in parents\", \n", "        pageToken=page_token,)\n", "    results.extend(result.get(\"files\", []))\n", "    page_token = result.get(\"nextPageToken\")\n", "\n", "    print(page_token)\n", "    print(len(results))\n", "\n", "    if not page_token:\n", "        break\n", "# 11WEwWEfEA3v-SZtFnkKbi9RsQ3xIVNey"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Copy of TH244973020153Y.mp4',\n", " 'Copy of TH245269291316B.mp4',\n", " 'Copy of TH244878002471S.mp4',\n", " 'Copy of TH244673873178T.mp4',\n", " 'Copy of TH244905355010I.mp4',\n", " 'Copy of TH244937491804J.mp4',\n", " 'Copy of TH244844987652V.mp4',\n", " 'Copy of TH245220320112U.mp4',\n", " 'Copy of TH244935980549M.mp4',\n", " 'Copy of TH245224505390G.mp4',\n", " 'Copy of TH245383022508X.mp4',\n", " 'Copy of TH245378381844P.mp4',\n", " 'Copy of TH245563639323Q.mp4',\n", " 'Copy of TH245563253900F.mp4',\n", " 'Copy of TH245442323448Z.mp4',\n", " 'Copy of TH245359652255Q.mp4',\n", " 'Copy of TH245591348061W.mp4',\n", " 'Copy of TH245328536080L.mp4',\n", " 'Copy of TH245451017582I.mp4',\n", " 'Copy of TH245588187092V.mp4',\n", " 'Copy of TH246019339849N.mp4',\n", " 'Copy of TH245711278952K.mp4',\n", " 'Copy of TH245843860184Q.mp4',\n", " 'Copy of TH246033618684W.mp4',\n", " 'Copy of TH246182213837O.mp4',\n", " 'Copy of TH245841767297X.mp4',\n", " 'Copy of TH245777378915P.mp4',\n", " 'Copy of TH245657589415Y.mp4',\n", " 'Copy of TH245766384803Q.mp4',\n", " 'Copy of TH245804732116L.mp4',\n", " 'Copy of TH246361828803S.mp4',\n", " 'Copy of TH246537879048O.mp4',\n", " 'Copy of TH246319079154Q.mp4',\n", " 'Copy of TH246227244765G.mp4',\n", " 'Copy of TH246527938483Y.mp4',\n", " 'Copy of TH246242911041V.mp4',\n", " 'Copy of TH246437410254J.mp4',\n", " 'Copy of TH246378767540N.mp4',\n", " 'Copy of TH246640002337P.mp4',\n", " 'Copy of TH246588943817E.mp4',\n", " 'Copy of TH246922230007Q.mp4',\n", " 'Copy of TH246809914858Z.mp4',\n", " 'Copy of TH247032016169P.mp4',\n", " 'Copy of TH246884223939M.mp4',\n", " 'Copy of TH247130117776M.mp4',\n", " 'Copy of TH247037076869L.mp4',\n", " 'Copy of TH246921265063H.mp4',\n", " 'Copy of TH247012425716D.mp4',\n", " 'Copy of TH246738545273Y.mp4',\n", " 'Copy of TH246805593554O.mp4',\n", " 'Copy of TH247383668928O.mp4',\n", " 'Copy of TH247538008991Q.mp4',\n", " 'Copy of TH247234038841R.mp4',\n", " 'Copy of TH247336610418N.mp4',\n", " 'Copy of TH247164688315S.mp4',\n", " 'Copy of TH247444866265N.mp4',\n", " 'Copy of TH247206021492N.mp4',\n", " 'Copy of TH247199323697P.mp4',\n", " 'Copy of TH247206857307P.mp4',\n", " 'Copy of TH247545007875R.mp4',\n", " 'Copy of TH247705834306P.mp4',\n", " 'Copy of TH247763588010V.mp4',\n", " 'Copy of TH247822757566Z.mp4',\n", " 'Copy of TH247984211251K.mp4',\n", " 'Copy of TH248004668969O.mp4',\n", " 'Copy of TH247736853524V.mp4',\n", " 'Copy of TH247738193087Y.mp4',\n", " 'Copy of TH247797087667R.mp4',\n", " 'Copy of TH247886803258N.mp4',\n", " 'Copy of TH247690236331C.mp4',\n", " 'Copy of TH248156995752L.mp4',\n", " 'Copy of TH248015681203U.mp4',\n", " 'Copy of TH248308066962S.mp4',\n", " 'Copy of TH248035437857X.mp4',\n", " 'Copy of TH248277171510J.mp4',\n", " 'Copy of TH248036199813R.mp4',\n", " 'Copy of TH248227954265T.mp4',\n", " 'Copy of TH248133009182T.mp4',\n", " 'Copy of TH248501637167A.mp4',\n", " 'Copy of TH248529038971I.mp4',\n", " 'Copy of TH248832936677S.mp4',\n", " 'Copy of TH249091689741N.mp4',\n", " 'Copy of TH249221724437M.mp4',\n", " 'Copy of TH249204012679Y.mp4',\n", " 'Copy of TH248837195552W.mp4',\n", " 'Copy of TH248904731053L.mp4',\n", " 'Copy of TH248721470171D.mp4',\n", " 'Copy of TH248877652842K.mp4',\n", " 'Copy of TH248832447770R.mp4',\n", " 'Copy of TH248874834388W.mp4',\n", " 'Copy of TH249325502369K.mp4',\n", " 'Copy of TH249548797921Z.mp4',\n", " 'Copy of TH249444449096R.mp4',\n", " 'Copy of TH249602933438N.mp4',\n", " 'Copy of TH249226124467U.mp4',\n", " 'Copy of TH249509895070P.mp4',\n", " 'Copy of TH249333530745Q.mp4',\n", " 'Copy of TH249543768123Q.mp4',\n", " 'Copy of TH249804809943I.mp4',\n", " 'Copy of TH249294628964N.mp4',\n", " 'Copy of TH249838591596N.mp4',\n", " 'Copy of TH2407855814189.mp4',\n", " 'Copy of TH249919452589T.mp4',\n", " 'Copy of TH2400689562422.mp4',\n", " 'Copy of TH2401047472494.mp4',\n", " 'Copy of TH2405374331764.mp4',\n", " 'Copy of TH2402130098985.mp4',\n", " 'Copy of TH2407427968566.mp4',\n", " 'Copy of TH2409815522764.mp4',\n", " 'Copy of TH2405664409170.mp4',\n", " 'Copy of TH2420367569469.mp4',\n", " 'Copy of TH2410007128516.mp4',\n", " 'Copy of TH2423300319777.mp4',\n", " 'Copy of TH2416279661095.mp4',\n", " 'Copy of TH2423364385188.mp4',\n", " 'Copy of TH2411107238731.mp4',\n", " 'Copy of TH2418112856220.mp4',\n", " 'Copy of TH2410698397534.mp4',\n", " 'Copy of TH2422156679249.mp4',\n", " 'Copy of TH2417194069176.mp4',\n", " 'Copy of TH2428702859831.mp4',\n", " 'Copy of TH2434783904141.mp4',\n", " 'Copy of TH2425710660987.mp4',\n", " 'Copy of TH2438487816523.mp4',\n", " 'Copy of TH2446488196749.mp4',\n", " 'Copy of TH2426505862970.mp4',\n", " 'Copy of TH2436092237144.mp4',\n", " 'Copy of TH2439465782118.mp4',\n", " 'Copy of TH2446606592491.mp4',\n", " 'Copy of TH2432681460395.mp4',\n", " 'Copy of TH2449024500642.mp4',\n", " 'Copy of TH2454617641545.webm',\n", " 'Copy of TH2458054183334.mp4',\n", " 'Copy of TH2453332773008.mp4',\n", " 'Copy of TH2467349076920.mp4',\n", " 'Copy of TH2461005366952.mp4',\n", " 'Copy of TH2465457859291.mp4',\n", " 'Copy of TH2454628046023.mp4',\n", " 'Copy of TH2465576422573.mp4',\n", " 'Copy of TH2455792055185.mp4',\n", " 'Copy of TH2473182187216.mp4',\n", " 'Copy of TH2477838121412.mp4',\n", " 'Copy of TH2479872848380.mp4',\n", " 'Copy of TH2479897345952.mp4',\n", " 'Copy of TH2479759688351.mp4',\n", " 'Copy of TH2470937706270.mp4',\n", " 'Copy of TH2476199719333.mp4',\n", " 'Copy of TH2474289121203.mp4',\n", " 'Copy of TH2473828710098.mp4',\n", " 'Copy of TH2471987457514.mp4',\n", " 'Copy of TH2492744521386.mp4',\n", " 'Copy of TH2482855711452.mp4',\n", " 'Copy of TH2495363181476.mp4',\n", " 'Copy of TH2481270226957.mp4',\n", " 'Copy of TH2490476688239.mp4',\n", " 'Copy of TH2497064470400.mp4',\n", " 'Copy of TH2485659600960.mp4',\n", " 'Copy of THT0101ZF2697Z.mp4',\n", " 'Copy of TH2480096204608.mp4',\n", " 'Copy of THT0101ZF7YT7Z.mp4',\n", " 'Copy of THT0304ZEUFV7Z.mp4',\n", " 'Copy of THT0117ZFD6M8Z.mp4',\n", " 'Copy of THT1501ZDDZ87Z.mp4',\n", " 'Copy of THT0506ZFAQB2Z.mp4',\n", " 'Copy of THT1204ZEQ191Z.mp4',\n", " 'Copy of THT0511ZDHZX9Z.mp4',\n", " 'Copy of THT0149ZDA0G6Z.mp4',\n", " 'Copy of THT0109ZFQFW7Z.mp4',\n", " 'Copy of THT0901ZE1UZ9Z.mp4',\n", " 'Copy of THT0140ZD1Q72Z.mp4',\n", " 'Copy of THT4101ZEKA81Z.mp4',\n", " 'Copy of THT5701ZDBZ43Z.mp4',\n", " 'Copy of THT1501ZF8TC2Z.mp4',\n", " 'Copy of THT3701ZFFGC7Z.mp4',\n", " 'Copy of THT1805ZE79T6Z.mp4',\n", " 'Copy of THT7111ZCQRC5Z.mp4',\n", " 'Copy of THT1507ZDTZB5Z.mp4',\n", " 'Copy of THT1601ZDM0X2Z.mp4',\n", " 'Copy of THT2101ZEZ2C9Z.mp4',\n", " 'Copy of THT2606ZECA68Z.mp4',\n", " 'TH249548797921Z.mp4',\n", " 'TH2479897345952.mp4',\n", " 'TH2465457859291.mp4',\n", " 'TH246640002337P.mp4',\n", " 'TH246537879048O.mp4',\n", " 'TH2497064470400.mp4',\n", " 'TH2473182187216.mp4',\n", " 'TH2446606592491.mp4',\n", " 'TH2482855711452.mp4',\n", " 'TH240522318440Z.webm',\n", " 'TH2454617641545.webm',\n", " 'TH246182213837O.mp4',\n", " 'TH243356036758Y.mp4',\n", " 'TH248529038971I.mp4',\n", " 'TH247690236331C.mp4',\n", " 'TH2410007128516.mp4',\n", " 'TH247012425716D.mp4',\n", " 'TH243712531863B.mp4',\n", " 'TH2416279661095.mp4',\n", " 'TH2455792055185.mp4',\n", " 'TH2439465782118.mp4',\n", " 'TH245269291316B.mp4',\n", " 'TH2490476688239.mp4',\n", " 'TH2407427968566.mp4',\n", " 'TH2417194069176.mp4',\n", " 'TH2420367569469.mp4',\n", " 'TH2423364385188.mp4',\n", " 'TH243101918178C.mp4',\n", " 'TH2465576422573.mp4',\n", " 'TH2425710660987.mp4',\n", " 'TH2436092237144.mp4',\n", " 'TH2449024500642.mp4',\n", " 'TH2481270226957.mp4',\n", " 'TH2477838121412.mp4',\n", " 'TH240360192159C.mp4',\n", " 'TH2401047472494.mp4',\n", " 'TH2492744521386.mp4',\n", " 'TH242379421370A.mp4',\n", " 'TH2434783904141.mp4',\n", " 'TH241781660855B.mp4',\n", " 'TH2480096204608.mp4',\n", " 'TH2467349076920.mp4',\n", " 'TH240473130035A.mp4',\n", " 'TH2446488196749.mp4',\n", " 'TH248721470171D.mp4',\n", " 'TH246588943817E.mp4',\n", " 'TH240157444996U.mp4',\n", " 'TH248832936677S.mp4',\n", " 'TH249919452589T.mp4',\n", " 'TH244473979458O.mp4',\n", " 'TH248004668969O.mp4',\n", " 'TH249091689741N.mp4',\n", " 'TH245777378915P.mp4',\n", " 'TH249444449096R.mp4',\n", " 'TH246227244765G.mp4',\n", " 'TH244231233073R.mp4',\n", " 'TH242787672630L.mp4',\n", " 'TH246033618684W.mp4',\n", " 'TH242565101244X.mp4',\n", " 'TH244070622759U.mp4',\n", " 'TH244468816255Q.mp4',\n", " 'TH244035084601K.mp4',\n", " 'TH242976248188W.mp4',\n", " 'TH240379787748O.mp4',\n", " 'TH246437410254J.mp4',\n", " 'TH244401943579H.mp4',\n", " 'TH242435827238M.mp4',\n", " 'TH241682371863R.mp4',\n", " 'TH244118300014O.mp4',\n", " 'TH248904731053L.mp4',\n", " 'TH240512403485P.mp4',\n", " 'TH245766384803Q.mp4',\n", " 'TH245224505390G.mp4',\n", " 'TH244905355010I.mp4',\n", " 'TH244246219950S.mp4',\n", " 'TH242686247851V.mp4',\n", " 'TH243205873723O.mp4',\n", " 'TH247130117776M.mp4',\n", " 'TH244937491804J.mp4',\n", " 'TH243950553059I.mp4',\n", " 'TH245711278952K.mp4',\n", " 'TH241141365662M.mp4',\n", " 'TH248837195552W.mp4',\n", " 'TH243978433895K.mp4',\n", " 'TH241230129227R.mp4',\n", " 'TH246884223939M.mp4',\n", " 'TH2485659600960.mp4',\n", " 'TH240014895275U.mp4',\n", " 'TH241390414641V.mp4',\n", " 'TH242572667500T.mp4',\n", " 'TH246319079154Q.mp4',\n", " 'TH241367281254N.mp4',\n", " 'TH243341956876L.mp4',\n", " 'TH248227954265T.mp4',\n", " 'TH241324608831X.mp4',\n", " 'TH249838591596N.mp4',\n", " 'TH2400689562422.mp4',\n", " 'TH245843860184Q.mp4',\n", " 'TH245378381844P.mp4',\n", " 'TH240289848984V.mp4',\n", " 'TH242177354673M.mp4',\n", " 'TH241215967695R.mp4',\n", " 'TH249294628964N.mp4',\n", " 'TH246242911041V.mp4',\n", " 'TH243687969755R.mp4',\n", " 'TH241840279232R.mp4',\n", " 'TH247763588010V.mp4',\n", " 'TH248308066962S.mp4',\n", " 'TH246527938483Y.mp4',\n", " 'TH242329753596R.mp4',\n", " 'TH247736853524V.mp4',\n", " 'TH247336610418N.mp4',\n", " 'TH243118379859L.mp4',\n", " 'TH244973020153Y.mp4',\n", " 'TH241997890860M.mp4',\n", " 'TH249226124467U.mp4',\n", " 'TH248035437857X.mp4',\n", " 'TH249602933438N.mp4',\n", " 'TH2426505862970.mp4',\n", " 'TH245657589415Y.mp4',\n", " 'TH240769090831Y.mp4',\n", " 'TH247164688315S.mp4',\n", " 'TH243564837871P.mp4',\n", " 'TH240343819527X.mp4',\n", " 'TH247705834306P.mp4',\n", " 'TH243647627767V.mp4',\n", " 'TH249509895070P.mp4',\n", " 'TH247738193087Y.mp4',\n", " 'TH247444866265N.mp4',\n", " 'TH245588187092V.mp4',\n", " 'TH245591348061W.mp4',\n", " 'TH248832447770R.mp4',\n", " 'TH242490958235S.mp4',\n", " 'TH248877652842K.mp4',\n", " 'TH243170939512P.mp4',\n", " 'TH248015681203U.mp4',\n", " 'TH242081774724Q.mp4',\n", " 'TH248133009182T.mp4',\n", " 'TH242349089632O.mp4',\n", " 'TH243271170041P.mp4',\n", " 'TH244214584858O.mp4',\n", " 'TH244673873178T.mp4',\n", " 'TH244607698936J.mp4',\n", " 'TH241909194772S.mp4',\n", " 'TH240896617792Q.mp4',\n", " 'TH241275138554S.mp4',\n", " 'TH242262271001V.mp4',\n", " 'TH244561047365X.mp4',\n", " 'TH242990462770P.mp4',\n", " 'TH2410698397534.mp4',\n", " 'TH245563639323Q.mp4',\n", " 'TH246378767540N.mp4',\n", " 'TH242528029752L.mp4',\n", " 'TH249325502369K.mp4',\n", " 'TH245383022508X.mp4',\n", " 'TH245442323448Z.mp4',\n", " 'TH240751749798P.mp4',\n", " 'TH243416174271T.mp4',\n", " 'TH247822757566Z.mp4',\n", " 'TH247383668928O.mp4',\n", " 'TH243135199254M.mp4',\n", " 'TH245359652255Q.mp4',\n", " 'TH247037076869L.mp4',\n", " 'TH245220320112U.mp4',\n", " 'TH246921265063H.mp4',\n", " 'TH245563253900F.mp4',\n", " 'TH242955477365K.mp4',\n", " 'TH2409815522764.mp4',\n", " 'TH249204012679Y.mp4',\n", " 'TH2422156679249.mp4',\n", " 'TH245451017582I.mp4',\n", " 'TH242527523455Q.mp4',\n", " 'TH244935980549M.mp4',\n", " 'TH240436594250H.mp4',\n", " 'TH249804809943I.mp4',\n", " 'TH241932329837R.mp4',\n", " 'TH243386466031H.mp4',\n", " 'TH241761604302Q.mp4',\n", " 'TH247538008991Q.mp4',\n", " 'TH244468341506N.mp4',\n", " 'TH240729526036R.mp4',\n", " 'TH247984211251K.mp4',\n", " 'TH248501637167A.mp4',\n", " '750114730352.mp4',\n", " '750168480261.mp4',\n", " 'TH246361828803S.mp4',\n", " '750180456200.mp4',\n", " 'TH241295248636O.mp4',\n", " '750142112381.mp4',\n", " 'TH241432268446X.mp4',\n", " '750193115276.mp4',\n", " 'TH241911874584I.mp4',\n", " '750165156392.mp4',\n", " '750169035383.mp4',\n", " 'TH242949243795M.mp4',\n", " '750145803304.mp4',\n", " 'TH248156995752L.mp4',\n", " '750191323324.mp4',\n", " '750189516392.mp4',\n", " 'TH240823877792G.mp4',\n", " '750112532363.mp4',\n", " 'TH243132634933L.mp4',\n", " '750148316330.mp4',\n", " '628726395953.mp4',\n", " 'TH240742773489K.mp4',\n", " 'TH245804732116L.mp4',\n", " '750112445316.mp4',\n", " 'TH247886803258N.mp4',\n", " 'TH247206857307P.mp4',\n", " 'TH244473654599G.mp4',\n", " 'TH241232558054N.mp4',\n", " '750182457334.mp4',\n", " '240612S4Q4V3Y6.mp4',\n", " 'TH244258789737J.mp4',\n", " 'TH248277171510J.mp4',\n", " 'TH249221724437M.mp4',\n", " '240612S5P8DM0P.mp4',\n", " '240612S39W3MXC.mp4',\n", " '240612SAPYKSSQ.mp4',\n", " 'TH247797087667R.mp4',\n", " 'TH248036199813R.mp4',\n", " 'TH246922230007Q.mp4',\n", " 'TH2454628046023.mp4',\n", " 'TH242030409442V.mp4',\n", " 'TH247032016169P.mp4',\n", " 'TH246805593554O.mp4',\n", " 'TH2423300319777.mp4',\n", " 'TH240282623688N.mp4',\n", " 'TH2458054183334.mp4',\n", " 'TH244089475681T.mp4',\n", " 'TH2432681460395.mp4',\n", " 'TH247234038841R.mp4',\n", " 'TH244130133737X.mp4',\n", " 'TH2407855814189.mp4',\n", " 'TH240063051098A.mp4',\n", " 'TH246738545273Y.mp4',\n", " 'TH2473828710098.mp4',\n", " 'TH249543768123Q.mp4',\n", " 'TH242817584226P.mp4',\n", " 'TH2402130098985.mp4',\n", " 'TH2438487816523.mp4',\n", " 'TH240022666556T.mp4',\n", " 'TH246019339849N.mp4',\n", " 'TH2405664409170.mp4',\n", " 'TH245841767297X.mp4',\n", " 'TH242110963053Y.mp4',\n", " '750137367291.webm',\n", " 'TH2470937706270.mp4',\n", " 'TH2411107238731.mp4',\n", " 'TH246809914858Z.mp4',\n", " 'TH2461005366952.mp4',\n", " 'TH2479872848380.mp4',\n", " 'TH242548933643Q.mp4',\n", " '750137367291.mp4',\n", " 'TH244844987652V.mp4',\n", " '750116544310.mp4',\n", " 'TH240969091814U.mp4',\n", " 'TH249333530745Q.mp4',\n", " 'TH242494496105T.mp4',\n", " '750187111321.mp4',\n", " 'TH243114561773M.mp4',\n", " '750114964270.mp4',\n", " 'TH247206021492N.mp4',\n", " '750193684321.mp4',\n", " 'TH2471987457514.mp4',\n", " '750136553386.mp4',\n", " 'TH2474289121203.mp4',\n", " 'TH248874834388W.mp4',\n", " '750128292244.mp4',\n", " '750162089202.mp4',\n", " 'TH244878002471S.mp4',\n", " '750131403383.mp4',\n", " 'TH2428702859831.mp4',\n", " '750134665280.mp4',\n", " '750150125222.mp4',\n", " 'TH240321238118W.mp4',\n", " '750190279333.mp4',\n", " 'TH247545007875R.mp4',\n", " '750103202332.mp4',\n", " 'TH240807132417V.mp4',\n", " '750186168384.mp4',\n", " 'TH244129389759U.mp4',\n", " '750106940214.mp4',\n", " 'TH2453332773008.mp4',\n", " '750140650265.mp4',\n", " 'TH2418112856220.mp4',\n", " '750112378293.mp4',\n", " 'TH247199323697P.mp4',\n", " '750100977301.mp4',\n", " 'TH2479759688351.mp4',\n", " 'TH243619624537U.mp4',\n", " 'TH245328536080L.mp4',\n", " '750165265371.mp4',\n", " 'TH2495363181476.mp4',\n", " '750104582242.mp4',\n", " '750182725316.mp4',\n", " '750150841296.mp4',\n", " '750171215301.mp4',\n", " '750146460383.mp4',\n", " 'TH2405374331764.mp4',\n", " '750195350365.mp4',\n", " 'TH2476199719333.mp4',\n", " '750126719296.mp4',\n", " '750108324276.mp4',\n", " '750148677294.mp4',\n", " '750161338301.mp4',\n", " '750182431342.mp4',\n", " 'TH01315RVE3R9A2.mp4',\n", " 'TH47015RVE3R8D.mp4',\n", " 'TH17015RVE3Q6D.mp4',\n", " 'TH01255RVE3N3B.mp4',\n", " 'TH01085RVE3S4B.mp4',\n", " 'TH01445RVE3M2B.mp4',\n", " 'TH20015RVE3Q1D.mp4',\n", " 'TH01475RVE3P5A1.mp4',\n", " 'TH68175RVE3M1A.mp4',\n", " 'TH01415RVE3N6C0.mp4',\n", " 'THT0117ZFD6M8Z.mp4',\n", " 'TH10015RVE3P1A.mp4',\n", " 'THT0511ZDHZX9Z.mp4',\n", " 'TH02025RVE3M8A.mp4',\n", " 'THT0101ZF2697Z.mp4',\n", " 'THT1507ZDTZB5Z.mp4',\n", " 'TH02015RVE3S3K0.mp4',\n", " 'THT3701ZFFGC7Z.mp4',\n", " 'TH44105RVE3N0G.mp4',\n", " 'THT0109ZFQFW7Z.mp4',\n", " 'TH01475RVE3N7B1.mp4',\n", " 'TH20015RVE3S6P.mp4',\n", " 'THT2606ZECA68Z.mp4',\n", " 'THT0901ZE1UZ9Z.mp4',\n", " 'TH01075RVE3P0C.mp4',\n", " 'THT1204ZEQ191Z.mp4',\n", " 'TH01335RVE3M5G.mp4',\n", " 'THT1501ZF8TC2Z.mp4',\n", " 'TH01035RVE3P8E.mp4',\n", " 'THT5701ZDBZ43Z.mp4',\n", " 'THT0149ZDA0G6Z.mp4',\n", " 'TH01125RVE3N8A0.mp4',\n", " 'TH01335RVE3N5G.mp4',\n", " 'THT1601ZDM0X2Z.mp4',\n", " 'TH01475RVE3T0B0.mp4',\n", " 'THT2101ZEZ2C9Z.mp4',\n", " 'TH01465RVE3R4A.mp4',\n", " 'THT7111ZCQRC5Z.mp4',\n", " 'TH01515RVE3M7C0.mp4',\n", " 'THT0304ZEUFV7Z.mp4',\n", " 'TH01405RVE3P2B0.mp4',\n", " 'THT0140ZD1Q72Z.mp4',\n", " 'TH01415RVE3R7B0.mp4',\n", " 'THT0506ZFAQB2Z.mp4',\n", " 'TH15065RVE3K9N.mp4',\n", " 'THT1501ZDDZ87Z.mp4',\n", " 'THT0101ZF7YT7Z.mp4',\n", " 'THT4101ZEKA81Z.mp4',\n", " 'THT1805ZE79T6Z.mp4',\n", " 'TH01055RVE3P6B1.mp4',\n", " 'TH03065RVE3Q7D1.mp4',\n", " 'TH13085RVE3R3G.mp4',\n", " 'TH01115RVE3M9B.mp4',\n", " 'TH01275RVE3N1C.mp4',\n", " 'TH01235RVE3M3C.mp4',\n", " 'JF024803214TH.mp4',\n", " 'JF024684295TH.mp4',\n", " 'TH28145RVE3M0A.mp4',\n", " 'TH01015RVE3Q4B1.mp4',\n", " 'EB571370904TH.mp4',\n", " 'EB571314709TH.mp4',\n", " 'TH01335RVE3Q9F.mp4',\n", " 'EB571226916TH.mp4',\n", " 'EA734415788TH.mp4',\n", " 'TH01255RVE3N9D.mp4',\n", " 'EB571169578TH.mp4',\n", " 'EB571226920TH.mp4',\n", " 'EB571205301TH.mp4',\n", " 'EB571370935TH.mp4',\n", " '240611QJ6GS46W.mp4',\n", " 'EB571295514TH.mp4',\n", " 'EB571226902TH.mp4',\n", " 'EB571261056TH.mp4',\n", " 'EB571226933TH.mp4',\n", " 'EB571277250TH.mp4',\n", " 'EB571314672TH.mp4',\n", " 'EB571169564TH.mp4',\n", " 'EB571370918TH.mp4',\n", " 'EB571242304TH.mp4',\n", " 'EB571205315TH.mp4',\n", " 'EB571333739TH.mp4',\n", " 'EB571277263TH.mp4',\n", " 'EB571370921TH.mp4',\n", " 'EB571314690TH.mp4',\n", " 'EB571242318TH.mp4',\n", " 'EB571314686TH.mp4',\n", " 'EB571205292TH.mp4',\n", " 'EB571277246TH.mp4',\n", " 'EA734415774TH.mp4']\n"]}], "source": ["names = [r['name'] for r in results]\n", "pprint(names)\n", "\n", "with open(\"test.txt\", \"w\") as f:\n", "    for line in names:\n", "        f.write(f\"{line}\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}