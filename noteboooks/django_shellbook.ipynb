{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------------------------------\n", "                                   Settings\n", "--------------------------------------------------------------------------------\n", "DEBUG   : True\n", "DATABASE: localhost/postgres:15432\n", "--------------------------------------------------------------------------------\n"]}], "source": ["import django\n", "import os\n", "import sys\n", "os.ch<PERSON>('..')\n", "\n", "PROJECT_DIR = os.path.dirname(os.getcwd())\n", "sys.path.append(PROJECT_DIR)\n", "\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"core.settings\")\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from picking.models import PickOrder\n", "import json\n", "\n", "pick_orders = PickOrder.objects.filter(order_number__startswith='991576188398904')\n", "\n", "order_jsons_3 = []\n", "order_jsons_4 = []\n", "for pick_order in pick_orders:\n", "    if '---' not in pick_order.order_number:\n", "        order_jsons_3.append(pick_order.order_json)\n", "        order_jsons_4.append(pick_order.order_json)\n", "    \n", "    if pick_order.order_number.endswith('-4'):\n", "        order_jsons_4.append(pick_order.order_json)\n", "    \n", "    if pick_order.order_number.endswith('-3'):\n", "        order_jsons_3.append(pick_order.order_json)\n", "\n", "with open('order_jsons_3.json', 'w') as f:\n", "    json.dump(order_jsons_3, f, ensure_ascii=False, indent=4)\n", "\n", "with open('order_jsons_4.json', 'w') as f:\n", "    json.dump(order_jsons_4, f, ensure_ascii=False, indent=4)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}