{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import django\n", "import os\n", "os.ch<PERSON>('..')\n", "\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"core.settings\")\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import uuid\n", "from django.utils import timezone\n", "from picking.models import PickOrderTrackingNo\n", "\n", "pick_order_tracking_no = PickOrderTrackingNo.objects.create(\n", "    company_id=1,\n", "    pick_order_id=292257,\n", "    tracking_no=\"TH20045JTS6S2B1\",\n", "    shipping_status=\"\",\n", "    shipping_message=\"\",\n", "    status=PickOrderTrackingNo.IN_DELIVERY,\n", "    is_active=True,\n", "    create_date=timezone.now(),\n", "    update_date=None,\n", "    shipping_provider=PickOrderTrackingNo.DBB_FLASH,\n", "    webhook_logs=[],\n", "    src_address = \"123/456\",\n", "    src_province = \"กรุงเทพมหานคร\",\n", "    src_district = \"ลาดพร้าว\",\n", "    src_subdistrict = \"วังทองหลาง\",\n", "    src_postcode = \"10240\",\n", "    dst_address = \"789/123\",\n", "    dst_province = \"กรุงเทพมหานคร\",\n", "    dst_district = \"บางกะปิ\",\n", "    dst_subdistrict = \"บางกะปิ\",\n", "    dst_postcode = \"10240\",\n", "    tracking_info={\n", "       \"pno\":\"TH20045JTS6S2B1\",\n", "       \"mchId\":\"CZ5533\",\n", "       \"subMchId\":\"10492839\",\n", "       \"outTradeNo\":\"7608877\",\n", "       \"sortCode\":\"19C-19026-03\",\n", "       \"lineCode\":\"08\",\n", "       \"sortingLineCode\":\"E08\",\n", "       \"dstStoreName\":\"PTY_SP-พัทยา\",\n", "       \"earlyFlightEnabled\":<PERSON><PERSON><PERSON>,\n", "       \"packEnabled\":<PERSON><PERSON><PERSON>,\n", "       \"upcountryCharge\":True,\n", "       \"notice\":None,\n", "       \"srcPostalCode\":\"24130\",\n", "       \"dstPostalCode\":\"20150\"\n", "    },\n", "    cod_amount=None\n", ")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}