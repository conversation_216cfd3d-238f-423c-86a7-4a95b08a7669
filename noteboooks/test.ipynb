{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   order_id           timestamp  value\n", "0         1 2024-10-01 10:00:00    100\n", "1         1 2024-10-02 12:00:00    150\n", "2         2 2024-10-01 11:00:00    200\n", "3         2 2024-10-03 14:00:00    250\n", "4         3 2024-10-02 09:00:00    300\n", "5         3 2024-10-04 15:00:00    350\n", "------------------\n", "order_id\n", "1    1\n", "2    3\n", "3    5\n", "Name: timestamp, dtype: int64\n", "------------------\n", "   order_id           timestamp  value\n", "1         1 2024-10-02 12:00:00    150\n", "3         2 2024-10-03 14:00:00    250\n", "5         3 2024-10-04 15:00:00    350\n"]}], "source": ["import pandas as pd\n", "\n", "# Sample DataFrame\n", "data = {\n", "    'order_id': [1, 1, 2, 2, 3, 3],\n", "    'timestamp': ['2024-10-01 10:00', '2024-10-02 12:00', '2024-10-01 11:00', '2024-10-03 14:00', '2024-10-02 09:00', '2024-10-04 15:00'],\n", "    'value': [100, 150, 200, 250, 300, 350]\n", "}\n", "df = pd.DataFrame(data)\n", "df['timestamp'] = pd.to_datetime(df['timestamp'])\n", "print(df)\n", "print('------------------')\n", "print(df.groupby('order_id')['timestamp'].idxmax())\n", "print('------------------')\n", "\n", "# Group by order_id and keep the latest timestamp\n", "latest_records = df.loc[df.groupby('order_id')['timestamp'].idxmax()]\n", "\n", "print(latest_records)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated JWT: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRhIjpbIjAwMiIsMTAwMCw3MCwiSU5WMDAwMDEiLDEwNzAsNzAsIjAwMDAwIiwiQUJDREVHIl19.GbV9b49SPFO5JIuRHzLYGXlSbKHR0U44cp5cokopvNo\n", "Decoded payload: {'data': ['002', 1000, 70, 'INV00001', 1070, 70, '00000', 'ABCDEG']}\n"]}], "source": ["import jwt\n", "\n", "def generate_jwt(payload, secret_key, algorithm='HS256', expires_in_minutes=60):\n", "    \"\"\"\n", "    Generate a JWT token.\n", "\n", "    :param payload: dict - Data to encode in the JWT.\n", "    :param secret_key: str - Secret key to sign the JWT.\n", "    :param algorithm: str - Algorithm to use for encoding (default: HS256).\n", "    :param expires_in_minutes: int - Token expiration time in minutes (default: 60).\n", "    :return: str - Encoded JWT token.\n", "    \"\"\"\n", "    # exp_time = datetime.datetime.utcnow() + datetime.timedelta(minutes=expires_in_minutes)\n", "    # payload['exp'] = exp_time  # Adding expiration time to payload\n", "    token = jwt.encode(payload, secret_key, algorithm=algorithm)\n", "    return token\n", "\n", "# Example usage\n", "secret_key = \"my_secret_key\"\n", "payload_data = {\n", "    \"data\": [\"002\",1000,70,\"INV00001\",1070,70,\"00000\",\"ABCDEG\"]\n", "}\n", "\n", "token = generate_jwt(payload_data, secret_key)\n", "print(\"Generated JWT:\", token)\n", "\n", "# Decoding to verify (for testing purposes)\n", "try:\n", "    decoded_payload = jwt.decode(token, secret_key, algorithms=['HS256'])\n", "    print(\"Decoded payload:\", decoded_payload)\n", "except jwt.ExpiredSignatureError:\n", "    print(\"Error: The token has expired.\")\n", "except jwt.Invalid<PERSON>okenError:\n", "    print(\"Error: The token is invalid.\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}