from dynamic_rest.viewsets import DynamicModelViewSet
from announcements.models import Announcement
from announcements.serializers import AnnouncementSerializer
from rest_framework.response import Response
from rest_framework.status import HTTP_405_METHOD_NOT_ALLOWED


class AnnouncementViewSet(DynamicModelViewSet):
    serializer_class = AnnouncementSerializer

    def get_queryset(self):
        return Announcement.objects.all()

    def create(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)
