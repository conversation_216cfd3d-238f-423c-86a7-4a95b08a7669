from typing import Mapping, Optional
from django.contrib import admin
from dotenv import Any
from .models import Announcement
from django import forms
# Register your models here.


class AnnouncementAdminForm(forms.ModelForm):
    class Meta:
        model = Announcement
        fields = ['title', 'text', 'theme', 'ui_mode', 'is_active']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['title'].help_text = 'ถ้าเลือก UI mode เป็น banner, title จะไม่แสดง'
        self.fields['is_active'].help_text = 'Active ได้ทีละ 1 announcement เท่านั้น'
        self.fields['text'].help_text = 'ใส่ html ได้'

    def clean(self) -> Optional[Mapping[str, Any]]:
        result = super().clean()
        if self.cleaned_data.get('is_active'):
            Announcement.objects.update(is_active=False)
        return result


class AnnouncementAdmin(admin.ModelAdmin):
    search_fields = ['title', 'text']
    list_display = ['title', 'text', 'theme', 'ui_mode', 'is_active']
    list_filter = ['is_active']
    form = AnnouncementAdminForm


admin.site.register(Announcement, AnnouncementAdmin)
