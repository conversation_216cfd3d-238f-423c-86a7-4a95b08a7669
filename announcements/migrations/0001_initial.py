# Generated by Django 2.2.24 on 2022-03-27 15:35

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('text', models.TextField()),
                ('is_active', models.BooleanField(default=False)),
                ('theme', models.CharField(choices=[('alert', 'Alert (red)'), ('warning', 'Warning (orange)'), ('info', 'Info (blue)')], max_length=10)),
                ('ui_mode', models.Char<PERSON>ield(max_length=10)),
            ],
        ),
        migrations.AddConstraint(
            model_name='announcement',
            constraint=models.UniqueConstraint(condition=models.Q(is_active=True), fields=('is_active',), name='active_one_at_a_time'),
        ),
    ]
