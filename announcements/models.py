from django.db import models
from django.db.models import Q
from django.db.models.constraints import UniqueConstraint


class Announcement(models.Model):
    title = models.CharField(max_length=200)
    text = models.TextField()
    is_active = models.BooleanField(default=False)

    THEME_ALERT = 'error'
    THEME_WARNING = 'warning'
    THEME_INFO = 'info'
    THEME_CHOICES = [
        (THEME_ALERT, 'Alert (red)'),
        (THEME_WARNING, 'Warning (orange)'),
        (THEME_INFO, 'Info (blue)'),
    ]
    theme = models.CharField(max_length=10, choices=THEME_CHOICES)

    UI_MODE_POPUP = 'popup'
    UI_MODE_BANNER = 'banner'
    UI_MODE_CHIOCES = [
        (UI_MODE_BANNER, 'Banner (ด้านบน)'),
        (UI_MODE_POPUP, 'Popup (ตรงกลาง)')
    ]
    ui_mode = models.Char<PERSON><PERSON>(max_length=10, choices=UI_MODE_CHIOCES)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=['is_active'],
                condition=Q(is_active=True),
                name='active_one_at_a_time')
        ]

    def __str__(self) -> str:
        return self.title
