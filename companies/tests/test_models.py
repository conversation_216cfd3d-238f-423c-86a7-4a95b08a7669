from pprint import pprint
from django.test import TestCase
from django.test.utils import tag
from companies.models import Company
from core.tests.testutils import TestCaseHelper, TestClient
from core.tests import setup
from services.zort.zort import ZortException
from users.models import User
from wallets.models import Wallet


# python manage.py test --tag=company
class CompanyModelTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        self.user, self.company, self.wallet = setup.init().values()
        self.user2 = User.objects.create_user(
            username='admin2',
            password="password",
            company=self.company,
        )
        self.wallet: Wallet = self.wallet
        self.client = TestClient()
        self.client.login()

    @tag('company')
    def test_get_zort_service(self):
        self.company: Company = self.company
        self.company.set_setting('ZORT_STORENAME', 'aaaa')
        self.company.set_setting('ZORT_API_KEY', 'bbbb')
        self.company.set_setting('ZORT_API_SECRET', 'cccc')

        service = self.company.get_zort_service()
        self.assertEqual(service.storename, 'aaaa')
        self.assertEqual(service.apikey, 'bbbb')
        self.assertEqual(service.apisecret, 'cccc')

    @tag('company')
    def test_get_zort_service_raise_exception(self):
        with self.assertRaises(ZortException):
            self.company.get_zort_service()

    @tag('company')
    def test_get_vrich_service(self):
        self.company: Company = self.company
        self.company.set_setting('VRICH_HOST', 'aaaa')
        self.company.set_setting('VRICH_TOKEN', 'bbbb')

        service = self.company.get_vrich_service()
        self.assertEqual(service.host, 'aaaa')
        self.assertEqual(service.token, 'bbbb')

    @tag('company')
    def test_get_vrich_service_for_user(self):
        company: Company = self.company
        u1: User = self.user
        u2: User = self.user2
        company.set_setting('VRICH_HOST', 'aaaa1', user_id=u1.id)
        company.set_setting('VRICH_TOKEN', 'bbbb1', user_id=u1.id)
        company.set_setting('VRICH_HOST', 'aaaa2', user_id=u2.id)
        company.set_setting('VRICH_TOKEN', 'bbbb2', user_id=u2.id)

        with self.assertRaises(AssertionError):
            service = company.get_vrich_service()

        service = company.get_vrich_service(user_id=u1.id)
        self.assertEqual(service.host, 'aaaa1')
        self.assertEqual(service.token, 'bbbb1')

        service = company.get_vrich_service(user_id=u2.id)
        self.assertEqual(service.host, 'aaaa2')
        self.assertEqual(service.token, 'bbbb2')

    @tag('company')
    def test_get_printnode_service(self):
        self.company: Company = self.company
        self.company.set_setting('PRINTNODE_API_KEY', 'aaaa')
        service = self.company.get_printnode_service()
        self.assertEqual(service.api_key, 'aaaa')

    @tag('company')
    def test_get_vrich_service_raise_exception(self):
        with self.assertRaises(AssertionError):
            self.company.get_vrich_service()

    @tag('company')
    def test_user_count(self):
        self.assertEqual(self.company.user_count, 2)

    @tag('company')
    def test_no_record_balance(self):
        self.company.wallet.delete()
        self.company.refresh_from_db()
        self.assertEqual(self.company.record_balance, 0)

    @tag('company')
    def test_wallet_record_balance(self):
        self.wallet.topup_record_balance(100, create_by=None)
        self.company.refresh_from_db()
        self.assertEqual(self.company.record_balance, 100)

    @tag('company')
    def test_no_sms_balance(self):
        self.company.wallet.delete()
        self.company.refresh_from_db()
        self.assertEqual(self.company.sms_balance, 0)

    @tag('company')
    def test_wallet_sms_balance(self):
        self.wallet.topup_sms_balance(100, create_by=None)
        self.assertEqual(self.company.sms_balance, 100)

    @tag('company')
    def test_get_all_settings(self):
        self.company.set_setting('SMS_SENDER', 'Cusway-SMS')
        self.company.set_setting('RECORD_GMAIL_ALLOWED_LIST', '["<EMAIL>", "<EMAIL>"]')
        self.company.set_setting('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', '10')

        settings = self.company.get_all_settings()
        self.assertEqual(settings['SMS_SENDER'], 'Cusway-SMS')
        self.assertEqual(settings['RECORD_GMAIL_ALLOWED_LIST'], ["<EMAIL>", "<EMAIL>"])
        self.assertEqual(settings['GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION'], 10)

    @tag('company')
    def test_set_settings_json(self):
        self.company.set_setting('SMS_SENDER', 'Cusway-SMS')
        self.company.set_setting('RECORD_GMAIL_ALLOWED_LIST', ["<EMAIL>", "<EMAIL>"])
        self.company.set_setting('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', 10)

        settings = self.company.get_all_settings()
        self.assertEqual(settings['SMS_SENDER'], 'Cusway-SMS')
        self.assertEqual(settings['RECORD_GMAIL_ALLOWED_LIST'], ["<EMAIL>", "<EMAIL>"])
        self.assertEqual(settings['GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION'], 10)

    @tag('company')
    def test_get_setting(self):
        company = self.company
        u1 = self.user
        u2 = self.user2
        company.set_setting('VRICH_HOST', 'http://vrich1.com', user_id=u1.id)
        company.set_setting('VRICH_HOST', 'http://vrich2.com', user_id=u2.id)
        company.set_setting('SMS_SENDER', 'Cusway-SMS')
        company.set_setting('RECORD_GMAIL_ALLOWED_LIST', ["<EMAIL>", "<EMAIL>"])
        company.set_setting('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', 10)

        company.refresh_from_db()
        self.assertEqual(company.get_setting('VRICH_HOST'), None)
        self.assertEqual(company.get_setting('VRICH_HOST', user_id=u1.id), 'http://vrich1.com')
        self.assertEqual(company.get_setting('VRICH_HOST', user_id=u2.id), 'http://vrich2.com')
        self.assertEqual(company.get_setting('SMS_SENDER'), 'Cusway-SMS')
        self.assertEqual(
            company.get_setting('RECORD_GMAIL_ALLOWED_LIST'),
            ["<EMAIL>", "<EMAIL>"]
        )
        self.assertEqual(company.get_setting('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION'), 10)
