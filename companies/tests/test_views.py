from datetime import date
import json
from django.test import SimpleTestCase, TestCase
from django.test.utils import tag
from companies.models import Company
from companies.views import is_obscure, obscure
from core.tests.testutils import TestCaseHelper, TestClient
from core.tests import setup
from users.models import User


# python manage.py test --tag=
# class CompanyCreateTestCase(TestCase, AssertMatchMixin):
#     def setUp(self) -> None:
#         setup.init()
#         self.client = TestClient()
#         self.client.login_with_authtoken()

#     @tag('create-company')
#     def test_create_company(self):
#         res = self.client.post('/api/companies/companies/create/', data=json.dumps({
#             'name': 'Cusway2',
#             'account_suffix': '@cusway2',
#             'sms_amount': 100,
#             'record_amount': 100,
#             'settings': [
#                 {'key': 'ENABLE_ORDER_CENTER', 'value': '1'},
#                 {'key': 'AUTO_SYNC_ORDER_ENABLE', 'value': '1'},
#                 {'key': 'ZORT_API_KEY', 'value': 'zort_api_key'},
#                 {'key': 'ZORT_API_SECRET', 'value': 'zort_api_secret'},
#                 {'key': 'ZORT_STORENAME', 'value': 'zort_store_name'},
#             ],
#             'users': [
#                 {'username': 'admin1', 'password': 'password1', 'groups': ['owner']},
#                 {'username': 'user1', 'password': 'password1', 'groups': ['employee']},
#             ],
#             'expire_date': '2023-01-01'
#         }), content_type='application/json')

#         self.assertEqual(res.status_code, 200, res.json())
#         company: Company = Company.objects.get(name='Cusway2', account_suffix='@cusway2')

#         admin1 = User.objects.get(username='admin1@cusway2', company=company)
#         self.assertEqual([g.name for g in admin1.groups.all()], ['owner'])

#         user1 = User.objects.get(username='user1@cusway2', company=company)
#         self.assertEqual([g.name for g in user1.groups.all()], ['employee'])

#         self.assertEqual(company.get_setting('ENABLE_ORDER_CENTER'), 1)
#         self.assertEqual(company.get_setting('AUTO_SYNC_ORDER_ENABLE'), 1)
#         self.assertEqual(company.get_setting('ZORT_API_KEY'), 'zort_api_key')
#         self.assertEqual(company.get_setting('ZORT_API_SECRET'), 'zort_api_secret')
#         self.assertEqual(company.get_setting('ZORT_STORENAME'), 'zort_store_name')

#         self.assertEqual(company.wallet.record_balance, 100)
#         self.assertEqual(company.wallet.sms_balance, 100)
#         self.assertEqual(company.expire_date, date(2023, 1, 1))


class ObscureTestCase(SimpleTestCase):
    @tag('obscure')
    def test_obscure(self):
        out = obscure('hellohellohello')
        self.assertEqual(out, 'hell***********')

    @tag('obscure')
    def test_is_obscure(self):
        testcases = {
            'hell***********': True,
            'hellohellohello': False
        }
        for data, expect in testcases.items():
            result = is_obscure(data)
            self.assertEqual(result, expect)


class CompanySettingAPITestCase(TestCase):
    def setUp(self) -> None:
        result = setup.init()
        self.company: Company = result['company']
        self.client = TestClient()
        self.client.login()

    def test_get_settings(self):
        self.company.set_setting('ZORT_API_KEY', 'hellohellohello')
        res = self.client.get('/api/companies/settings/')
        data = res.json()
        self.assertEqual(data['ZORT_API_KEY'], 'hell***********')
        self.assertEqual(data['ENABLE_ORDER_CENTER'], 1)

    def test_post_update_settings(self):
        self.client.post('/api/companies/settings/', data={
            # obscure and will not update
            'ZORT_API_KEY': 'hell***********',

            # will update
            'COMPANY_NAME': 'CUSWAY!'
        })

        self.company.refresh_from_db()
        self.assertEqual(self.company.get_setting('ZORT_API_KEY'), None)
        self.assertEqual(self.company.get_setting('COMPANY_NAME'), 'CUSWAY!')


class RenameCompanyTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        self.user, self.company, self.wallet = setup.init().values()
        self.client = TestClient()
        self.client.login()

    @tag('rename-company')
    def test_can_run(self):
        response = self.client.post(
            '/api/companies/company/rename/',
            data=json.dumps({
                'name': 'Ceo Company',
                'suffix': '@ceo'
            }),
            content_type='application/json'
        )
        print(response.json())
        self.assertEqual(response.json(), 'OK')

        company = Company.objects.get(account_suffix='@ceo')
        print(company.name)
        self.assertEqual(company.name, 'Ceo Company')

    @tag('rename-company')
    def test_same_suffix(self):
        response = self.client.post(
            '/api/companies/company/rename/',
            data=json.dumps({
                'name': 'Ceo Company',
                'suffix': '@localhost'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.json(), {'suffix': ['Suffix already exist']})
        self.assertEqual(response.status_code, 400)


class CreateAdminTestCase(TestCase):
    def setUp(self) -> None:
        self.user, self.company, self.wallet = setup.init().values()
        self.client = TestClient()
        self.client.login()

    @tag('create-admin')
    def test_can_run(self):
        response = self.client.post(
            '/api/companies/company/change-admin/',
            data=json.dumps({
                'username': 'admin2',
                'password': 'password',
                'first_name': 'Admin',
                'last_name': '2',

            }),
            content_type='application/json'
        )

        self.assertEqual(response.status_code, 200)
        # self.assertEqual('access_token' in response.json(), True)
        # self.assertEqual('refresh_token' in response.json(), True)

        user = User.objects.get(username='admin2@localhost')
        self.assertEqual(user.groups.first().name, 'owner')
        self.assertEqual(user.check_password('password'), True)
