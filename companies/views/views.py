import os
from django.utils.decorators import method_decorator
from django.db import transaction
from django.conf import settings
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import TokenAuthentication
from dynamic_rest.viewsets import DynamicModelViewSet
from adminapi.views.admin_company_views import CompanyRegisterAPI
from cloudtasks.tasks import create_webhook_auth_task

from companies.permissions import CanChangeSettingOrReadOnly, IsTemporaryUser
from companies.models import SETTING_KEYS, Company
from companies.serializers.create_company_serializers import (
    _UserSerializer,
    CreateCompanySerializer,
)
from companies.serializers.serializers import CompanySerializer
from companies.service import CompanyChoiceService
from core.responses.error_responses import ResponseError
from picking.permissions import CanViewOrderCenterPage, CanViewProductPage
from picking.services.webhook import WebhookService
from services.dobybot_connect.dobybot_connect import DobybotConnectException
from users.models import User
from wallets.models import Wallet
from django.contrib.auth.models import Group


def obscure(value):
    if isinstance(value, dict):
        return {"****": "****"}
    return value[:4] + ("*" * (len(value) - 4))


def is_obscure(value):
    return value[4:] == ("*" * (len(value) - 4))


class CompanySettingAPI(APIView):
    permission_classes = [CanChangeSettingOrReadOnly]

    def get(self, request):
        company: Company = request.user.company
        settings = company.get_all_settings()

        # Obscure secret values
        for key, value in settings.items():
            if SETTING_KEYS[key].secret and value:
                settings[key] = obscure(value)

            if key == "ETAX_SELLER":
                if value:
                    settings[key] = {
                        "name": value.get("name"),
                        "tax_id": value.get("tax_id"),
                        "address": value.get("address"),
                        "phone_number": value.get("phone_number"),
                        "seller_branch": value.get("seller_branch"),
                        "seller_branch_name": value.get("seller_branch_name"),
                        "post_code": value.get("post_code"),
                    }

        return Response(settings)

    def post(self, request):
        company: Company = request.user.company
        editable_setting_keys = [
            k for k, v in SETTING_KEYS.items() if v.allow_customer_to_edit
        ]

        for key in editable_setting_keys:
            value = request.data.get(key)

            # Skip the key if the setting value is None
            if value is None:
                continue

            # Skip if the value is obscured
            if SETTING_KEYS[key].secret and type(value) == str and is_obscure(value):
                continue

            # Save setting value to database
            value = request.data[key]
            company.set_setting(key, value, commit=False)

        company.save(update_fields=["settings_json"])
        return Response(status=204)


class CompanyChoicesAPI(APIView):
    def get(self, request, choice_name):
        company: Company = request.user.company
        choice_service = CompanyChoiceService(company)

        filters = request.query_params.dict()
        choices = choice_service.get_choices(choice_name, filters)
        return Response(choices)


class CompanyDynamicViewSet(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]
    serializer_class = CompanySerializer

    def get_queryset(self):
        return Company.objects.all()

    def perform_destroy(self, company: Company):
        if "test" not in company.account_suffix:
            raise ValidationError(
                f"Unabled to delete: the target company (id={company.id}) is not a test company"
            )
        return super().perform_destroy(company)


@method_decorator(transaction.atomic, "dispatch")
class CompanyCreateAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    def post(self, request):
        validator = CreateCompanySerializer(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        # create company
        company: Company = Company.objects.create(
            name=data["name"],
            account_suffix=data["account_suffix"],
            expire_date=data["expire_date"],
        )

        # create company's settings
        for setting in data["settings"]:
            company.set_setting(setting["key"], setting["value"])

        # create admin@company with default password
        suffix = data["account_suffix"]
        User.objects.create_user(
            username=f"root@{suffix}",
            password="lv'lk,so7j'",  # สองสามหนึ่ง
            is_superuser=True,
            is_staff=True,
            company=company,
        )

        # create company's users
        users = []
        for u in data["users"]:
            user = User.objects.create_user(
                username=u["username"] + suffix, password=u["password"], company=company
            )
            for group in Group.objects.filter(name__in=u["groups"]):
                user.groups.add(group)
            users.append(user)

        # create company's wallet & top-ups
        wallet: Wallet = Wallet.objects.create(company=company)
        wallet.topup_record_balance(
            value=data["record_amount"],
            create_by=request.user,
            description="Initial Deposit",
        )
        wallet.topup_sms_balance(
            value=data["sms_amount"],
            create_by=request.user,
            description="Initial Deposit",
        )

        company_admin_url = (
            f"{settings.DEFAULT_HOST}/admin/companies/company/{company.id}/change/"
        )
        return Response(
            {
                "company": CompanySerializer(company).data,
                "users": _UserSerializer(users, many=True).data,
                "admin_url": company_admin_url,
            }
        )


class MarketplaceListAPI(APIView):
    def get(self, request):
        company: Company = request.user.company
        if settings.DEBUG:
            try:
                service = company.get_dobybot_connect_service()
            except Exception:
                return Response([])
        else:
            service = company.get_dobybot_connect_service()

        try:
            marketplaces = service.get_marketplaces()
        except Exception:
            marketplaces = []

        dbbc_version = company.get_setting("DOBYBOT_CONNECT_VERSION")
        default_setting = {
            1: {
                "sync_order": False,
            },
            2: {
                "sync_order": True,
            },
        }
        for shop in marketplaces:
            if shop["marketplace"] == "shopee":
                shop_setting = company.get_setting("SHOPEE_API").get(
                    shop["seller_id"], default_setting[dbbc_version]
                )
                chat_setting = company.get_setting("SHOPEE_CHAT_API").get(
                    shop["seller_id"], False
                )
                shop["settings"] = {**(shop_setting or {}), "chat": chat_setting}
            if shop["marketplace"] == "lazada":
                seller_id = str(shop["seller_id"])
                shop_setting = company.get_setting("LAZADA_API").get(
                    seller_id, default_setting[dbbc_version]
                )
                chat_setting = company.get_setting("LAZADA_CHAT_API").get(
                    seller_id, False
                )
                shop["settings"] = {**(shop_setting or {}), "chat": chat_setting}
            if shop["marketplace"] == "tiktok_shop":
                seller_id = str(shop["seller_id"])
                shop_setting = company.get_setting("TIKTOK_SHOP_API").get(
                    seller_id, default_setting[dbbc_version]
                )
                shop["settings"] = shop_setting
            if shop["marketplace"] == "tiktok_shop_v2":
                seller_id = str(shop["seller_id"])
                shop_setting = company.get_setting("TIKTOK_SHOP_V2_API").get(
                    seller_id, default_setting[dbbc_version]
                )
                shop["settings"] = shop_setting
            if shop["marketplace"] == "zort":
                shop["marketplace"] = "zort_v2"
                shop["settings"] = company.get_setting("ZORT_API_V2")
            if shop["marketplace"] == "nocnoc":
                seller_id = str(shop["seller_id"])
                shop["settings"] = company.get_setting("NOCNOC_API").get(
                    seller_id, default_setting[dbbc_version]
                )
            if shop["marketplace"] == "line_my_shop":
                shop["settings"] = company.get_setting("LINE_MY_SHOP_API").get(
                    shop["seller_id"], default_setting[dbbc_version]
                )
            if shop["marketplace"] == "nex_gen_commerce":
                shop["settings"] = company.get_setting("NEX_GEN_COMMERCE_API").get(
                    shop["seller_id"], default_setting[dbbc_version]
                )
            if shop["marketplace"] == "woo_commerce":
                shop["settings"] = company.get_setting("WOO_COMMERCE_API").get(
                    shop["seller_id"], default_setting[dbbc_version]
                )

            if company.get_setting("DOBYBOT_CONNECT_VERSION") == 1:
                shop["is_active"] = True

        if company.get_setting("ZORT_STORENAME"):
            marketplaces.append(
                {
                    "marketplace": "zort_v1",
                    "seller_id": company.get_setting("ZORT_STORENAME"),
                    "seller_shop_name": company.get_setting("ZORT_STORENAME"),
                    "refresh_token_expire": None,
                    "logo_url": None,
                    "access_token": True,
                    "refresh_token": True,
                    "settings": {},
                    "is_active": True,
                    "remark": "",
                }
            )

        return Response(marketplaces)


class DobybotConnectSellerAuthAPI(APIView):
    class Validator(serializers.Serializer):
        platform = serializers.ChoiceField(
            choices=[
                "lazada",
                "shopee",
                "tiktok_shop",
                "tiktok_shop_v2",
                "nocnoc",
                "woo_commerce",
            ]
        )

    def get(self, request):
        validator = self.Validator(data=request.GET)
        validator.is_valid(raise_exception=True)

        company: Company = request.user.company
        platform = validator.validated_data["platform"]
        service = company.get_dobybot_connect_service()
        url = service.get_seller_authorization_url(platform)
        return Response(url)


class DobybotConnectShopeeChatAuthAPI(APIView):
    def get(self, request):
        company: Company = request.user.company
        service = company.get_dobybot_connect_service()
        url = service.get_shopee_chat_authorization_url()
        return Response(url)


class DobybotConnectLazadaChatAuthAPI(APIView):
    def get(self, request):
        company: Company = request.user.company
        service = company.get_dobybot_connect_service()
        url = service.get_lazada_chat_authorization_url()
        return Response(url)


class DobybotConnectShopSettingAPI(APIView):
    permission_classes = [CanChangeSettingOrReadOnly]

    class Validator(serializers.Serializer):
        marketplace = serializers.ChoiceField(
            choices=[
                "lazada",
                "shopee",
                "tiktok_shop",
                "tiktok_shop_v2",
                "shipnity",
                "zort_v2",
                "nocnoc",
                "line_my_shop",
                "woo_commerce",
            ]
        )
        shop_id = serializers.CharField()
        settings = serializers.JSONField()

    def put(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        company: Company = request.user.company

        payload = {**validator.validated_data}
        if payload["marketplace"] == "zort_v2":
            payload["marketplace"] = "zort"

        service = company.get_dobybot_connect_service()
        service.update_shop_settings(**payload)
        return Response(status=204)


class DobybotConnectShipnityAuthAPI(APIView):
    class Validator(serializers.Serializer):
        email = serializers.CharField()
        token = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        body = validator.validated_data
        company: Company = request.user.company

        # if company.get_setting('PRE_RECORD_WEBHOOK_ENABLE'):
        #     return Response(
        #         'เชื่อมต่อไม่สำเร็จเนื่องจากมีการเปิดใช้งาน Pre Record Webhook อยู่แล้ว กรุณาติดต่อผู้ดูแลระบบ',
        #         status=400
        #     )

        service = company.get_dobybot_connect_service()
        try:
            shinity_shop = service.register_shipnity(
                company.uuid, body["email"], body["token"]
            )
        except DobybotConnectException as e:
            if e.response.status_code == 401:
                return Response("Email หรือ Token ไม่ถูกต้อง", status=400)
            return Response(str(e), status=400)

        company.set_setting("PRE_RECORD_WEBHOOK_ENABLE", True)
        company.set_setting(
            "PRE_RECORD_WEBHOOK_CONFIG",
            {
                "method": "GET",
                "url": os.getenv("DOBYBOT_CONNECT_HOST")
                + "/shipnity/{company.uuid}/order/{order_number}",
                "headers": {
                    "Authorization": f'Bearer {os.getenv("DOBYBOT_CONNECT_APIKEY")}'
                },
                "order_data_path": "",
            },
        )
        return Response("OK")


class DobybotConnectZortAuthAPI(APIView):
    class Validator(serializers.Serializer):
        storename = serializers.CharField()
        apikey = serializers.CharField()
        apisecret = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        body = validator.validated_data
        company: Company = request.user.company

        service = company.get_dobybot_connect_service()
        try:
            service.register_zort(company.uuid, **body)
        except DobybotConnectException as e:
            if e.response.status_code == 400:
                return Response(e.response.text, status=400)
            else:
                return Response(str(e), status=400)

        return Response("OK")


class DobybotConnectUpdateZortWebhookAPI(APIView):
    permission_classes = [CanChangeSettingOrReadOnly]

    class Validator(serializers.Serializer):
        add_order_url = serializers.CharField(allow_blank=True)
        update_order_url = serializers.CharField(allow_blank=True)
        update_order_tracking_url = serializers.CharField(allow_blank=True)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        company: Company = request.user.company

        service = company.get_dobybot_connect_service()

        try:
            service.update_zort_webhook(**validator.validated_data)
        except DobybotConnectException as e:
            return Response(e.response.text, status=400)

        return Response("OK")


class WebhookAuthTaskScheduleAPI(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAdminUser]

    def post(self, request):
        queryset = Company.objects.filter(
            settings_json__WEBHOOK_AUTH_REQUEST_CONFIG__isnull=False
        )
        for company in queryset:
            create_webhook_auth_task(company_id=company.id)

        return Response({"tasks_created": queryset.count()})


class WebhookAuthTaskHandlerAPI(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        company: Company = validator.validated_data["company"]
        WebhookService.refresh_webhook_auth(company=company)
        return Response("OK")


class DobybotConnectSyncOrder(APIView):
    permission_classes = [CanViewOrderCenterPage]

    def post(self, request):
        company: Company = request.user.company
        service = company.get_dobybot_connect_service()
        result = service.sync_orders(company_uuid=company.uuid)
        return Response(result)


class DobybotConnectSyncShopOrder(APIView):
    permission_classes = [CanViewOrderCenterPage]

    class Validator(serializers.Serializer):
        marketplace = serializers.ChoiceField(
            choices=[
                "lazada",
                "shopee",
                "tiktok_shop",
                "tiktok_shop_v2",
                "shipnity",
                "zort_v2",
                "nocnoc",
                "line_my_shop",
                "nex_gen_commerce",
                "woo_commerce",
            ]
        )
        shop_id = serializers.CharField()
        start = serializers.DateTimeField(required=False)
        end = serializers.DateTimeField(required=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        data = validator.validated_data
        company: Company = request.user.company
        service = company.get_dobybot_connect_service()

        marketplace = data["marketplace"]
        if marketplace == "zort_v2":
            marketplace = "zort"

        if data.get("start") and data.get("end"):
            result = service.sync_shop_order_by(
                company_uuid=company.uuid,
                marketplace=marketplace,
                shop_id=data["shop_id"],
                start=data["start"],
                end=data["end"],
            )
        else:
            result = service.sync_shop_order(
                company_uuid=company.uuid,
                marketplace=marketplace,
                shop_id=data["shop_id"],
            )

        return Response(result)


class DobybotConnectSyncShopProducts(APIView):
    permission_classes = [CanViewProductPage]

    class Validator(serializers.Serializer):
        marketplace = serializers.ChoiceField(
            choices=[
                "lazada",
                "shopee",
                "tiktok_shop",
                "tiktok_shop_v2",
                "zort_v2",
                # "nocnoc",
                "line_my_shop",
                "nex_gen_commerce",
                # "woo_commerce",
            ]
        )
        shop_id = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        data = validator.validated_data
        company: Company = request.user.company

        if company.get_setting("DOBYBOT_CONNECT_VERSION") == 1:
            return ResponseError("DOBYBOT_CONNECT_NOT_SUPPORT_SYNC_PRODUCTS")

        service = company.get_dobybot_connect_service()

        result = service.sync_products(
            shop_id=data["shop_id"],
        )

        return Response(result)


class CompanyWalletBalanceAPI(APIView):
    def get(self, request):
        wallet: Wallet = request.user.company.wallet

        return Response(
            {
                "version": wallet.version,
                "record_balance": wallet.get_realtime_record_balance(),
                "google_drive_limit": wallet.get_google_drive_limit(),
                "google_drive_usage": wallet.google_drive_usage,
            }
        )


class CompanyRenameAPI(APIView):
    permission_classes = [IsTemporaryUser]

    class Validator(serializers.Serializer):
        name = serializers.CharField()
        suffix = serializers.CharField()

        # check if suffix already exist
        def validate_suffix(self, value):
            if Company.objects.filter(account_suffix=value).exists():
                raise serializers.ValidationError("SUFFIX_ALREADY_EXIST")
            return value

        def validate_name(self, value):
            if Company.objects.filter(name=value).exists():
                raise serializers.ValidationError("NAME_ALREADY_EXIST")
            return value

    def post(self, request):
        company = request.user.company
        if company.is_setup:
            return Response("COMPANY_ALREADY_SETUP")

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        data = validator.validated_data
        company.name = data["name"]
        company.account_suffix = data["suffix"]
        company.is_setup = True
        company.save()

        return Response("OK")


class CompanyChangeAdminAPI(APIView):
    permission_classes = [IsTemporaryUser]

    class Validator(serializers.Serializer):
        username = serializers.CharField()
        password = serializers.CharField()
        first_name = serializers.CharField()
        last_name = serializers.CharField()

    def post(self, request):
        company = request.user.company

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        data = validator.validated_data

        User.objects.filter(is_temporary=True).exclude(is_superuser=True).update(
            is_active=False
        )
        user, _ = CompanyRegisterAPI.create_admin_user(
            company, data["username"], data["password"], is_temporary=False
        )

        user.first_name = data["first_name"]
        user.last_name = data["last_name"]
        user.save()
        return Response("OK")


class GetSyncTaskAPI(APIView):
    def get(self, request, task_id):
        company: Company = request.user.company
        service = company.get_dobybot_connect_service()
        result = service.get_sync_task(task_id)
        return Response(result)
