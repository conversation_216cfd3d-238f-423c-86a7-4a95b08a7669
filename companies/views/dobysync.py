from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.authtoken.models import Token


from rest_framework import serializers
from companies.models.models import Company
from services.dobybot_connect.dobybot_connect import (
    DobybotConnectException,
    DobybotConnectService,
)
from services.dobybot_connect.dobysync import (
    DobySyncClientService,
    DobySyncIntegratorService,
)
from users.models import User


class DobybotConnectSwitchToV2API(APIView):
    def post(self, request):
        company: Company = request.user.company

        if company.get_setting("DOBYBOT_CONNECT_VERSION") == 2:
            return Response({"status": "error", "message": "Already on V2"}, status=400)

        # Delete v1 shops
        dcon_service: DobybotConnectService = company.get_dobybot_connect_service()

        try:
            dcon_company_token = dcon_service.get_token()
        except DobybotConnectException:
            tokenuser = User.objects.filter(
                username="dobybot-connect" + company.account_suffix
            ).first()
            if not tokenuser:
                tokenuser = DobybotConnectService.create_token_user(company)
            token = Token.objects.filter(user=tokenuser).first()
            if not token:
                token = Token.objects.create(user=tokenuser)
            tokenkey = token.key if token else None

            dcon_company_token = {
                "name": company.name,
                "dobybot": {
                    "host": "https://api.dobybot.com",
                    "authtoken": tokenkey,
                },
                "lazada": {},
                "lazada_chat": {},
                "shopee": {},
                "shopee_chat": {},
                "tiktok_shop": {},
                "shipnity": {},
                "zort": {},
            }

        lazada_settings = company.get_setting("LAZADA_API")
        shopee_settings = company.get_setting("SHOPEE_API")
        tiktok_shop_settings = company.get_setting("TIKTOK_SHOP_API")
        zort_settings = company.get_setting("ZORT_API_V2")

        for shop_id, setting in lazada_settings.items():
            if not dcon_company_token["lazada"] or not dcon_company_token["lazada"].get(
                shop_id
            ):
                continue

            dcon_company_token["lazada"][shop_id]["airway_bill"] = setting.get(
                "airway_bill", False
            )
        for shop_id, setting in shopee_settings.items():
            if not dcon_company_token["shopee"] or not dcon_company_token["shopee"].get(
                shop_id
            ):
                continue

            dcon_company_token["shopee"][shop_id]["airway_bill"] = setting.get(
                "airway_bill", False
            )
        for shop_id, setting in tiktok_shop_settings.items():
            if not dcon_company_token["tiktok_shop"] or not dcon_company_token[
                "tiktok_shop"
            ].get(shop_id):
                continue

            dcon_company_token["tiktok_shop"][shop_id]["airway_bill"] = setting.get(
                "airway_bill", False
            )
        if dcon_company_token.get("zort"):
            dcon_company_token["zort"]["airway_bill"] = zort_settings.get(
                "airway_bill", False
            )

        # Register v2
        client, unsync_shops = DobySyncIntegratorService.migrate_client_from_v1(
            company, dcon_company_token
        )
        company.set_setting("DOBYSYNC_CLIENT", client.model_dump())

        try:
            shops = dcon_service.get_marketplaces()
        except DobybotConnectException:
            shops = []

        for shop in shops:
            dcon_service.disconnect_shop(
                marketplace=shop["marketplace"], shop_id=shop["seller_id"]
            )

        # Update settings
        company.set_setting("DOBYBOT_CONNECT_VERSION", 2)

        # TODO: Clear v1 settings
        company.set_setting("ZORT_API_KEY", "")
        company.set_setting("ZORT_API_SECRET", "")
        company.set_setting("ZORT_STORENAME", "")
        company.save()

        return Response(
            {
                "status": "ok",
                "message": "Switched to V2",
                "unsync_shops": unsync_shops,
            }
        )


class DobybotConnectSwitchToV1API(APIView):
    def post(self, request):
        company: Company = request.user.company

        if company.get_setting("DOBYBOT_CONNECT_VERSION") == 1:
            return Response({"status": "error", "message": "Already on V1"}, status=400)

        # Delete v2 shops
        dcon_service: DobySyncClientService = company.get_dobybot_connect_service()
        shops = dcon_service.get_marketplaces()
        for shop in shops:
            dcon_service.disconnect_shop(
                marketplace=shop["marketplace"], shop_id=shop["seller_id"]
            )

        # TODO: Clear v2 settings
        company.set_setting("ZORT_API_KEY", "")
        company.set_setting("ZORT_API_SECRET", "")
        company.set_setting("ZORT_STORENAME", "")

        # Update settings
        # company.set_setting("DOBYSYNC_CLIENT", client.model_dump())
        company.set_setting("DOBYBOT_CONNECT_VERSION", 1)
        company.save()

        # Register v1
        dcon_service = company.get_dobybot_connect_service()

        return Response({"status": "ok", "message": "Switched to V1"})


class DobybotConnectNocNocAuthAPI(APIView):
    class Validator(serializers.Serializer):
        storename = serializers.CharField()
        client_id = serializers.CharField()
        secret_key = serializers.CharField()

    def post(self, request):
        company: Company = request.user.company
        dcon_service: DobySyncClientService = company.get_dobybot_connect_service()

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        try:
            dcon_service.register_nocnoc(**data)
        except DobybotConnectException as e:
            return Response(e.response.json(), status=400)

        return Response({"status": "ok", "message": "Connected to NocNoc"})


class DobybotConnectLineMyShopAuthAPI(APIView):
    class Validator(serializers.Serializer):
        storename = serializers.CharField()
        api_key = serializers.CharField()

    def post(self, request):
        company: Company = request.user.company
        dcon_service: DobySyncClientService = company.get_dobybot_connect_service()

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        try:
            dcon_service.register_linemyshop(**data)
        except DobybotConnectException as e:
            return Response(e.response.json(), status=400)

        return Response({"status": "ok", "message": "Connected to Line My Shop"})


class DobybotConnectNexGenCommerceAuthAPI(APIView):
    class Validator(serializers.Serializer):
        storename = serializers.CharField()
        shop_id = serializers.CharField()

    def post(self, request):
        company: Company = request.user.company
        dcon_service: DobySyncClientService = company.get_dobybot_connect_service()

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        try:
            dcon_service.register_nex_gen_commerce(**data)
        except DobybotConnectException as e:
            return Response(e.response.json(), status=400)

        return Response({"status": "ok", "message": "Connected to Line My Shop"})


class DobybotConnectWooCommerceAuthAPI(APIView):
    class Validator(serializers.Serializer):
        storename = serializers.CharField()
        store_url = serializers.CharField()
        api_key = serializers.CharField()
        api_secret = serializers.CharField()

    def post(self, request):
        company: Company = request.user.company
        dcon_service: DobySyncClientService = company.get_dobybot_connect_service()

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        try:
            dcon_service.register_woo_commerce(**data)
        except DobybotConnectException as e:
            return Response(e.response.json(), status=400)

        return Response({"status": "ok", "message": "Connected to WooCommerce"})


class DobybotConnectDisconnectShopAPI(APIView):
    class Validator(serializers.Serializer):
        marketplace = serializers.CharField()
        seller_id = serializers.CharField()

    def post(self, request):
        company: Company = request.user.company
        dcon_service = company.get_dobybot_connect_service()

        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        dcon_service.disconnect_shop(
            marketplace=data["marketplace"], shop_id=data["seller_id"]
        )

        if data["marketplace"] == "lazada":
            config = company.get_setting("LAZADA_API")
            config.pop(data["seller_id"], None)
            company.set_setting("LAZADA_API", config, commit=True)
        if data["marketplace"] == "shopee":
            config = company.get_setting("SHOPEE_API")
            config.pop(data["seller_id"], None)
            company.set_setting("SHOPEE_API", config, commit=True)
        if data["marketplace"] == "nocnoc":
            config = company.get_setting("NOCNOC_API")
            config.pop(data["seller_id"], None)
            company.set_setting("NOCNOC_API", config, commit=True)
        if data["marketplace"] == "tiktok_shop":
            config = company.get_setting("TIKTOK_SHOP_API")
            config.pop(data["seller_id"], None)
            company.set_setting("TIKTOK_SHOP_API", config, commit=True)
        if data["marketplace"] == "tiktok_shop_v2":
            config = company.get_setting("TIKTOK_SHOP_V2_API")
            config.pop(data["seller_id"], None)
            company.set_setting("TIKTOK_SHOP_V2_API", config, commit=True)
        if data["marketplace"] == "line_my_shop":
            config = company.get_setting("LINE_MY_SHOP_API")
            config.pop(data["seller_id"], None)
            company.set_setting("LINE_MY_SHOP_API", config, commit=True)
        if data["marketplace"] == "nex_gen_commerce":
            config = company.get_setting("NEX_GEN_COMMERCE_API")
            config.pop(data["seller_id"], None)
            company.set_setting("NEX_GEN_COMMERCE_API", config, commit=True)
        if data["marketplace"] == "woo_commerce":
            config = company.get_setting("WOO_COMMERCE_API")
            config.pop(data["seller_id"], None)
            company.set_setting("WOO_COMMERCE_API", config, commit=True)

        return Response(
            {
                "status": "ok",
                "message": f"Disconnected {data['seller_id']} from {data['marketplace']}",
            }
        )
