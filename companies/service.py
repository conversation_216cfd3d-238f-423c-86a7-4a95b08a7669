from picking.models import PickOrder
from companies.models import Company
from django.utils import timezone
from datetime import timedelta


class CompanyChoiceService:
    def __init__(self, company: Company) -> None:
        self.company = company

    def get_choices(self, choice_name, filters=None):
        fn = getattr(self, f"get_{choice_name}")
        filters = filters or {}
        return fn(filters)

    def get_saleschannel_choices(self, filters=None):
        return (
            PickOrder.objects.filter(company=self.company, **filters)
            .values_list("order_saleschannel", flat=True)
            .distinct()
        )

    def get_shippingchannel_choices(self, filters=None):
        return (
            PickOrder.objects.filter(company=self.company, **filters)
            .values_list("order_shippingchannel", flat=True)
            .distinct()
        )

    def get_warehouse_choices(self, filters=None):
        return (
            PickOrder.objects.filter(company=self.company, **filters)
            .values_list("order_warehousecode", flat=True)
            .distinct()
        )
