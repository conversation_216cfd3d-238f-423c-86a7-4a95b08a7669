from django.core.validators import MinValueValidator
from rest_framework import serializers

from companies.models import SettingValue
from users.models import User
from django.contrib.auth.models import Group


class _SettingValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = SettingValue
        fields = ['key', 'value']


class _UserSerializer(serializers.ModelSerializer):
    groups = serializers.ListField(default=['employee'], write_only=True)

    class Meta:
        model = User
        fields = ['username', 'password', 'groups']

    def validate_groups(self, value):
        valid_groups = list(Group.objects.values_list('name', flat=True))
        for g in value:
            if g not in valid_groups:
                raise serializers.ValidationError(f'{value} is not a valid group')
        return value


class CreateCompanySerializer(serializers.Serializer):
    name = serializers.CharField()
    account_suffix = serializers.CharField()
    sms_amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)])
    record_amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)])
    expire_date = serializers.DateField()
    settings = _SettingValueSerializer(many=True)
    users = _UserSerializer(many=True)
