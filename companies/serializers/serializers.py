import json
import sentry_sdk

from dynamic_rest.serializers import DynamicModelSerializer
from companies.models import Company, SettingValue
from rest_framework import serializers

from wallets.models import Wallet


class CompanySerializer(DynamicModelSerializer):
    record_balance = serializers.SerializerMethodField()
    google_drive_limit = serializers.SerializerMethodField()

    def get_record_balance(self, company: Company):
        try:
            return company.wallet.get_realtime_record_balance()
        except Wallet.DoesNotExist:
            sentry_sdk.capture_message(f"{company} has no wallet")
            return 0

    def get_google_drive_limit(self, company: Company):
        try:
            return company.wallet.get_google_drive_limit()
        except Wallet.DoesNotExist:
            sentry_sdk.capture_message(f"{company} has no wallet")
            return 0

    class Meta:
        model = Company
        name = "company"
        fields = [
            "id",
            "uuid",
            "name",
            "account_suffix",
            "sms_balance",
            "record_balance",
            "expire_date",
            "is_active",
            "is_active_remark",
            "google_drive_usage",
            "google_drive_limit",
            "wallet_last_update_datetime",
            "package",
            "force_redirect",
            "web_logo",
            "feature_flag",
            "is_setup",
            "receipt_logo",
        ]
        read_only_fields = [
            "id",
            "uuid",
            "name",
            "account_suffix",
            "sms_balance",
            "record_balance",
            "expire_date",
            "is_active",
            "is_active_remark",
            "google_drive_usage",
            "google_drive_limit",
            "wallet_last_update_datetime",
            "package",
            "force_redirect",
            "web_logo",
            "feature_flag",
            "is_setup",
            "receipt_logo",
        ]
