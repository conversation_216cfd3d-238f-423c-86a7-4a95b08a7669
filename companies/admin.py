from collections.abc import Callable, Mapping, Sequence
from typing import Any
from django import forms
from distutils.util import strtobool
import json
import re

# from wallets.models import Wallet
from django.contrib import admin
from django.core.files.base import File
from django.db.models.base import Model
from django.forms.utils import ErrorList
from django.http.request import HttpRequest
from companies.models.models import PublicFile
from companies.models.settings import SETTING_KEYS
from users.models import User

from wallets.models import GoogleDrivePackage, RecordPackage, Wallet
from .models import SettingKey, SettingValue, Company
from django.db import models
from django.forms.widgets import TextInput
from django.contrib.admin.widgets import AdminTextInputWidget
from simple_history.admin import SimpleHistoryAdmin


class SettingValueInline(admin.TabularInline):
    model = SettingValue
    # formfield_overrides = {
    #     models.TextField: {'widget': AdminTextInputWidget},
    # }
    readonly_fields = ["help", "dtype", "default", "allow_customer_to_edit"]
    fields = [
        "key",
        "user",
        "help",
        "dtype",
        "default",
        "allow_customer_to_edit",
        "value",
    ]
    extra = 0

    def get_queryset(self, request, *args, **kwargs):
        return SettingValue.objects.filter(id=0)
        # result = re.findall(r'\/admin\/companies\/company/(\d+)\/change\/', request.path)
        # if result:
        #     company_id = result[0]
        #     return SettingValue.objects.filter(company_id=company_id).order_by('key')
        # return super().get_queryset(request)


class PublicFileInline(admin.TabularInline):
    model = PublicFile
    extra = 0


class WalletInline(admin.StackedInline):
    model = Wallet
    show_change_link = True
    readonly_fields = [
        "version",
        "google_drive_usage",
        "record_balance",
        "shopee_chat_daily_usage",
        "lazada_chat_daily_usage",
        "sms_credit_onhold",
    ]

    fields = ["version", "google_drive_usage", "record_balance"]

    # def record_balance(self, obj):
    #     return obj.get_record_balance()

    # def google_drive_limit(self, obj):
    #     return obj.wallet.get_google_drive_limit()

    def get_fields(self, request: HttpRequest, obj: Any):
        if obj is None or not Wallet.objects.filter(company=obj).exists():
            return [
                "version",
                "record_balance",
                "google_drive_usage",
                "google_drive_limit",
                "shopee_chat_daily_usage",
                "lazada_chat_daily_usage",
            ]

        if obj.wallet.version == 1:
            return [
                "version",
                "record_balance",
                "google_drive_usage",
                "google_drive_limit",
                "shopee_chat_daily_usage",
                "lazada_chat_daily_usage",
            ]
        if obj.wallet.version == 2:
            return [
                "version",
                "get_record_balance",
                "google_drive_usage",
                "get_google_drive_limit",
                "shopee_chat_daily_usage",
                "lazada_chat_daily_usage",
            ]

    def get_readonly_fields(self, request: HttpRequest, obj: Any):
        if obj is None or not Wallet.objects.filter(company=obj).exists():
            return [
                "version",
                "record_balance",
                "google_drive_usage",
                "shopee_chat_daily_usage",
                "lazada_chat_daily_usage",
            ]

        if obj.wallet.version == 1:
            return [
                "version",
                "record_balance",
                "google_drive_usage",
                "shopee_chat_daily_usage",
                "lazada_chat_daily_usage",
            ]
        if obj.wallet.version == 2:
            return [
                "version",
                "get_record_balance",
                "google_drive_usage",
                "get_google_drive_limit",
                "shopee_chat_daily_usage",
                "lazada_chat_daily_usage",
            ]


class CompanyForm(forms.ModelForm):
    feature_flag = forms.MultipleChoiceField(
        choices=[
            (Company.ORDER_CENTER, "Order Center"),
            (Company.RECORD, "Record"),
            (Company.SMS_CHAT, "SMS & Chat"),
            (Company.PRODUCT_MANAGEMENT, "Product Management"),
            (Company.FIX_CASE, "Fix Case"),
            (Company.PRINTING, "Printing"),
            (Company.SHIPPING, "Shipping"),
            (Company.ETAX, "E-Tax"),
        ],
        required=False,
    )

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        instance = kwargs.get("instance")
        if instance:
            self.initial["feature_flag"] = [
                k for k, v in instance.feature_flag.items() if v is True
            ]

    def clean_feature_flag(self):
        ALL_FEATURE_FLAGS = [
            Company.ORDER_CENTER,
            Company.RECORD,
            Company.SMS_CHAT,
            Company.PRODUCT_MANAGEMENT,
            Company.FIX_CASE,
            Company.PRINTING,
            Company.SHIPPING,
            Company.ETAX,
        ]

        feature_flag = self.cleaned_data["feature_flag"]

        result = {}
        for ff in ALL_FEATURE_FLAGS:
            if ff in feature_flag:
                result[ff] = True
            else:
                result[ff] = False

        return result


class CompanyAdmin(SimpleHistoryAdmin):
    search_fields = ["name", "account_suffix", "uuid"]
    list_display = [
        "id",
        "name",
        "account_suffix",
        "user_count",
        "wallet_ver",
        "credit_balance",
        "package",
        "create_date",
        "uuid",
    ]
    list_filter = ["create_date"]
    list_display_links = ["name"]
    readonly_fields = ["setting_json_str", "register_dobybot_connect"]
    exclude = ["settings_json"]
    inlines = [WalletInline, PublicFileInline, SettingValueInline]

    form = CompanyForm

    change_form_template = "companies/company_changeform.html"

    def credit_balance(self, obj):
        return obj.wallet.get_record_balance()

    def wallet_ver(self, obj):
        return obj.wallet.version

    def save_related(self, request, form, formsets, change) -> None:
        result = super().save_related(request, form, formsets, change)
        self.update_settings_json_with_settings_value(request)
        return result

    def update_settings_json_with_settings_value(self, request):
        match = re.findall(r"\/admin\/companies\/company/(\d+)\/change\/", request.path)
        if match:
            company_id = match[0]
            company = Company.objects.get(id=company_id)

            settings_json = company.settings_json
            for setting_value in company.settings.all():
                key = setting_value.key
                value = setting_value.value
                user = setting_value.user

                try:
                    setting_key = SETTING_KEYS[key]
                except KeyError:
                    continue

                if value is None:
                    pass
                elif setting_key.dtype is bool:
                    try:
                        value = True if strtobool(value) else False
                    except Exception:
                        value = False
                elif setting_key.dtype is int:
                    value = int(value)
                elif setting_key.dtype is float:
                    value = float(value)
                elif setting_key.dtype is dict or setting_key.dtype is list:
                    try:
                        value = json.loads(value)
                    except Exception:
                        value = None

                if user is not None:
                    settings_json[f"{key}.{user.id}"] = value
                else:
                    settings_json[key] = value

            company.settings_json = settings_json
            company.save(update_fields=["settings_json"])
            company.settings.all().delete()


admin.site.register(SettingValue)
admin.site.register(Company, CompanyAdmin)
# admin.site.register(Company, SimpleHistoryAdmin)
