# Generated by Django 2.2.24 on 2021-11-30 15:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0001_squashed_0018_auto_20211119_0120'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='account_suffix',
            field=models.Char<PERSON>ield(max_length=200),
        ),
        migrations.AlterField(
            model_name='settingvalue',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to='companies.Company'),
        ),
        migrations.AlterField(
            model_name='settingvalue',
            name='key',
            field=models.CharField(choices=[('AUTO_SYNC_ORDER_ENABLE', 'AUTO_SYNC_ORDER_ENABLE'), ('ZORT_API_KEY', 'ZORT_API_KEY'), ('ZORT_API_SECRET', 'ZORT_API_SECRET'), ('ZORT_STORENAME', 'ZORT_STORENAME'), ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'), ('PRINTNODE_PICKORDER_PRINTER_ID', 'PRINTNODE_PICKORDER_PRINTER_ID'), ('PRINTNODE_PICKORDER_PRINTER_TYPE', 'PRINTNODE_PICKORDER_PRINTER_TYPE'), ('PICKORDER_AUTO_PRINT_ENABLE', 'PICKORDER_AUTO_PRINT_ENABLE'), ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'), ('SMS_SENDER', 'SMS_SENDER'), ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'), ('POST_RECORD_ACTION_SHARE_VIDEO_LINK', 'POST_RECORD_ACTION_SHARE_VIDEO_LINK'), ('POST_RECORD_ACTION_SHORTEN_URL', 'POST_RECORD_ACTION_SHORTEN_URL'), ('POST_RECORD_ACTION_READY_TO_SHIP', 'POST_RECORD_ACTION_READY_TO_SHIP'), ('POST_RECORD_ACTION_SEND_SMS', 'POST_RECORD_ACTION_SEND_SMS'), ('COMPANY_NAME', 'COMPANY_NAME'), ('COMPANY_ADDRESS', 'COMPANY_ADDRESS'), ('COMPANY_ADMIN_TEL', 'COMPANY_ADMIN_TEL'), ('RECEIPT_FOOTER', 'RECEIPT_FOOTER'), ('PRICE_SMS', 'PRICE_SMS'), ('PRICE_RECORD', 'PRICE_RECORD'), ('PRICE_SYNC_ORDER', 'PRICE_SYNC_ORDER')], max_length=200),
        ),
    ]
