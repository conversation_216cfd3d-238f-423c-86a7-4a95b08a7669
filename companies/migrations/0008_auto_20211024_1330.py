# Generated by Django 2.2.24 on 2021-10-24 13:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0007_auto_20211023_0530'),
    ]

    operations = [
        migrations.AlterField(
            model_name='settingvalue',
            name='key',
            field=models.CharField(choices=[('ZORT_API_KEY', 'ZORT_API_KEY'), ('ZORT_API_SECRET', 'ZORT_API_SECRET'), ('ZORT_STORENAME', 'ZORT_STORENAME'), ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'), ('PRINTNODE_PICKORDER_PRINTER_ID', 'PRINTNODE_PICKORDER_PRINTER_ID'), ('PRINTNODE_PICKORDER_PRINTER_TYPE', 'PRINTNODE_PICKORDER_PRINTER_TYPE'), ('PRINTNODE_AIRWAY_BILL_PRINTER_ID', 'PRINTNODE_AIRWAY_BILL_PRINTER_ID'), ('WEBHOOK_URL', 'WEBHOOK_URL'), ('WEBHOOK_SECRET', 'WEBHOOK_SECRET'), ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'), ('SMS_NAME', 'SMS_NAME'), ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'), ('COMPANY_NAME', 'COMPANY_NAME'), ('COMPANY_ADDRESS', 'COMPANY_ADDRESS')], max_length=200),
        ),
        migrations.AlterField(
            model_name='settingvalue',
            name='value',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
