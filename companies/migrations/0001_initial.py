# Generated by Django 2.2.24 on 2021-10-12 01:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='SettingValue',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.CharField(max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='SettingKey',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=200)),
                ('dtype', models.CharField(choices=[('str', 'str'), ('bool', 'bool'), ('int', 'int'), ('float', 'float'), ('dict', 'dict'), ('list', 'list')], max_length=5)),
                ('default', models.CharField(max_length=200)),
                ('help', models.TextField()),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('setting_value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.SettingValue')),
            ],
        ),
        migrations.AddField(
            model_name='company',
            name='settings',
            field=models.ManyToManyField(through='companies.SettingKey', to='companies.SettingValue'),
        ),
    ]
