# Generated by Django 3.2.19 on 2025-04-02 05:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0078_auto_20250324_1401'),
    ]

    operations = [
        migrations.AlterField(
            model_name='settingvalue',
            name='key',
            field=models.CharField(choices=[('ENABLE_ORDER_CENTER', 'ENABLE_ORDER_CENTER'), ('RECORD_SHOW_START_BUTTON', 'RECORD_SHOW_START_BUTTON'), ('RECORD_GMAIL_ALLOWED_LIST', 'RECORD_GMAIL_ALLOWED_LIST'), ('RECORD_PIECE_SCAN_MODE_ENABLE', 'RECORD_PIECE_SCAN_MODE_ENABLE'), ('RECORD_CHECK_BUTTON_DISABLE', 'RECORD_CHECK_BUTTON_DISABLE'), ('CONTROL_CODE_FORCE_UPPERCASE_LETTERS', 'CONTROL_CODE_FORCE_UPPERCASE_LETTERS'), ('AUTO_SYNC_ORDER_ENABLE', 'AUTO_SYNC_ORDER_ENABLE'), ('ZORT_API_V2', 'ZORT_API_V2'), ('ZORT_API_V2_READY_TO_SHIP_HOOK', 'ZORT_API_V2_READY_TO_SHIP_HOOK'), ('ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG', 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG'), ('ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK', 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK'), ('ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG', 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG'), ('ZORT_API_V2_WEBHOOKS', 'ZORT_API_V2_WEBHOOKS'), ('ZORT_API_KEY', 'ZORT_API_KEY'), ('ZORT_API_SECRET', 'ZORT_API_SECRET'), ('ZORT_STORENAME', 'ZORT_STORENAME'), ('USE_MULTIPLE_VRICH', 'USE_MULTIPLE_VRICH'), ('VRICH_HOST', 'VRICH_HOST'), ('VRICH_TOKEN', 'VRICH_TOKEN'), ('VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE', 'VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE'), ('VRICH_ORDER_NUMBER_APPEND_TRACKING', 'VRICH_ORDER_NUMBER_APPEND_TRACKING'), ('IMIND_HOST', 'IMIND_HOST'), ('IMIND_TOKEN', 'IMIND_TOKEN'), ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'), ('PRINTNODE_AIRWAY_BILL_PRINTER_ID', 'PRINTNODE_AIRWAY_BILL_PRINTER_ID'), ('PRINTNODE_PICKORDER_PRINTER_ID', 'PRINTNODE_PICKORDER_PRINTER_ID'), ('PRINTNODE_PICKORDER_PRINTER_TYPE', 'PRINTNODE_PICKORDER_PRINTER_TYPE'), ('PICKORDER_AUTO_PRINT_ENABLE', 'PICKORDER_AUTO_PRINT_ENABLE'), ('PICKORDER_STORE_BLOCKED_LIST', 'PICKORDER_STORE_BLOCKED_LIST'), ('DOBYBOT_AIRWAYBILL_SHOP_LIST', 'DOBYBOT_AIRWAYBILL_SHOP_LIST'), ('DOBYBOT_AIRWAYBILL_SENDER_NAME', 'DOBYBOT_AIRWAYBILL_SENDER_NAME'), ('DOBYBOT_AIRWAYBILL_SENDER_PHONE', 'DOBYBOT_AIRWAYBILL_SENDER_PHONE'), ('DOBYBOT_AIRWAYBILL_SENDER_ADDRESS', 'DOBYBOT_AIRWAYBILL_SENDER_ADDRESS'), ('DOBYBOT_AIRWAYBILL_FOOTER', 'DOBYBOT_AIRWAYBILL_FOOTER'), ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'), ('SALES_CHANNEL_SMS_BLOCKED_LIST', 'SALES_CHANNEL_SMS_BLOCKED_LIST'), ('SMS_SENDER', 'SMS_SENDER'), ('SMS_AUTO_RETRY_PENDING', 'SMS_AUTO_RETRY_PENDING'), ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'), ('LON_READY_TO_SHIP_REMARK_MESSAGE', 'LON_READY_TO_SHIP_REMARK_MESSAGE'), ('LON_MORE_DETAIL_LINK', 'LON_MORE_DETAIL_LINK'), ('SMS_FIXCASE_OPEN_MESSAGE', 'SMS_FIXCASE_OPEN_MESSAGE'), ('SMS_FIXCASE_CLOSE_MESSGE', 'SMS_FIXCASE_CLOSE_MESSGE'), ('LON_FIXCASE_OPEN_MESSAGE', 'LON_FIXCASE_OPEN_MESSAGE'), ('LON_FIXCASE_CLOSE_MESSAGE', 'LON_FIXCASE_CLOSE_MESSAGE'), ('EMAIL_SENDER', 'EMAIL_SENDER'), ('SMS_MULTI_PACKAGE_MESSAGE', 'SMS_MULTI_PACKAGE_MESSAGE'), ('MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS', 'MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS'), ('CONFIRM_RECORD_PASSWORD_ENABLE', 'CONFIRM_RECORD_PASSWORD_ENABLE'), ('CONFIRM_RECORD_PASSWORD_CONFIG', 'CONFIRM_RECORD_PASSWORD_CONFIG'), ('SUPERVISOR_PASSWORD', 'SUPERVISOR_PASSWORD'), ('STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG', 'STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG'), ('POST_RECORD_ACTION_SHARE_VIDEO_LINK', 'POST_RECORD_ACTION_SHARE_VIDEO_LINK'), ('POST_RECORD_ACTION_CREATE_ORDER', 'POST_RECORD_ACTION_CREATE_ORDER'), ('POST_RECORD_ACTION_CREATE_ORDER_PATTERN', 'POST_RECORD_ACTION_CREATE_ORDER_PATTERN'), ('POST_RECORD_ACTION_SHORTEN_URL', 'POST_RECORD_ACTION_SHORTEN_URL'), ('POST_RECORD_ACTION_READY_TO_SHIP', 'POST_RECORD_ACTION_READY_TO_SHIP'), ('POST_RECORD_ACTION_SEND_SMS', 'POST_RECORD_ACTION_SEND_SMS'), ('POST_RECORD_ACTION_SEND_EMAIL', 'POST_RECORD_ACTION_SEND_EMAIL'), ('POST_RECORD_ACTION_SEND_LON', 'POST_RECORD_ACTION_SEND_LON'), ('POST_RECORD_ACTION_SEND_WEBHOOK_BEFORE_VIDEO_UPLOAD', 'POST_RECORD_ACTION_SEND_WEBHOOK_BEFORE_VIDEO_UPLOAD'), ('PRE_RECORD_WEBHOOK_ENABLE', 'PRE_RECORD_WEBHOOK_ENABLE'), ('PRE_RECORD_WEBHOOK_CONFIG', 'PRE_RECORD_WEBHOOK_CONFIG'), ('WEBHOOK_AUTH_REQUEST_CONFIG', 'WEBHOOK_AUTH_REQUEST_CONFIG'), ('POST_RECORD_ACTION_WEBHOOK_ENABLE', 'POST_RECORD_ACTION_WEBHOOK_ENABLE'), ('POST_RECORD_WEBHOOK_CONFIG', 'POST_RECORD_WEBHOOK_CONFIG'), ('POST_RECORD_WEBHOOK_URL', 'POST_RECORD_WEBHOOK_URL'), ('POST_RECORD_WEBHOOK_AUTH', 'POST_RECORD_WEBHOOK_AUTH'), ('READY_TO_SHIP_REQUIRE_VIDEO', 'READY_TO_SHIP_REQUIRE_VIDEO'), ('READY_TO_SHIP_REQUIRE_NO_FIXCASES', 'READY_TO_SHIP_REQUIRE_NO_FIXCASES'), ('READY_TO_SHIP_ORDER_NUMBER_REGEX', 'READY_TO_SHIP_ORDER_NUMBER_REGEX'), ('READY_TO_SHIP_WEBHOOK_ENABLE', 'READY_TO_SHIP_WEBHOOK_ENABLE'), ('READY_TO_SHIP_WEBHOOK_URL', 'READY_TO_SHIP_WEBHOOK_URL'), ('READY_TO_SHIP_WEBHOOK_AUTH', 'READY_TO_SHIP_WEBHOOK_AUTH'), ('FIXCASE_SEND_OPEN_MESSAGE', 'FIXCASE_SEND_OPEN_MESSAGE'), ('FIXCASE_SEND_CLOSE_MESSAGE', 'FIXCASE_SEND_CLOSE_MESSAGE'), ('FIXCASE_LON_SEND_OPEN_MESSAGE', 'FIXCASE_LON_SEND_OPEN_MESSAGE'), ('FIXCASE_LON_SEND_CLOSE_MESSAGE', 'FIXCASE_LON_SEND_CLOSE_MESSAGE'), ('POST_CLOSE_FIXCASE_ACTION_ID', 'POST_CLOSE_FIXCASE_ACTION_ID'), ('COMPANY_NAME', 'COMPANY_NAME'), ('COMPANY_ADDRESS', 'COMPANY_ADDRESS'), ('COMPANY_ADMIN_TEL', 'COMPANY_ADMIN_TEL'), ('COMPANY_ADMIN_LINE_NAME', 'COMPANY_ADMIN_LINE_NAME'), ('COMPANY_ADMIN_LINE_URL', 'COMPANY_ADMIN_LINE_URL'), ('HIDE_RECEIPT', 'HIDE_RECEIPT'), ('HIDE_SCAN_LOGS', 'HIDE_SCAN_LOGS'), ('RECEIPT_FOOTER', 'RECEIPT_FOOTER'), ('GOOGLE_DRIVE_SHARE_DRIVE_ID', 'GOOGLE_DRIVE_SHARE_DRIVE_ID'), ('GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE', 'GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE'), ('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', 'GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION'), ('LOW_RECORD_CREDIT_WARNING_AMOUNT', 'LOW_RECORD_CREDIT_WARNING_AMOUNT'), ('LOW_SMS_CREDIT_WARNING_AMOUNT', 'LOW_SMS_CREDIT_WARNING_AMOUNT'), ('USE_MQTT', 'USE_MQTT'), ('SHOPEE_CHAT_API', 'SHOPEE_CHAT_API'), ('SHOPEE_READY_TO_SHIP_MESSAGE', 'SHOPEE_READY_TO_SHIP_MESSAGE'), ('LAZADA_CHAT_API', 'LAZADA_CHAT_API'), ('LAZADA_READY_TO_SHIP_MESSAGE', 'LAZADA_READY_TO_SHIP_MESSAGE'), ('SHOPEE_API', 'SHOPEE_API'), ('LAZADA_API', 'LAZADA_API'), ('TIKTOK_SHOP_API', 'TIKTOK_SHOP_API'), ('TIKTOK_SHOP_V2_API', 'TIKTOK_SHOP_V2_API'), ('SHIPNITY_API', 'SHIPNITY_API'), ('WEIGHT_SCALE_PREFER_UNIT', 'WEIGHT_SCALE_PREFER_UNIT'), ('WEIGHT_SLIP_PRINT_OPTIONS', 'WEIGHT_SLIP_PRINT_OPTIONS'), ('WEIGHT_ONLY_MODE_ENABLE', 'WEIGHT_ONLY_MODE_ENABLE'), ('WEIGHT_ONLY_MODE_PRINT_METHOD', 'WEIGHT_ONLY_MODE_PRINT_METHOD'), ('SHOPEE_CHAT_DAILY_LIMIT', 'SHOPEE_CHAT_DAILY_LIMIT'), ('LAZADA_CHAT_DAILY_LIMIT', 'LAZADA_CHAT_DAILY_LIMIT'), ('HOMEPAGE_URL', 'HOMEPAGE_URL'), ('ENABLE_NO_RECORD_MODE', 'ENABLE_NO_RECORD_MODE'), ('DISABLED_PRE_RECORD_CHECK', 'DISABLED_PRE_RECORD_CHECK'), ('ALLOW_NEGATIVE_CREDIT', 'ALLOW_NEGATIVE_CREDIT'), ('ZORT_PRE_ORDER_TRANSFORM', 'ZORT_PRE_ORDER_TRANSFORM'), ('API_SMS_REFUND_UNDELIVERED', 'API_SMS_REFUND_UNDELIVERED'), ('SMS_WEBHOOK_ENABLE', 'SMS_WEBHOOK_ENABLE'), ('SMS_WEBHOOK_CONFIG', 'SMS_WEBHOOK_CONFIG'), ('RECORD_FAILED_TRANSCODE_SHOW_ERROR', 'RECORD_FAILED_TRANSCODE_SHOW_ERROR'), ('PICK_ITEM_RENAME_BY_PRODUCT', 'PICK_ITEM_RENAME_BY_PRODUCT'), ('PICKORDER_PRINT_START_DATE', 'PICKORDER_PRINT_START_DATE'), ('CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE', 'CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE'), ('PRINT_RECEIPT_TAX_INVOICE_2_COPY', 'PRINT_RECEIPT_TAX_INVOICE_2_COPY'), ('POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK', 'POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK'), ('DOBYBOT_CONNECT_VERSION', 'DOBYBOT_CONNECT_VERSION'), ('DOBYSYNC_CLIENT', 'DOBYSYNC_CLIENT'), ('NOCNOC_API', 'NOCNOC_API'), ('LINE_MY_SHOP_API', 'LINE_MY_SHOP_API'), ('AUTO_GENERATE_TRACKINGNO_ENABLE', 'AUTO_GENERATE_TRACKINGNO_ENABLE'), ('AUTO_GENERATE_TRACKINGNO_PROVIDER', 'AUTO_GENERATE_TRACKINGNO_PROVIDER'), ('AUTO_GENERATE_TRACKINGNO_SHOPS', 'AUTO_GENERATE_TRACKINGNO_SHOPS'), ('SHIPPING_SENDER', 'SHIPPING_SENDER'), ('FLASH_API_CONFIG', 'FLASH_API_CONFIG'), ('WATERMARK_LOGO', 'WATERMARK_LOGO'), ('ENABLE_WATERMARK', 'ENABLE_WATERMARK'), ('RECORD_TAGS', 'RECORD_TAGS'), ('LOG_ROCKET_ENABLE', 'LOG_ROCKET_ENABLE'), ('WINDOW_NETWORK_FOLDER_PATH', 'WINDOW_NETWORK_FOLDER_PATH'), ('ENABLE_P_SCORE', 'ENABLE_P_SCORE'), ('RECORD_SCAN_INPUT_DISABLE', 'RECORD_SCAN_INPUT_DISABLE'), ('ETAX_SELLER', 'ETAX_SELLER'), ('ETAX_DOCUMENT_FOLDER_ID', 'ETAX_DOCUMENT_FOLDER_ID'), ('ETAX_DOCUMENT_ID_MODE', 'ETAX_DOCUMENT_ID_MODE'), ('ETAX_ENABLE_PICKSLIP_QR_CODE', 'ETAX_ENABLE_PICKSLIP_QR_CODE'), ('ETAX_ALLOW_ON_ORDER_RECEIVED', 'ETAX_ALLOW_ON_ORDER_RECEIVED'), ('ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED', 'ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED'), ('ETAX_CREATE_ACTION_CREDIT_AMOUNT', 'ETAX_CREATE_ACTION_CREDIT_AMOUNT'), ('ETAX_UPDATE_ACTION_CREDIT_AMOUNT', 'ETAX_UPDATE_ACTION_CREDIT_AMOUNT'), ('ETAX_CANCEL_ACTION_CREDIT_AMOUNT', 'ETAX_CANCEL_ACTION_CREDIT_AMOUNT'), ('ETAX_CREDIT_NOTE_ACTION_CREDIT_AMOUNT', 'ETAX_CREDIT_NOTE_ACTION_CREDIT_AMOUNT'), ('ETAX_DOC_CREATE_DATE', 'ETAX_DOC_CREATE_DATE'), ('ETAX_HEADER_MESSAGE_BILL', 'ETAX_HEADER_MESSAGE_BILL'), ('ETAX_FOOTER_MESSAGE_BILL', 'ETAX_FOOTER_MESSAGE_BILL'), ('ETAX_SMS_MESSAGE', 'ETAX_SMS_MESSAGE'), ('BKP_PREDICT_ORDER_LEVEL', 'BKP_PREDICT_ORDER_LEVEL'), ('ETAX_HIDE_SKU', 'ETAX_HIDE_SKU'), ('ETAX_ORDER_OPEN_DAYS', 'ETAX_ORDER_OPEN_DAYS'), ('EASY_ETAX_TOKEN', 'EASY_ETAX_TOKEN'), ('ETAX_BRANCHES', 'ETAX_BRANCHES'), ('ETAX_SMS_CREDIT_AMOUNT', 'ETAX_SMS_CREDIT_AMOUNT'), ('RECEIPT_SHOW_ETAX_BUTTON', 'RECEIPT_SHOW_ETAX_BUTTON'), ('ETAX_SHOW_CONSENT_CHECKBOX', 'ETAX_SHOW_CONSENT_CHECKBOX'), ('ETAX_SHOW_DOBYBOT_CONTACT', 'ETAX_SHOW_DOBYBOT_CONTACT'), ('ETAX_RETRIEVAL_DAY', 'ETAX_RETRIEVAL_DAY'), ('ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST', 'ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST'), ('ETAX_AUTO_START_DATE', 'ETAX_AUTO_START_DATE'), ('ETAX_WELCOME_MESSAGE', 'ETAX_WELCOME_MESSAGE'), ('ETAX_AUTO_CANCEL_MODE', 'ETAX_AUTO_CANCEL_MODE'), ('ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF', 'ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF'), ('ETAX_EMAIL_REPLACEMENT', 'ETAX_EMAIL_REPLACEMENT'), ('ETAX_LOGO', 'ETAX_LOGO'), ('ETAX_AUTO_ZORT_TAG', 'ETAX_AUTO_ZORT_TAG'), ('ETAX_WEBHOOK_ENABLE', 'ETAX_WEBHOOK_ENABLE'), ('ETAX_WEBHOOK_CONFIG', 'ETAX_WEBHOOK_CONFIG'), ('ETAX_AUTO_ACTION_WHEN_ORDER_CHANGE', 'ETAX_AUTO_ACTION_WHEN_ORDER_CHANGE'), ('ETAX_BUYER_REQUEST_ETAX_HANDLING_MODE', 'ETAX_BUYER_REQUEST_ETAX_HANDLING_MODE'), ('ETAX_SMS_SENDER', 'ETAX_SMS_SENDER'), ('ETAX_BYPASS_CUTOFF_DATE_CHECK', 'ETAX_BYPASS_CUTOFF_DATE_CHECK'), ('JAMBOLIVE_WEBHOOK_CONFIG', 'JAMBOLIVE_WEBHOOK_CONFIG'), ('CUSTOM_BRAND', 'CUSTOM_BRAND'), ('ETAX_REQUEST_WEBHOOK_ENABLE', 'ETAX_REQUEST_WEBHOOK_ENABLE'), ('ETAX_REQUEST_WEBHOOK_CONFIG', 'ETAX_REQUEST_WEBHOOK_CONFIG'), ('RECORD_SCAN_SERIAL_NO_ENABLE', 'RECORD_SCAN_SERIAL_NO_ENABLE'), ('RECORD_SCAN_SKU_ENABLE', 'RECORD_SCAN_SKU_ENABLE')], db_index=True, max_length=200),
        ),
    ]
