# Generated by Django 2.2.4 on 2022-06-23 18:42

import companies.models.models
import core.storages
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0022_auto_20220621_2203'),
    ]

    operations = [
        migrations.AddField(
            model_name='company', name='settings_json',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),),
        migrations.AlterField(
            model_name='publicfile', name='ref', field=models.FileField(
                storage=core.storages.GoogleCloudPublicStorage(),
                upload_to=companies.models.public_file_directory),)
    ]
