# Generated by Django 2.2.24 on 2021-10-17 02:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0004_auto_20211012_0203'),
    ]

    operations = [
        migrations.AlterField(
            model_name='settingvalue',
            name='key',
            field=models.CharField(choices=[('ZORT_API_KEY', 'ZORT_API_KEY'), ('ZORT_API_SECRET', 'ZORT_API_SECRET'), ('ZORT_STORENAME', 'ZORT_STORENAME'), ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'), ('PRINTNODE_PICK_ORDER_PRINTER_ID', 'PRINTNODE_PICK_ORDER_PRINTER_ID'), ('WEBHOOK_URL', 'WEBHOOK_URL'), ('WEBHOOK_SECRET', 'WEBHOOK_SECRET'), ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'), ('SMS_NAME', 'SMS_NAME'), ('ADDRESS_LINE_1', 'ADDRESS_LINE_1'), ('ADDRESS_LINE_2', 'ADDRESS_LINE_2')], max_length=200),
        ),
    ]
