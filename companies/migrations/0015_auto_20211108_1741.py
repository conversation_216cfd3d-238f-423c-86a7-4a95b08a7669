# Generated by Django 2.2.24 on 2021-11-08 17:41
import uuid
from django.db import migrations


def insert_company_uuid(app, schema_editor):
    Company = app.get_model("companies", "Company")
    for company in Company.objects.all():
        company.uuid = uuid.uuid4()
        company.save()


class Migration(migrations.Migration):

    dependencies = [
        ("companies", "0014_auto_20211108_1741"),
    ]

    operations = [migrations.RunPython(insert_company_uuid)]
