# Generated by Django 3.2.19 on 2023-08-03 03:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import nanoid.generate
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('companies', '0043_alter_settingvalue_key'),
    ]

    operations = [
        migrations.AlterField(
            model_name='settingvalue',
            name='key',
            field=models.CharField(choices=[('ENABLE_ORDER_CENTER', 'ENABLE_ORDER_CENTER'), ('RECORD_SHOW_START_BUTTON', 'RECORD_SHOW_START_BUTTON'), ('RECORD_GMAIL_ALLOWED_LIST', 'RECORD_GMAIL_ALLOWED_LIST'), ('RECORD_PIECE_SCAN_MODE_ENABLE', 'RECORD_PIECE_SCAN_MODE_ENABLE'), ('CONTROL_CODE_FORCE_UPPERCASE_LETTERS', 'CONTROL_CODE_FORCE_UPPERCASE_LETTERS'), ('AUTO_SYNC_ORDER_ENABLE', 'AUTO_SYNC_ORDER_ENABLE'), ('ZORT_API_V2', 'ZORT_API_V2'), ('ZORT_API_V2_READY_TO_SHIP_HOOK', 'ZORT_API_V2_READY_TO_SHIP_HOOK'), ('ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG', 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG'), ('ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK', 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK'), ('ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG', 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG'), ('ZORT_API_V2_WEBHOOKS', 'ZORT_API_V2_WEBHOOKS'), ('ZORT_API_KEY', 'ZORT_API_KEY'), ('ZORT_API_SECRET', 'ZORT_API_SECRET'), ('ZORT_STORENAME', 'ZORT_STORENAME'), ('USE_MULTIPLE_VRICH', 'USE_MULTIPLE_VRICH'), ('VRICH_HOST', 'VRICH_HOST'), ('VRICH_TOKEN', 'VRICH_TOKEN'), ('VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE', 'VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE'), ('IMIND_HOST', 'IMIND_HOST'), ('IMIND_TOKEN', 'IMIND_TOKEN'), ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'), ('PRINTNODE_PICKORDER_PRINTER_ID', 'PRINTNODE_PICKORDER_PRINTER_ID'), ('PRINTNODE_PICKORDER_PRINTER_TYPE', 'PRINTNODE_PICKORDER_PRINTER_TYPE'), ('PICKORDER_AUTO_PRINT_ENABLE', 'PICKORDER_AUTO_PRINT_ENABLE'), ('DOBYBOT_AIRWAYBILL_SHOP_LIST', 'DOBYBOT_AIRWAYBILL_SHOP_LIST'), ('DOBYBOT_AIRWAYBILL_SENDER_NAME', 'DOBYBOT_AIRWAYBILL_SENDER_NAME'), ('DOBYBOT_AIRWAYBILL_SENDER_PHONE', 'DOBYBOT_AIRWAYBILL_SENDER_PHONE'), ('DOBYBOT_AIRWAYBILL_SENDER_ADDRESS', 'DOBYBOT_AIRWAYBILL_SENDER_ADDRESS'), ('DOBYBOT_AIRWAYBILL_FOOTER', 'DOBYBOT_AIRWAYBILL_FOOTER'), ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'), ('SALES_CHANNEL_SMS_BLOCKED_LIST', 'SALES_CHANNEL_SMS_BLOCKED_LIST'), ('SMS_SENDER', 'SMS_SENDER'), ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'), ('SMS_FIXCASE_OPEN_MESSAGE', 'SMS_FIXCASE_OPEN_MESSAGE'), ('SMS_FIXCASE_CLOSE_MESSGE', 'SMS_FIXCASE_CLOSE_MESSGE'), ('EMAIL_SENDER', 'EMAIL_SENDER'), ('SMS_MULTI_PACKAGE_MESSAGE', 'SMS_MULTI_PACKAGE_MESSAGE'), ('MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS', 'MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS'), ('CONFIRM_RECORD_PASSWORD_ENABLE', 'CONFIRM_RECORD_PASSWORD_ENABLE'), ('CONFIRM_RECORD_PASSWORD_CONFIG', 'CONFIRM_RECORD_PASSWORD_CONFIG'), ('SUPERVISOR_PASSWORD', 'SUPERVISOR_PASSWORD'), ('STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG', 'STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG'), ('POST_RECORD_ACTION_SHARE_VIDEO_LINK', 'POST_RECORD_ACTION_SHARE_VIDEO_LINK'), ('POST_RECORD_ACTION_CREATE_ORDER', 'POST_RECORD_ACTION_CREATE_ORDER'), (
                'POST_RECORD_ACTION_CREATE_ORDER_PATTERN', 'POST_RECORD_ACTION_CREATE_ORDER_PATTERN'), ('POST_RECORD_ACTION_SHORTEN_URL', 'POST_RECORD_ACTION_SHORTEN_URL'), ('POST_RECORD_ACTION_READY_TO_SHIP', 'POST_RECORD_ACTION_READY_TO_SHIP'), ('POST_RECORD_ACTION_SEND_SMS', 'POST_RECORD_ACTION_SEND_SMS'), ('POST_RECORD_ACTION_SEND_EMAIL', 'POST_RECORD_ACTION_SEND_EMAIL'), ('PRE_RECORD_WEBHOOK_ENABLE', 'PRE_RECORD_WEBHOOK_ENABLE'), ('PRE_RECORD_WEBHOOK_CONFIG', 'PRE_RECORD_WEBHOOK_CONFIG'), ('WEBHOOK_AUTH_REQUEST_CONFIG', 'WEBHOOK_AUTH_REQUEST_CONFIG'), ('POST_RECORD_ACTION_WEBHOOK_ENABLE', 'POST_RECORD_ACTION_WEBHOOK_ENABLE'), ('POST_RECORD_WEBHOOK_CONFIG', 'POST_RECORD_WEBHOOK_CONFIG'), ('POST_RECORD_WEBHOOK_URL', 'POST_RECORD_WEBHOOK_URL'), ('POST_RECORD_WEBHOOK_AUTH', 'POST_RECORD_WEBHOOK_AUTH'), ('READY_TO_SHIP_REQUIRE_VIDEO', 'READY_TO_SHIP_REQUIRE_VIDEO'), ('READY_TO_SHIP_REQUIRE_NO_FIXCASES', 'READY_TO_SHIP_REQUIRE_NO_FIXCASES'), ('READY_TO_SHIP_ORDER_NUMBER_REGEX', 'READY_TO_SHIP_ORDER_NUMBER_REGEX'), ('READY_TO_SHIP_WEBHOOK_ENABLE', 'READY_TO_SHIP_WEBHOOK_ENABLE'), ('READY_TO_SHIP_WEBHOOK_URL', 'READY_TO_SHIP_WEBHOOK_URL'), ('READY_TO_SHIP_WEBHOOK_AUTH', 'READY_TO_SHIP_WEBHOOK_AUTH'), ('FIXCASE_SEND_OPEN_MESSAGE', 'FIXCASE_SEND_OPEN_MESSAGE'), ('FIXCASE_SEND_CLOSE_MESSAGE', 'FIXCASE_SEND_CLOSE_MESSAGE'), ('POST_CLOSE_FIXCASE_ACTION_ID', 'POST_CLOSE_FIXCASE_ACTION_ID'), ('COMPANY_NAME', 'COMPANY_NAME'), ('COMPANY_ADDRESS', 'COMPANY_ADDRESS'), ('COMPANY_ADMIN_TEL', 'COMPANY_ADMIN_TEL'), ('HIDE_RECEIPT', 'HIDE_RECEIPT'), ('RECEIPT_FOOTER', 'RECEIPT_FOOTER'), ('GOOGLE_DRIVE_SHARE_DRIVE_ID', 'GOOGLE_DRIVE_SHARE_DRIVE_ID'), ('GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE', 'GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE'), ('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', 'GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION'), ('LOW_RECORD_CREDIT_WARNING_AMOUNT', 'LOW_RECORD_CREDIT_WARNING_AMOUNT'), ('LOW_SMS_CREDIT_WARNING_AMOUNT', 'LOW_SMS_CREDIT_WARNING_AMOUNT'), ('USE_MQTT', 'USE_MQTT'), ('SHOPEE_CHAT_API', 'SHOPEE_CHAT_API'), ('SHOPEE_READY_TO_SHIP_MESSAGE', 'SHOPEE_READY_TO_SHIP_MESSAGE'), ('LAZADA_CHAT_API', 'LAZADA_CHAT_API'), ('LAZADA_READY_TO_SHIP_MESSAGE', 'LAZADA_READY_TO_SHIP_MESSAGE'), ('SHOPEE_API', 'SHOPEE_API'), ('LAZADA_API', 'LAZADA_API'), ('TIKTOK_SHOP_API', 'TIKTOK_SHOP_API'), ('SHIPNITY_API', 'SHIPNITY_API'), ('WEIGHT_SCALE_PREFER_UNIT', 'WEIGHT_SCALE_PREFER_UNIT'), ('WEIGHT_SLIP_PRINT_OPTIONS', 'WEIGHT_SLIP_PRINT_OPTIONS'), ('SHOPEE_CHAT_DAILY_LIMIT', 'SHOPEE_CHAT_DAILY_LIMIT'), ('LAZADA_CHAT_DAILY_LIMIT', 'LAZADA_CHAT_DAILY_LIMIT'), ('HOMEPAGE_URL', 'HOMEPAGE_URL'), ('ENABLE_NO_RECORD_MODE', 'ENABLE_NO_RECORD_MODE')], db_index=True, max_length=200),
        ),
        migrations.CreateModel(
            name='HistoricalCompany',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('uuid', models.CharField(db_index=True, default=nanoid.generate, max_length=36)),
                ('name', models.CharField(max_length=200)),
                ('account_suffix', models.CharField(blank=True, max_length=200)),
                ('create_date', models.DateTimeField(blank=True, editable=False)),
                ('expire_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_active_remark', models.TextField(blank=True, default='')),
                ('package', models.CharField(choices=[
                 ('001', 'FULL INTEGRATION'), ('002', 'RECORD ONLY')], default='001', max_length=3)),
                ('settings_json', models.JSONField(blank=True, default=dict, null=True)),
                ('force_redirect', models.BooleanField(default=False)),
                ('receipt_logo', models.URLField(blank=True, null=True)),
                ('web_logo', models.URLField(blank=True, null=True)),
                ('register_dobybot_connect', models.BooleanField(default=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[
                 ('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(
                    null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical company',
                'verbose_name_plural': 'historical companies',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
