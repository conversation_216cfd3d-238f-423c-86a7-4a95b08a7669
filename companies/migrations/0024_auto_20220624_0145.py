# Generated by Django 2.2.4 on 2022-06-23 18:45

from distutils.util import strtobool
import json
from django.db import migrations

from companies.models.settings import SETTING_KEYS


def migrate_to_settings_json(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')

    for company in Company.objects.prefetch_related('settings').all():
        settings_json = {}

        for setting_value in company.settings.all():
            key = setting_value.key
            value = setting_value.value

            try:
                setting_key = SETTING_KEYS[key]
            except KeyError:
                continue

            if value is None:
                pass
            elif setting_key.dtype is bool:
                try:
                    value = True if strtobool(value) else False
                except Exception:
                    value = False
            elif setting_key.dtype is int:
                value = int(value)
            elif setting_key.dtype is float:
                value = float(value)
            elif setting_key.dtype is dict or setting_key.dtype is list:
                try:
                    value = json.loads(value)
                except Exception:
                    value = None
            settings_json[key] = value

        company.settings_json = settings_json
        company.save()


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0023_auto_20220624_0142'),
    ]

    operations = [
        migrations.RunPython(migrate_to_settings_json)
    ]
