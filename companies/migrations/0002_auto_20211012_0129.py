# Generated by Django 2.2.24 on 2021-10-12 01:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='company',
            name='settings',
        ),
        migrations.RemoveField(
            model_name='settingkey',
            name='company',
        ),
        migrations.RemoveField(
            model_name='settingkey',
            name='setting_value',
        ),
        migrations.AddField(
            model_name='settingvalue',
            name='company',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='settings', to='companies.Company'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='settingvalue',
            name='key',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='companies.SettingKey'),
            preserve_default=False,
        ),
    ]
