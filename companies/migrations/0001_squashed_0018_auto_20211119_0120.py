# Generated by Django 2.2.24 on 2021-11-28 18:36

from django.db import migrations, models
import django.db.models.deletion
import uuid


# Functions from the following migrations need manual copying.
# Move them and any dependencies into this file, then update the
# RunPython operations to refer to the local versions:
# companies.migrations.0015_auto_20211108_1741

# def insert_company_uuid(app, schema_editor):
#     Company = app.get_model("companies", "Company")
#     for company in Company.objects.all():
#         company.uuid = uuid.uuid4()
#         company.save()


class Migration(migrations.Migration):

    replaces = [
        ('companies', '0001_initial'),
        ('companies', '0002_auto_20211012_0129'),
        ('companies', '0003_auto_20211012_0201'),
        ('companies', '0004_auto_20211012_0203'),
        ('companies', '0005_auto_20211017_0213'),
        ('companies', '0006_auto_20211020_1911'),
        ('companies', '0007_auto_20211023_0530'),
        ('companies', '0008_auto_20211024_1330'),
        ('companies', '0009_auto_20211025_1317'),
        ('companies', '0010_company_account_suffix'),
        ('companies', '0011_auto_20211030_1225'),
        ('companies', '0012_auto_20211030_1410'),
        ('companies', '0013_auto_20211031_1454'),
        ('companies', '0014_auto_20211108_1741'),
        ('companies', '0015_auto_20211108_1741'),
        ('companies', '0016_auto_20211108_1744'),
        ('companies', '0017_auto_20211108_1801'),
        ('companies', '0018_auto_20211119_0120')]

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.AutoField(auto_created=True,
                 primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('account_suffix', models.CharField(default='@cusway.com', max_length=200)),
                ('uuid', models.UUIDField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='SettingValue',
            fields=[
                ('id', models.AutoField(auto_created=True,
                 primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.CharField(blank=True, max_length=400, null=True)),
                ('company', models.ForeignKey(
                    default=1, on_delete=django.db.models.deletion.CASCADE,
                    related_name='settings', to='companies.Company')),
                ('key', models.CharField(
                    choices=[('AUTO_SYNC_ORDER_ENABLE', 'AUTO_SYNC_ORDER_ENABLE'),
                             ('ZORT_API_KEY', 'ZORT_API_KEY'),
                             ('ZORT_API_SECRET', 'ZORT_API_SECRET'),
                             ('ZORT_STORENAME', 'ZORT_STORENAME'),
                             ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'),
                             ('PRINTNODE_PICKORDER_PRINTER_ID',
                              'PRINTNODE_PICKORDER_PRINTER_ID'),
                             ('PRINTNODE_PICKORDER_PRINTER_TYPE',
                              'PRINTNODE_PICKORDER_PRINTER_TYPE'),
                             ('PICKORDER_AUTO_PRINT_ENABLE', 'PICKORDER_AUTO_PRINT_ENABLE'),
                             ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'),
                             ('SMS_SENDER', 'SMS_SENDER'),
                             ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'),
                             ('POST_RECORD_ACTION_SHARE_VIDEO_LINK',
                              'POST_RECORD_ACTION_SHARE_VIDEO_LINK'),
                             ('POST_RECORD_ACTION_SHORTEN_URL',
                              'POST_RECORD_ACTION_SHORTEN_URL'),
                             ('POST_RECORD_ACTION_READY_TO_SHIP',
                              'POST_RECORD_ACTION_READY_TO_SHIP'),
                             ('POST_RECORD_ACTION_SEND_SMS', 'POST_RECORD_ACTION_SEND_SMS'),
                             ('COMPANY_NAME', 'COMPANY_NAME'),
                             ('COMPANY_ADDRESS', 'COMPANY_ADDRESS'),
                             ('COMPANY_ADMIN_TEL', 'COMPANY_ADMIN_TEL')],
                    max_length=200)),
            ],
            options={
                'unique_together': {('key', 'company')},
            },
        ),
        # migrations.RunPython(
        #     code=companies.migrations.0015_auto_20211108_1741.insert_company_uuid,
        # ),
        migrations.AlterField(
            model_name='company',
            name='uuid',
            field=models.UUIDField(db_index=True, default=uuid.uuid4, unique=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='uuid',
            field=models.CharField(db_index=True, default=uuid.uuid4,
                                   max_length=36, unique=True),
        ),
        migrations.AlterField(
            model_name='settingvalue',
            name='key',
            field=models.CharField(
                choices=[('AUTO_SYNC_ORDER_ENABLE', 'AUTO_SYNC_ORDER_ENABLE'),
                         ('ZORT_API_KEY', 'ZORT_API_KEY'),
                         ('ZORT_API_SECRET', 'ZORT_API_SECRET'),
                         ('ZORT_STORENAME', 'ZORT_STORENAME'),
                         ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'),
                         ('PRINTNODE_PICKORDER_PRINTER_ID',
                          'PRINTNODE_PICKORDER_PRINTER_ID'),
                         ('PRINTNODE_PICKORDER_PRINTER_TYPE',
                          'PRINTNODE_PICKORDER_PRINTER_TYPE'),
                         ('PICKORDER_AUTO_PRINT_ENABLE', 'PICKORDER_AUTO_PRINT_ENABLE'),
                         ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'),
                         ('SMS_SENDER', 'SMS_SENDER'),
                         ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'),
                         ('POST_RECORD_ACTION_SHARE_VIDEO_LINK',
                          'POST_RECORD_ACTION_SHARE_VIDEO_LINK'),
                         ('POST_RECORD_ACTION_SHORTEN_URL',
                          'POST_RECORD_ACTION_SHORTEN_URL'),
                         ('POST_RECORD_ACTION_READY_TO_SHIP',
                          'POST_RECORD_ACTION_READY_TO_SHIP'),
                         ('POST_RECORD_ACTION_SEND_SMS', 'POST_RECORD_ACTION_SEND_SMS'),
                         ('COMPANY_NAME', 'COMPANY_NAME'),
                         ('COMPANY_ADDRESS', 'COMPANY_ADDRESS'),
                         ('COMPANY_ADMIN_TEL', 'COMPANY_ADMIN_TEL'),
                         ('RECEIPT_FOOTER', 'RECEIPT_FOOTER')],
                max_length=200),
        ),
        migrations.AlterField(
            model_name='settingvalue',
            name='value',
            field=models.TextField(blank=True, null=True),
        ),
    ]
