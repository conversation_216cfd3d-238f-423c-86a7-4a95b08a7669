# Generated by Django 2.2.4 on 2022-10-29 04:13

from django.db import migrations, models
import nanoid.generate


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0029_auto_20221012_1437'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company', name='uuid', field=models.CharField(
                db_index=True, default=nanoid.generate, max_length=36, unique=True),),
        migrations.AlterField(
            model_name='settingvalue', name='key', field=models.CharField(
                choices=[('ENABLE_ORDER_CENTER', 'ENABLE_ORDER_CENTER'),
                         ('RECORD_SHOW_START_BUTTON', 'RECORD_SHOW_START_BUTTON'),
                         ('RECORD_GMAIL_ALLOWED_LIST', 'RECORD_GMAIL_ALLOWED_LIST'),
                         ('RECORD_PIECE_SCAN_MODE_ENABLE', 'RECORD_PIECE_SCAN_MODE_ENABLE'),
                         ('CONTROL_CODE_FORCE_UPPERCASE_LETTERS',
                          'CONTROL_CODE_FORCE_UPPERCASE_LETTERS'),
                         ('AUTO_SYNC_ORDER_ENABLE', 'AUTO_SYNC_ORDER_ENABLE'),
                         ('ZORT_API_KEY', 'ZORT_API_KEY'),
                         ('ZORT_API_SECRET', 'ZORT_API_SECRET'),
                         ('ZORT_STORENAME', 'ZORT_STORENAME'),
                         ('USE_MULTIPLE_VRICH', 'USE_MULTIPLE_VRICH'),
                         ('VRICH_HOST', 'VRICH_HOST'),
                         ('VRICH_TOKEN', 'VRICH_TOKEN'),
                         ('VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE',
                          'VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE'),
                         ('IMIND_HOST', 'IMIND_HOST'),
                         ('IMIND_TOKEN', 'IMIND_TOKEN'),
                         ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'),
                         ('PRINTNODE_PICKORDER_PRINTER_ID', 'PRINTNODE_PICKORDER_PRINTER_ID'),
                         ('PRINTNODE_PICKORDER_PRINTER_TYPE', 'PRINTNODE_PICKORDER_PRINTER_TYPE'),
                         ('PICKORDER_AUTO_PRINT_ENABLE', 'PICKORDER_AUTO_PRINT_ENABLE'),
                         ('DOBYBOT_AIRWAYBILL_SHOP_LIST', 'DOBYBOT_AIRWAYBILL_SHOP_LIST'),
                         ('DOBYBOT_AIRWAYBILL_SENDER_NAME', 'DOBYBOT_AIRWAYBILL_SENDER_NAME'),
                         ('DOBYBOT_AIRWAYBILL_SENDER_PHONE', 'DOBYBOT_AIRWAYBILL_SENDER_PHONE'),
                         ('DOBYBOT_AIRWAYBILL_SENDER_ADDRESS', 'DOBYBOT_AIRWAYBILL_SENDER_ADDRESS'),
                         ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'),
                         ('SALES_CHANNEL_SMS_BLOCKED_LIST', 'SALES_CHANNEL_SMS_BLOCKED_LIST'),
                         ('SMS_SENDER', 'SMS_SENDER'),
                         ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'),
                         ('SMS_FIXCASE_OPEN_MESSAGE', 'SMS_FIXCASE_OPEN_MESSAGE'),
                         ('SMS_FIXCASE_CLOSE_MESSGE', 'SMS_FIXCASE_CLOSE_MESSGE'),
                         ('EMAIL_SENDER', 'EMAIL_SENDER'),
                         ('SMS_MULTI_PACKAGE_MESSAGE', 'SMS_MULTI_PACKAGE_MESSAGE'),
                         ('MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS',
                          'MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS'),
                         ('CONFIRM_RECORD_PASSWORD_ENABLE', 'CONFIRM_RECORD_PASSWORD_ENABLE'),
                         ('SUPERVISOR_PASSWORD', 'SUPERVISOR_PASSWORD'),
                         ('POST_RECORD_ACTION_SHARE_VIDEO_LINK',
                          'POST_RECORD_ACTION_SHARE_VIDEO_LINK'),
                         ('POST_RECORD_ACTION_SHORTEN_URL', 'POST_RECORD_ACTION_SHORTEN_URL'),
                         ('POST_RECORD_ACTION_READY_TO_SHIP', 'POST_RECORD_ACTION_READY_TO_SHIP'),
                         ('POST_RECORD_ACTION_SEND_SMS', 'POST_RECORD_ACTION_SEND_SMS'),
                         ('POST_RECORD_ACTION_SEND_EMAIL', 'POST_RECORD_ACTION_SEND_EMAIL'),
                         ('PRE_RECORD_WEBHOOK_ENABLE', 'PRE_RECORD_WEBHOOK_ENABLE'),
                         ('PRE_RECORD_WEBHOOK_CONFIG', 'PRE_RECORD_WEBHOOK_CONFIG'),
                         ('POST_RECORD_ACTION_WEBHOOK_ENABLE', 'POST_RECORD_ACTION_WEBHOOK_ENABLE'),
                         ('POST_RECORD_WEBHOOK_CONFIG', 'POST_RECORD_WEBHOOK_CONFIG'),
                         ('POST_RECORD_WEBHOOK_URL', 'POST_RECORD_WEBHOOK_URL'),
                         ('POST_RECORD_WEBHOOK_AUTH', 'POST_RECORD_WEBHOOK_AUTH'),
                         ('READY_TO_SHIP_REQUIRE_VIDEO', 'READY_TO_SHIP_REQUIRE_VIDEO'),
                         ('READY_TO_SHIP_REQUIRE_NO_FIXCASES', 'READY_TO_SHIP_REQUIRE_NO_FIXCASES'),
                         ('READY_TO_SHIP_ORDER_NUMBER_REGEX', 'READY_TO_SHIP_ORDER_NUMBER_REGEX'),
                         ('READY_TO_SHIP_WEBHOOK_ENABLE', 'READY_TO_SHIP_WEBHOOK_ENABLE'),
                         ('READY_TO_SHIP_WEBHOOK_URL', 'READY_TO_SHIP_WEBHOOK_URL'),
                         ('READY_TO_SHIP_WEBHOOK_AUTH', 'READY_TO_SHIP_WEBHOOK_AUTH'),
                         ('FIXCASE_SEND_OPEN_MESSAGE', 'FIXCASE_SEND_OPEN_MESSAGE'),
                         ('FIXCASE_SEND_CLOSE_MESSAGE', 'FIXCASE_SEND_CLOSE_MESSAGE'),
                         ('POST_CLOSE_FIXCASE_ACTION_ID', 'POST_CLOSE_FIXCASE_ACTION_ID'),
                         ('COMPANY_NAME', 'COMPANY_NAME'),
                         ('COMPANY_ADDRESS', 'COMPANY_ADDRESS'),
                         ('COMPANY_ADMIN_TEL', 'COMPANY_ADMIN_TEL'),
                         ('HIDE_RECEIPT', 'HIDE_RECEIPT'),
                         ('RECEIPT_FOOTER', 'RECEIPT_FOOTER'),
                         ('GOOGLE_DRIVE_SHARE_DRIVE_ID', 'GOOGLE_DRIVE_SHARE_DRIVE_ID'),
                         ('GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE', 'GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE'),
                         ('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION',
                          'GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION'),
                         ('LOW_RECORD_CREDIT_WARNING_AMOUNT', 'LOW_RECORD_CREDIT_WARNING_AMOUNT'),
                         ('LOW_SMS_CREDIT_WARNING_AMOUNT', 'LOW_SMS_CREDIT_WARNING_AMOUNT'),
                         ('USE_MQTT', 'USE_MQTT')],
                db_index=True, max_length=200),), ]
