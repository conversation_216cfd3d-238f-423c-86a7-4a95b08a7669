# Generated by Django 3.2.19 on 2023-10-09 04:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0046_alter_settingvalue_key'),
    ]

    operations = [
        migrations.AlterField(
            model_name='settingvalue',
            name='key',
            field=models.CharField(choices=[('ENABLE_ORDER_CENTER', 'ENABLE_ORDER_CENTER'), ('RECORD_SHOW_START_BUTTON', 'RECORD_SHOW_START_BUTTON'), ('RECORD_GMAIL_ALLOWED_LIST', 'RECORD_GMAIL_ALLOWED_LIST'), ('RECORD_PIECE_SCAN_MODE_ENABLE', 'RECORD_PIECE_SCAN_MODE_ENABLE'), ('RECORD_CHECK_BUTTON_DISABLE', 'RECORD_CHECK_BUTTON_DISABLE'), ('CONTROL_CODE_FORCE_UPPERCASE_LETTERS', 'CONTROL_CODE_FORCE_UPPERCASE_LETTERS'), ('AUTO_SYNC_ORDER_ENABLE', 'AUTO_SYNC_ORDER_ENABLE'), ('ZORT_API_V2', 'ZORT_API_V2'), ('ZORT_API_V2_READY_TO_SHIP_HOOK', 'ZORT_API_V2_READY_TO_SHIP_HOOK'), ('ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG', 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG'), ('ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK', 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK'), ('ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG', 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG'), ('ZORT_API_V2_WEBHOOKS', 'ZORT_API_V2_WEBHOOKS'), ('ZORT_API_KEY', 'ZORT_API_KEY'), ('ZORT_API_SECRET', 'ZORT_API_SECRET'), ('ZORT_STORENAME', 'ZORT_STORENAME'), ('USE_MULTIPLE_VRICH', 'USE_MULTIPLE_VRICH'), ('VRICH_HOST', 'VRICH_HOST'), ('VRICH_TOKEN', 'VRICH_TOKEN'), ('VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE', 'VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE'), ('VRICH_ORDER_NUMBER_APPEND_TRACKING', 'VRICH_ORDER_NUMBER_APPEND_TRACKING'), ('IMIND_HOST', 'IMIND_HOST'), ('IMIND_TOKEN', 'IMIND_TOKEN'), ('PRINTNODE_API_KEY', 'PRINTNODE_API_KEY'), ('PRINTNODE_PICKORDER_PRINTER_ID', 'PRINTNODE_PICKORDER_PRINTER_ID'), ('PRINTNODE_PICKORDER_PRINTER_TYPE', 'PRINTNODE_PICKORDER_PRINTER_TYPE'), ('PICKORDER_AUTO_PRINT_ENABLE', 'PICKORDER_AUTO_PRINT_ENABLE'), ('PICKORDER_STORE_BLOCKED_LIST', 'PICKORDER_STORE_BLOCKED_LIST'), ('DOBYBOT_AIRWAYBILL_SHOP_LIST', 'DOBYBOT_AIRWAYBILL_SHOP_LIST'), ('DOBYBOT_AIRWAYBILL_SENDER_NAME', 'DOBYBOT_AIRWAYBILL_SENDER_NAME'), ('DOBYBOT_AIRWAYBILL_SENDER_PHONE', 'DOBYBOT_AIRWAYBILL_SENDER_PHONE'), ('DOBYBOT_AIRWAYBILL_SENDER_ADDRESS', 'DOBYBOT_AIRWAYBILL_SENDER_ADDRESS'), ('DOBYBOT_AIRWAYBILL_FOOTER', 'DOBYBOT_AIRWAYBILL_FOOTER'), ('LINE_NOTIFY_TOKEN', 'LINE_NOTIFY_TOKEN'), ('SALES_CHANNEL_SMS_BLOCKED_LIST', 'SALES_CHANNEL_SMS_BLOCKED_LIST'), ('SMS_SENDER', 'SMS_SENDER'), ('SMS_READY_TO_SHIP_MESSAGE', 'SMS_READY_TO_SHIP_MESSAGE'), ('SMS_FIXCASE_OPEN_MESSAGE', 'SMS_FIXCASE_OPEN_MESSAGE'), ('SMS_FIXCASE_CLOSE_MESSGE', 'SMS_FIXCASE_CLOSE_MESSGE'), ('EMAIL_SENDER', 'EMAIL_SENDER'), ('SMS_MULTI_PACKAGE_MESSAGE', 'SMS_MULTI_PACKAGE_MESSAGE'), ('MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS', 'MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS'), ('CONFIRM_RECORD_PASSWORD_ENABLE', 'CONFIRM_RECORD_PASSWORD_ENABLE'), ('CONFIRM_RECORD_PASSWORD_CONFIG', 'CONFIRM_RECORD_PASSWORD_CONFIG'), ('SUPERVISOR_PASSWORD', 'SUPERVISOR_PASSWORD'), ('STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG', 'STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG'), ('POST_RECORD_ACTION_SHARE_VIDEO_LINK', 'POST_RECORD_ACTION_SHARE_VIDEO_LINK'), ('POST_RECORD_ACTION_CREATE_ORDER', 'POST_RECORD_ACTION_CREATE_ORDER'), ('POST_RECORD_ACTION_CREATE_ORDER_PATTERN', 'POST_RECORD_ACTION_CREATE_ORDER_PATTERN'), ('POST_RECORD_ACTION_SHORTEN_URL', 'POST_RECORD_ACTION_SHORTEN_URL'), ('POST_RECORD_ACTION_READY_TO_SHIP', 'POST_RECORD_ACTION_READY_TO_SHIP'), ('POST_RECORD_ACTION_SEND_SMS', 'POST_RECORD_ACTION_SEND_SMS'), ('POST_RECORD_ACTION_SEND_EMAIL', 'POST_RECORD_ACTION_SEND_EMAIL'), ('PRE_RECORD_WEBHOOK_ENABLE', 'PRE_RECORD_WEBHOOK_ENABLE'), ('PRE_RECORD_WEBHOOK_CONFIG', 'PRE_RECORD_WEBHOOK_CONFIG'), ('WEBHOOK_AUTH_REQUEST_CONFIG', 'WEBHOOK_AUTH_REQUEST_CONFIG'), ('POST_RECORD_ACTION_WEBHOOK_ENABLE', 'POST_RECORD_ACTION_WEBHOOK_ENABLE'), ('POST_RECORD_WEBHOOK_CONFIG', 'POST_RECORD_WEBHOOK_CONFIG'), ('POST_RECORD_WEBHOOK_URL', 'POST_RECORD_WEBHOOK_URL'), ('POST_RECORD_WEBHOOK_AUTH', 'POST_RECORD_WEBHOOK_AUTH'), ('READY_TO_SHIP_REQUIRE_VIDEO', 'READY_TO_SHIP_REQUIRE_VIDEO'), ('READY_TO_SHIP_REQUIRE_NO_FIXCASES', 'READY_TO_SHIP_REQUIRE_NO_FIXCASES'), ('READY_TO_SHIP_ORDER_NUMBER_REGEX', 'READY_TO_SHIP_ORDER_NUMBER_REGEX'), ('READY_TO_SHIP_WEBHOOK_ENABLE', 'READY_TO_SHIP_WEBHOOK_ENABLE'), ('READY_TO_SHIP_WEBHOOK_URL', 'READY_TO_SHIP_WEBHOOK_URL'), ('READY_TO_SHIP_WEBHOOK_AUTH', 'READY_TO_SHIP_WEBHOOK_AUTH'), ('FIXCASE_SEND_OPEN_MESSAGE', 'FIXCASE_SEND_OPEN_MESSAGE'), ('FIXCASE_SEND_CLOSE_MESSAGE', 'FIXCASE_SEND_CLOSE_MESSAGE'), ('POST_CLOSE_FIXCASE_ACTION_ID', 'POST_CLOSE_FIXCASE_ACTION_ID'), ('COMPANY_NAME', 'COMPANY_NAME'), ('COMPANY_ADDRESS', 'COMPANY_ADDRESS'), ('COMPANY_ADMIN_TEL', 'COMPANY_ADMIN_TEL'), ('HIDE_RECEIPT', 'HIDE_RECEIPT'), ('RECEIPT_FOOTER', 'RECEIPT_FOOTER'), ('GOOGLE_DRIVE_SHARE_DRIVE_ID', 'GOOGLE_DRIVE_SHARE_DRIVE_ID'), ('GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE', 'GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE'), ('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', 'GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION'), ('LOW_RECORD_CREDIT_WARNING_AMOUNT', 'LOW_RECORD_CREDIT_WARNING_AMOUNT'), ('LOW_SMS_CREDIT_WARNING_AMOUNT', 'LOW_SMS_CREDIT_WARNING_AMOUNT'), ('USE_MQTT', 'USE_MQTT'), ('SHOPEE_CHAT_API', 'SHOPEE_CHAT_API'), ('SHOPEE_READY_TO_SHIP_MESSAGE', 'SHOPEE_READY_TO_SHIP_MESSAGE'), ('LAZADA_CHAT_API', 'LAZADA_CHAT_API'), ('LAZADA_READY_TO_SHIP_MESSAGE', 'LAZADA_READY_TO_SHIP_MESSAGE'), ('SHOPEE_API', 'SHOPEE_API'), ('LAZADA_API', 'LAZADA_API'), ('TIKTOK_SHOP_API', 'TIKTOK_SHOP_API'), ('SHIPNITY_API', 'SHIPNITY_API'), ('WEIGHT_SCALE_PREFER_UNIT', 'WEIGHT_SCALE_PREFER_UNIT'), ('WEIGHT_SLIP_PRINT_OPTIONS', 'WEIGHT_SLIP_PRINT_OPTIONS'), ('WEIGHT_ONLY_MODE_ENABLE', 'WEIGHT_ONLY_MODE_ENABLE'), ('WEIGHT_ONLY_MODE_PRINT_METHOD', 'WEIGHT_ONLY_MODE_PRINT_METHOD'), ('SHOPEE_CHAT_DAILY_LIMIT', 'SHOPEE_CHAT_DAILY_LIMIT'), ('LAZADA_CHAT_DAILY_LIMIT', 'LAZADA_CHAT_DAILY_LIMIT'), ('HOMEPAGE_URL', 'HOMEPAGE_URL'), ('ENABLE_NO_RECORD_MODE', 'ENABLE_NO_RECORD_MODE'), ('DISABLED_PRE_RECORD_CHECK', 'DISABLED_PRE_RECORD_CHECK'), ('ALLOW_NEGATIVE_CREDIT', 'ALLOW_NEGATIVE_CREDIT')], db_index=True, max_length=200),
        ),
    ]
