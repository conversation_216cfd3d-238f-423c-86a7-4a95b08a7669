from dynamic_rest.routers import DynamicRouter
from django.urls import path
from . import views


router = DynamicRouter()
router.register("resource/companies", views.CompanyDynamicViewSet)


# /api/companies/
urlpatterns = router.urls + [
    path("settings/", views.CompanySettingAPI.as_view()),
    path("wallet-balance/", views.CompanyWalletBalanceAPI.as_view()),
    path("companies/create/", views.CompanyCreateAPI.as_view()),
    path("choices/<str:choice_name>/", views.CompanyChoicesAPI.as_view()),
    path("company/rename/", views.CompanyRenameAPI.as_view()),
    path("company/change-admin/", views.CompanyChangeAdminAPI.as_view()),
    # v1 & v2
    path("marketplaces/", views.MarketplaceListAPI.as_view()),
    path("dobybot-connect/seller/auth/", views.DobybotConnectSellerAuthAPI.as_view()),
    path(
        "dobybot-connect/seller/auth/shopee-chat/",
        views.DobybotConnectShopeeChatAuthAPI.as_view(),
    ),
    path(
        "dobybot-connect/seller/auth/lazada-chat/",
        views.DobybotConnectLazadaChatAuthAPI.as_view(),
    ),
    path(
        "dobybot-connect/seller/auth/shipnity/",
        views.DobybotConnectShipnityAuthAPI.as_view(),
    ),
    path(
        "dobybot-connect/seller/auth/zort/", views.DobybotConnectZortAuthAPI.as_view()
    ),
    path(
        "dobybot-connect/seller/disconnect/",
        views.DobybotConnectDisconnectShopAPI.as_view(),
    ),
    path(
        "dobybot-connect/shop-settings/", views.DobybotConnectShopSettingAPI.as_view()
    ),
    path("dobybot-connect/sync-orders/", views.DobybotConnectSyncOrder.as_view()),
    path(
        "dobybot-connect/sync-shop-orders/", views.DobybotConnectSyncShopOrder.as_view()
    ),
    path(
        "dobybot-connect/zort/webhook/",
        views.DobybotConnectUpdateZortWebhookAPI.as_view(),
    ),
    # versions
    path("dobybot-connect/switch-to-v2/", views.DobybotConnectSwitchToV2API.as_view()),
    path("dobybot-connect/switch-to-v1/", views.DobybotConnectSwitchToV1API.as_view()),
    # v2 only (dobysync)
    path(
        "dobybot-connect/seller/auth/nocnoc/",
        views.DobybotConnectNocNocAuthAPI.as_view(),
    ),
    path(
        "dobybot-connect/seller/auth/linemyshop/",
        views.DobybotConnectLineMyShopAuthAPI.as_view(),
    ),
    path(
        "dobybot-connect/seller/auth/nex_gen_commerce/",
        views.DobybotConnectNexGenCommerceAuthAPI.as_view(),
    ),
    path(
        "dobybot-connect/seller/auth/woo-commerce/",
        views.DobybotConnectWooCommerceAuthAPI.as_view(),
    ),
    path("dobybot-connect/sync-task/<task_id>/", views.GetSyncTaskAPI.as_view()),
    path(
        "dobybot-connect/sync-shop-products/",
        views.DobybotConnectSyncShopProducts.as_view(),
    ),
    # tasks
    path("tasks/handler/webhook-auth/", views.WebhookAuthTaskHandlerAPI.as_view()),
    path("tasks/scheduler/webhook-auth/", views.WebhookAuthTaskScheduleAPI.as_view()),
    # path('companies/delete/', views.CompanyCreateAPI.as_view()),
]
