import json
import nanoid
from typing import Literal, Union
from django.db import models
from django.utils import timezone
from distutils.util import strtobool

from companies.models.settings import SETTING_KEYS
from core.storages import default_public_storage
from services.dobybot_connect.dobybot_connect import DobybotConnectService
from services.dobybot_connect.dobysync import DobySyncClientService
from services.flash.flash_service import FlashService
from services.kerry.kerry_service import KerryService
from services.thaipost.thaipost_service import (
    ThaiPostEmsService,
    ThaiPostRegService,
    ThaiPostService,
)
from services.imind.imind import IMindService
from services.printnode import PrintNodeService
from services.vrich.vrich import VRichService
from sms.services import SmsService
from services.zort import ZortService
from sms.services.email import EmailService
from sms.services.sms import AntsSmsService, JeraSmsService
from wallets.models import RecordTransaction
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>
from simple_history.models import HistoricalRecords


class SettingValue(models.Model):
    KEY_CHOICES = [(key, key) for key in SETTING_KEYS]
    key = models.CharField(max_length=200, choices=KEY_CHOICES, db_index=True)
    company = models.ForeignKey(
        "companies.Company", on_delete=models.CASCADE, related_name="settings"
    )
    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="user_settings",
        blank=True,
        null=True,
    )
    value = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = [["key", "user", "company"]]

    def __str__(self) -> str:
        setting_key = SETTING_KEYS[self.key]
        return f"{self.company.name} {setting_key.help}"

    @property
    def help(self):
        s = SETTING_KEYS.get(self.key)
        return s.help if s else ""

    @property
    def default(self):
        s = SETTING_KEYS.get(self.key)
        return s.default if s else ""

    @property
    def dtype(self):
        s = SETTING_KEYS.get(self.key)
        return str(s.dtype) if s else ""

    @property
    def group(self):
        s = SETTING_KEYS.get(self.key)
        return s.group if s else ""

    @property
    def allow_customer_to_edit(self):
        s = SETTING_KEYS.get(self.key)
        return s.allow_customer_to_edit if s else ""


class Company(models.Model):
    uuid = models.CharField(
        unique=True, db_index=True, default=nanoid.generate, max_length=36
    )
    name = models.CharField(max_length=200, unique=True)
    account_suffix = models.CharField(max_length=200, unique=True)
    create_date = models.DateTimeField(auto_now_add=True)
    expire_date = models.DateField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_active_remark = models.TextField(blank=True, default="")

    PACKAGE_FULL_INTEGRATION = "001"
    PACKAGE_RECORD_ONLY = "002"
    PACKAGE_CHIOCES = [
        (PACKAGE_FULL_INTEGRATION, "FULL INTEGRATION"),
        (PACKAGE_RECORD_ONLY, "RECORD ONLY"),
    ]
    package = models.CharField(
        max_length=3, choices=PACKAGE_CHIOCES, default=PACKAGE_FULL_INTEGRATION
    )
    settings_json = models.JSONField(blank=True, null=True, default=dict)
    force_redirect = models.BooleanField(default=False)
    receipt_logo = models.URLField(blank=True, null=True)
    web_logo = models.URLField(blank=True, null=True)

    # compnay feature flag
    # {
    #   "order_center": true,
    #   "record": true,
    #   "sms_chat": true,
    #   "product_management": true,
    #   "fix_case": true,
    #   "printing": true,
    #   "shipping": true,
    # }
    ORDER_CENTER = "order_center"
    RECORD = "record"
    SMS_CHAT = "sms_chat"
    PRODUCT_MANAGEMENT = "product_management"
    FIX_CASE = "fix_case"
    PRINTING = "printing"
    SHIPPING = "shipping"
    ETAX = "etax"

    feature_flag = models.JSONField(blank=True, null=True, default=dict)

    register_dobybot_connect = models.BooleanField(default=False)
    history = HistoricalRecords()
    is_setup = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "companies"

    @property
    def setting_json_str(self) -> str:
        return json.dumps(self.settings_json, indent=4, sort_keys=True)

    @property
    def user_count(self):
        return self.user_set.count()

    @property
    def record_balance(self):
        try:
            return self.wallet.get_record_balance()
        except Company.wallet.RelatedObjectDoesNotExist:
            return 0

    @property
    def sms_balance(self):
        try:
            return self.wallet.sms_balance
        except Company.wallet.RelatedObjectDoesNotExist:
            return 0

    @property
    def sms_credit_onhold(self):
        try:
            return self.wallet.sms_credit_onhold
        except Company.wallet.RelatedObjectDoesNotExist:
            return 0

    @property
    def google_drive_usage(self):
        try:
            return self.wallet.google_drive_usage
        except Company.wallet.RelatedObjectDoesNotExist:
            return 0

    @property
    def google_drive_limit(self):
        try:
            return self.wallet.get_google_drive_limit()
        except Company.wallet.RelatedObjectDoesNotExist:
            return 0

    @property
    def wallet_last_update_datetime(self):
        tx = RecordTransaction.objects.filter(wallet=self.wallet).last()
        if not tx:
            return timezone.now()
        return tx.create_date

    def __str__(self) -> str:
        return self.name

    def get_web_logo_url(self):
        if self.web_logo:
            return self.web_logo
        return "https://cloud.dobybot.com/logo-with-text.png"

    def get_zort_service(self) -> ZortService:
        return ZortService(
            storename=self.get_setting("ZORT_STORENAME"),
            apikey=self.get_setting("ZORT_API_KEY"),
            apisecret=self.get_setting("ZORT_API_SECRET"),
        )

    def get_vrich_service(self, user_id: int = None) -> VRichService:
        host = self.get_setting("VRICH_HOST", user_id)
        token = self.get_setting("VRICH_TOKEN", user_id)
        return VRichService(host, token)

    def get_imind_service(self) -> IMindService:
        return IMindService(
            host=self.get_setting("IMIND_HOST"), token=self.get_setting("IMIND_TOKEN")
        )

    def get_printnode_service(self) -> PrintNodeService:
        return PrintNodeService(
            api_key=self.get_setting("PRINTNODE_API_KEY"),
        )

    def get_sms_service(self, provider: Literal["jera", "ants"] = "jera") -> SmsService:
        if provider == "ants":
            return self.get_ants_sms_service()

        if provider == "jera":
            return self.get_jera_sms_service()

        raise NotImplementedError(f'Invalid provider: "{provider}"')

    def get_email_service(self):
        return EmailService(company=self)

    def get_ants_sms_service(self) -> AntsSmsService:
        return AntsSmsService(company=self)

    def get_jera_sms_service(self) -> JeraSmsService:
        return JeraSmsService(company=self)

    def get_dobybot_connect_service(
        self,
    ) -> Union["DobybotConnectService", "DobySyncClientService"]:
        version = self.get_setting("DOBYBOT_CONNECT_VERSION")
        if version == 1:
            return DobybotConnectService(company=self)
        if version == 2:
            return DobySyncClientService.from_company(self)

    def get_flash_service(self) -> "FlashService":
        return FlashService.from_company(self)

    def get_thaipost_ems_service(self) -> "ThaiPostEmsService":
        return ThaiPostEmsService.new()

    def get_thaipost_reg_service(self) -> "ThaiPostRegService":
        return ThaiPostRegService.new()

    def get_kerry_service(self) -> "KerryService":
        return KerryService.from_company()

    def get_etax_service(self):
        from services.etax_invoice.etax_service import ETaxService

        etax_service = ETaxService.from_company(self)
        return etax_service

    def get_all_settings(self) -> dict:
        """Return all company's settings"""
        settings = {}
        for key in SETTING_KEYS:
            settings[key] = self.get_setting(key)
        return settings

    def get_setting(self, key: str, user_id: int = None, raise_exception=None) -> any:
        """Return single setting by key"""
        setting_key = SETTING_KEYS[key]

        try:
            json_key = self.get_json_setting_key(key, user_id)
            value = self.settings_json[json_key]
        except KeyError:
            if callable(setting_key.default):
                value = setting_key.default()
            else:
                value = setting_key.default

        if raise_exception and value is None:
            raise Exception(f'Invalid company setting "{key}"')

        if type(value) is str and setting_key.dtype is not str:
            if setting_key.dtype is bool:
                return bool(strtobool(value))
            if setting_key.dtype is int:
                return int(value)
            if setting_key.dtype is float:
                return float(value)
            if setting_key.dtype is dict or setting_key.dtype is list:
                return json.loads(value)
        return value

    def set_setting(self, key: str, value, user_id: int = None, commit=True):
        json_key = self.get_json_setting_key(key, user_id)
        self.settings_json[json_key] = value

        if commit:
            self.save(update_fields=["settings_json"])

    @staticmethod
    def get_json_setting_key(key, user_id):
        return f"{key}.{user_id}" if user_id else key


def public_file_directory(instance, filename):
    return f"company/{instance.company_id}/public/{filename}"


class PublicFile(models.Model):
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)
    ref = models.FileField(
        storage=default_public_storage, upload_to=public_file_directory
    )
