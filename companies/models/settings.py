import json
from dataclasses import dataclass


@dataclass
class SettingKey:
    key: str
    dtype: any
    default: str = None
    help: str = ""
    group: str = ""
    allow_customer_to_edit: bool = False
    secret: bool = False


DEFAULTS = {
    "ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG": {
        "warehousecode": "",
        "shipment": {
            "lazada": "lex",
            "shopee": "none",
            "tiktok": "pickup",
            "other": "none",
        },
        "address": None,
        "pickuptime": None,
        "booking": 0,
    },
    "ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG": {
        "warehousecode": "",
        "status": "Shipping",
    },
    "ZORT_API_V2_WEBHOOKS": {
        "add_order_url": "",
        "update_order_url": "",
        "update_order_tracking_url": "",
    },
    "WEIGHT_SLIP_PRINT_CONFIG": {"paper": "100 x 75", "rotate": 0},
    "JAMBOLIVE_WEBHOOK_CONFIG": {
        "url": "https://sea.jambolive.tv/console/dobybot/webhook/",
        "method": "POST",
        "headers": {},
    },
    "CF_SHOPS_WEBHOOK_CONFIG": {
        "url": "https://cf-shops.com/api/dobybotCallback/postRecord",
        "method": "POST",
        "headers": {},
    },
}


SETTING_KEYS = {
    "ENABLE_ORDER_CENTER": SettingKey(
        key="ENABLE_ORDER_CENTER",
        dtype=bool,
        default="1",
        help="เลิกใช้งานเปลี่ยนไปใช้ Company Package แทน",
        allow_customer_to_edit=False,
    ),
    # Recording
    "RECORD_SHOW_START_BUTTON": SettingKey(
        key="RECORD_SHOW_START_BUTTON",
        dtype=bool,
        default="1",
        help="แสดงปุ่มเริ่มบันทึกใน record",
        allow_customer_to_edit=False,
    ),
    "RECORD_GMAIL_ALLOWED_LIST": SettingKey(
        key="RECORD_GMAIL_ALLOWED_LIST",
        dtype=list,
        default="[]",
        help="List ของ Gmail ที่อนุญาติให้ใช้งานได้ในหน้า record",
        allow_customer_to_edit=False,
    ),
    "RECORD_PIECE_SCAN_MODE_ENABLE": SettingKey(
        key="RECORD_PIECE_SCAN_MODE_ENABLE",
        dtype=bool,
        default="0",
        help="เปิด/ปิดการใช้งานโหมด Piece Scan",
        allow_customer_to_edit=True,
    ),
    "RECORD_CHECK_BUTTON_DISABLE": SettingKey(
        key="RECORD_CHECK_BUTTON_DISABLE",
        dtype=bool,
        default="0",
        help="ปิดการใช้งานปุ่ม Check (ปุ่ม + เวลาเช็คสินค้า) ในหน้า record",
        allow_customer_to_edit=True,
    ),
    "CONTROL_CODE_FORCE_UPPERCASE_LETTERS": SettingKey(
        key="CONTROL_CODE_FORCE_UPPERCASE_LETTERS",
        dtype=bool,
        default="0",
        help="แปลง control code เป็นตัวอักษรพิมพ์ใหญ่อัตโนมัติ แก้ปัญหา Flash express บาร์โค๊ดห่วย บางทีแสกนได้ตัวพิมพ์เล็ก",
        allow_customer_to_edit=False,
    ),
    # Zort
    "AUTO_SYNC_ORDER_ENABLE": SettingKey(
        key="AUTO_SYNC_ORDER_ENABLE",
        dtype=bool,
        default="0",
        help="เปิด/ปิดการ sync order จาก zort อัตโนมัติ",
        allow_customer_to_edit=True,
    ),
    "ZORT_API_V2": SettingKey(
        key="ZORT_API_V2",
        dtype=dict,
        allow_customer_to_edit=True,
        default="{}",
        help="ตั้งค่า ZORT API (V2, dobybot-connect)",
    ),
    "ZORT_API_V2_READY_TO_SHIP_HOOK": SettingKey(
        key="ZORT_API_V2_READY_TO_SHIP_HOOK",
        dtype=str,
        allow_customer_to_edit=True,
        default="",
        help=(
            "ส่งคำสั่ง ready to ship ไปยัง zort.\n"
            + "ค่าที่เลือกได้\n"
            + "- 'pre_record' ส่งเมื่อเริ่มอัดวิดีโอ\n",
            "- 'post_record' ส่งเมื่ออัดวิดีโอเสร็จ\n",
            "- 'ready_to_ship' ส่งเมื่อแสกน ready to ship\n",
            "- '' ไม่ส่ง\n",
        ),
    ),
    "ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG": SettingKey(
        key="ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG",
        dtype=dict,
        allow_customer_to_edit=True,
        default=json.dumps(DEFAULTS["ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG"]),
        help="ตั้งค่า Zort ready to ship",
    ),
    "ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK": SettingKey(
        key="ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK",
        dtype=str,
        allow_customer_to_edit=True,
        default="",
        help=(
            "ส่งคำสั่ง update order status ไปยัง zort.\n"
            + "ค่าที่เลือกได้\n"
            + "- 'pre_record' ส่งเมื่อเริ่มอัดวิดีโอ\n",
            "- 'post_record' ส่งเมื่ออัดวิดีโอเสร็จ\n",
            "- 'ready_to_ship' ส่งเมื่อแสกน ready to ship\n",
            "- '' ไม่ส่ง\n",
        ),
    ),
    "ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG": SettingKey(
        key="ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG",
        dtype=dict,
        allow_customer_to_edit=True,
        default=json.dumps(DEFAULTS["ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG"]),
        help="ตั้งค่า Zort update order status",
    ),
    "ZORT_API_V2_WEBHOOKS": SettingKey(
        key="ZORT_API_V2_WEBHOOKS",
        dtype=dict,
        allow_customer_to_edit=True,
        default=json.dumps(DEFAULTS["ZORT_API_V2_WEBHOOKS"]),
        help="ตั้งค่า webhook จาก zort -> dobybot",
    ),
    "ZORT_API_KEY": SettingKey(
        key="ZORT_API_KEY", dtype=str, allow_customer_to_edit=True, secret=True
    ),
    "ZORT_API_SECRET": SettingKey(
        key="ZORT_API_SECRET", dtype=str, allow_customer_to_edit=True, secret=True
    ),
    "ZORT_STORENAME": SettingKey(
        key="ZORT_STORENAME",
        dtype=str,
        allow_customer_to_edit=True,
    ),
    # VRich
    "USE_MULTIPLE_VRICH": SettingKey(
        key="USE_MULTIPLE_VRICH", dtype=bool, allow_customer_to_edit=False, default="0"
    ),
    "VRICH_HOST": SettingKey(
        key="VRICH_HOST", dtype=str, allow_customer_to_edit=False, secret=True
    ),
    "VRICH_TOKEN": SettingKey(
        key="VRICH_TOKEN", dtype=str, allow_customer_to_edit=False, secret=True
    ),
    "VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE": SettingKey(
        key="VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE",
        dtype=bool,
        allow_customer_to_edit=True,
        default="0",
    ),
    "VRICH_ORDER_NUMBER_APPEND_TRACKING": SettingKey(
        key="VRICH_ORDER_NUMBER_APPEND_TRACKING",
        dtype=bool,
        allow_customer_to_edit=False,
        default="False",
    ),
    # IMind
    "IMIND_HOST": SettingKey(
        key="IMIND_HOST", dtype=str, allow_customer_to_edit=False, secret=True
    ),
    "IMIND_TOKEN": SettingKey(
        key="IMIND_TOKEN", dtype=str, allow_customer_to_edit=False, secret=True
    ),
    # PrintNode
    "PRINTNODE_API_KEY": SettingKey(
        key="PRINTNODE_API_KEY", dtype=str, allow_customer_to_edit=True, secret=False
    ),
    "PRINTNODE_AIRWAY_BILL_PRINTER_ID": SettingKey(
        key="PRINTNODE_PICKORDER_PRINTER_ID", dtype=str, allow_customer_to_edit=True
    ),
    "PRINTNODE_PICKORDER_PRINTER_ID": SettingKey(
        key="PRINTNODE_PICKORDER_PRINTER_ID", dtype=str, allow_customer_to_edit=True
    ),
    "PRINTNODE_PICKORDER_PRINTER_TYPE": SettingKey(
        key="PRINTNODE_PICKORDER_PRINTER_TYPE", dtype=str, allow_customer_to_edit=True
    ),
    "PICKORDER_AUTO_PRINT_ENABLE": SettingKey(
        key="PICKORDER_AUTO_PRINT_ENABLE",
        dtype=bool,
        default="0",
        help="เปิด/ปิดการพิมพ์ใบจัดของอัตโนมัติ",
        allow_customer_to_edit=True,
    ),
    "PICKORDER_STORE_BLOCKED_LIST": SettingKey(
        key="PICKORDER_STORE_BLOCKED_LIST",
        dtype=list,
        default="[]",
        help="รายชื่อร้านที่จะไม่พิมพ์ใบจัดของ",
        allow_customer_to_edit=True,
    ),
    "DOBYBOT_AIRWAYBILL_SHOP_LIST": SettingKey(
        key="DOBYBOT_AIRWAYBILL_SHOP_LIST",
        dtype=list,
        default="[]",
        help="รายชื่อ shop ที่จะใช้ Airway Bill ที่สร้างจากระบบ",
        allow_customer_to_edit=True,
    ),
    "DOBYBOT_AIRWAYBILL_SENDER_NAME": SettingKey(
        key="DOBYBOT_AIRWAYBILL_SENDER_NAME",
        dtype=str,
        default="",
        help="ชื่อผู้ส่งที่ใช้ใน Airway Bill ที่สร้างจากระบบ",
        allow_customer_to_edit=True,
    ),
    "DOBYBOT_AIRWAYBILL_SENDER_PHONE": SettingKey(
        key="DOBYBOT_AIRWAYBILL_SENDER_PHONE",
        dtype=str,
        default="",
        help="เบอร์โทรผู้ส่งที่ใช้ใน Airway Bill ที่สร้างจากระบบ",
        allow_customer_to_edit=True,
    ),
    "DOBYBOT_AIRWAYBILL_SENDER_ADDRESS": SettingKey(
        key="DOBYBOT_AIRWAYBILL_SENDER_ADDRESS",
        dtype=str,
        default="",
        help="ที่อยู่ผู้ส่งที่ใช้ใน Airway Bill ที่สร้างจากระบบ",
        allow_customer_to_edit=True,
    ),
    "DOBYBOT_AIRWAYBILL_FOOTER": SettingKey(
        key="DOBYBOT_AIRWAYBILL_FOOTER",
        dtype=str,
        default="",
        help="HTML ท้ายใบ Airway Bill ที่สร้างจากระบบ",
        allow_customer_to_edit=True,
    ),
    # Line Notify
    "LINE_NOTIFY_TOKEN": SettingKey(
        key="LINE_NOTIFY_TOKEN",
        dtype=str,
        help="Token สำรหรับกลุ่มไลน์แจ้งเตือนเวลา เปิด/ปิด เคส",
        secret=True,
    ),
    # SMS Messages
    "SALES_CHANNEL_SMS_BLOCKED_LIST": SettingKey(
        key="SALES_CHANNEL_SMS_BLOCKED_LIST",
        default="[]",
        dtype=list,
        help="รายชื่อ sales channel ที่จะไม่ส่ง sms",
        allow_customer_to_edit=True,
    ),
    "SMS_SENDER": SettingKey(
        key="SMS_SENDER",
        default="OrderNotice",
        dtype=str,
        help="ชื่อผู้ส่ง SMS, admin only เนื่องจากต้องขอ Whitelist",
    ),
    "SMS_AUTO_RETRY_PENDING": SettingKey(
        key="SMS_AUTO_RETRY_PENDING",
        default="0",
        dtype=bool,
        help="Auto retry ส่ง sms ที่เกิด error ในสถานะ pending",
        allow_customer_to_edit=False,
    ),
    "SMS_READY_TO_SHIP_MESSAGE": SettingKey(
        key="SMS_READY_TO_SHIP_MESSAGE",
        dtype=str,
        help=(
            "ข้อความที่ส่ง SMS หาลูกค้าเมื่อแพคสินค้าเสร็จ\n"
            "{customer} ชื่อ-สกุลลูกค้า\n"
            "{order_number} เลขที่คำสั่งซื้อ\n"
            "{tracking_no} เลขที่ tracking\n"
            "{receipt_link} ลิงค์ใบเสร็จ (ทำการ shorten url ให้อัตโนมัติ)\n"
            "{video_link} ลิงค์วิดีโอ (ทำการ shorten url ให้อัตโนมัติ)\n"
            "{shipping_channel} ช่องทางขนส่ง\n"
        ),
        default="{order_number} แพคเสร็จแล้วค่ะ ดูคลิปการแพค {receipt_link}",
        allow_customer_to_edit=True,
    ),
    "LON_READY_TO_SHIP_REMARK_MESSAGE": SettingKey(
        key="LON_READY_TO_SHIP_REMARK_MESSAGE",
        dtype=str,
        help=("ข้อความที่ส่ง Line Official Notification (LON) หาลูกค้าเมื่อแพคสินค้าเสร็จ\n"),
        default="",
        allow_customer_to_edit=True,
    ),
    "LON_MORE_DETAIL_LINK": SettingKey(
        key="LON_MORE_DETAIL_LINK",
        dtype=str,
        help="ลิงค์เพิ่มเติมใน LON",
        default="https://www.dobybot.com/ordernotice",
        allow_customer_to_edit=True,
    ),
    "SMS_FIXCASE_OPEN_MESSAGE": SettingKey(
        key="SMS_FIXCASE_OPEN_MESSAGE",
        dtype=str,
        help=(
            "ข้อความที่ส่ง SMS หาลูกค้าเมื่อเปิด FixCase\n"
            "{company} ชื่อบริษัท\n"
            "{customer} ชื่อ-สกุลลูกค้า\n"
            "{order_number} เลขที่คำสั่งซื้อ\n"
        ),
        default=(
            "เคสแก้ไขปัญหาของออเดอร์ {order_number} ได้ถูกสร้างแล้ว กำลังดำเนินการแก้ไขให้ลูกค้าค่ะ"
        ),
        allow_customer_to_edit=True,
    ),
    "SMS_FIXCASE_CLOSE_MESSGE": SettingKey(
        key="SMS_FIXCASE_CLOSE_MESSGE",
        dtype=str,
        help=(
            "ข้อความที่ส่ง SMS หาลูกค้าเมื่อปิด FixCase\n"
            "{company} ชื่อบริษัท\n"
            "{customer} ชื่อ-สกุลลูกค้า\n"
            "{order_number} เลขที่คำสั่งซื้อ\n"
            "{fixcase_remark} หมายเลข tracking\n"
        ),
        default=(
            "เคสหมายเลข {order_number} ถูกแก้ไขแล้วค่ะ หมายเหตุจากร้าน : {fixcase_remark}"
        ),
        allow_customer_to_edit=True,
    ),
    "LON_FIXCASE_OPEN_MESSAGE": SettingKey(
        key="LON_FIXCASE_OPEN_MESSAGE",
        dtype=str,
        help=(
            "ข้อความที่ส่ง Line Official Notification (LON) หาลูกค้าเมื่อเปิด FixCase\n"
            "{company} ชื่อบริษัท\n"
            "{customer} ชื่อ-สกุลลูกค้า\n"
            "{order_number} เลขที่คำสั่งซื้อ\n"
        ),
        default=(
            "เคสแก้ไขปัญหาของ ออเดอร์ {order_number} ได้ถูกสร้างแล้ว กำลังดำเนินการแก้ไขให้ลูกค้าค่ะ"
        ),
        allow_customer_to_edit=True,
    ),
    "LON_FIXCASE_CLOSE_MESSAGE": SettingKey(
        key="LON_FIXCASE_CLOSE_MESSAGE",
        dtype=str,
        help=(
            "ข้อความที่ส่ง Line Official Notification (LON) หาลูกค้าเมื่อปิด FixCase\n"
            "{company} ชื่อบริษัท\n"
            "{customer} ชื่อ-สกุลลูกค้า\n"
            "{order_number} เลขที่คำสั่งซื้อ\n"
            "{fixcase_remark} หมายเลข tracking\n"
        ),
        default=("เคสหมายเลข {order_number} ถูกแก้ไขแล้วค่ะ {fixcase_remark}"),
        allow_customer_to_edit=True,
    ),
    # Email Messages
    "EMAIL_SENDER": SettingKey(
        key="SMS_SENDER",
        default="OrderNotice",
        dtype=str,
        help="ชื่อผู้ส่ง email, admin only",
    ),
    # Multi Package SMS
    "SMS_MULTI_PACKAGE_MESSAGE": SettingKey(
        key="SMS_MULTI_PACKAGE_MESSAGE",
        dtype=str,
        help=(
            "ข้อความที่ส่งหาลูกค้าเมื่อ ready to ship ครบทุกกล่องในคำสั่งซื้อ\n",
            "{company} ชื่อบริษัท\n"
            "{customer} ชื่อ-สกุลลูกค้า\n"
            "{order_number} เลขที่คำสั่งซื้อ\n"
            "{tracking_no} เลขที่ tracking\n"
            "{total_packages} จำนวนกล่องทั้งหมด\n",
        ),
        default=("คำสั่งซื้อหมายเลข {order_number} ของท่านมีสินค้าทั้งหมด {total_packages} กล่อง"),
        allow_customer_to_edit=True,
    ),
    "MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS": SettingKey(
        key="MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS",
        dtype=bool,
        default="0",
        help="ส่ง sms แจ้งจำนวนกล่องเมื่อ ทุกกล่องในออเดอร์ถูกแสกน ready to ship",
        allow_customer_to_edit=True,
    ),
    # Pre Record Actions
    "CONFIRM_RECORD_PASSWORD_ENABLE": SettingKey(
        key="CONFIRM_RECORD_PASSWORD_ENABLE",
        dtype=bool,
        default="0",
        help="กรณีบันทึกวิดีโอที่มี FixCase / หมายเหตุ / บันทึกซ้ำ / ถูกยกเลิก ต้องกรอกรหัสผ่าน CONFIRM_RECORD_PASSWORD จึงจะบันทึกวิดีโอได้",
        allow_customer_to_edit=True,
    ),
    "CONFIRM_RECORD_PASSWORD_CONFIG": SettingKey(
        key="CONFIRM_RECORD_PASSWORD_CONFIG",
        dtype=list,
        default=["FIXCASE", "DUPLICATE_RECORD", "REMARK"],
        help=(
            "เลือกว่าให้กรอกรหัสผ่านพนักงานในกรณีใดบ้าง สำหรับ REMARK ถ้าขึ้นต้นด้วย ! จะบังคับให้กรอกรหัสผ่านหัวหน้างาน"
            "choices: ['FIXCASE', 'DUPLICATE_RECORD', 'REMARK', 'ORDER_NOT_FOUND']"
        ),
        allow_customer_to_edit=True,
    ),
    "SUPERVISOR_PASSWORD": SettingKey(
        key="SUPERVISOR_PASSWORD",
        dtype=str,
        default="12345",
        help="รหัสผ่านหัวหน้างาน ใช้ในการแก้ไขตั้งค่าขั้นสูงและอนุมัติการบันทึกวิดีโอที่มี FixCase/หมายเหตุ/ซ้ำ ใช้ร่วมกับ CONFIRM_RECORD_PASSWORD",
        allow_customer_to_edit=True,
    ),
    "STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG": SettingKey(
        key="STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG",
        dtype=bool,
        default="0",
        help=(
            "ใช้กับร่วมกับ RECORD_PIECE_SCAN_MODE_ENABLE, SUPERVISOR_PASSWORD กรณีที่แสกนสินค้าไม่ครบ จะหยุดการบันทึกวิดีโอไม่ได้"
            + "ต้องใช้รหัส supervisor จึงจะหยุดบันทึกวิดีโอได้"
        ),
        allow_customer_to_edit=True,
    ),
    "ADD_ITEM_BARCODE_REQUIRE_SUPERVISOR_PASSWORD": SettingKey(
        key="ADD_ITEM_BARCODE_REQUIRE_SUPERVISOR_PASSWORD",
        dtype=bool,
        default="0",
        help=(
            "ใช้ SUPERVISOR_PASSWORD ในการปรับจำนวนการแพ็คโดยการคลิ๊ก"
            + "ต้องใช้รหัส supervisor จึงจะปรับจำนวนการแพ็คได้"
        ),
        allow_customer_to_edit=True,
    ),
    # Post Record Actions
    "POST_RECORD_ACTION_SHARE_VIDEO_LINK": SettingKey(
        key="POST_RECORD_ACTION_SHARE_VIDEO_LINK",
        dtype=bool,
        default="0",
        help="แชร์ลิงค์วิดีโอ Google Drive หลังอัดวิดีโอเสร็จ",
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_ACTION_CREATE_ORDER": SettingKey(
        key="POST_RECORD_ACTION_CREATE_ORDER",
        dtype=bool,
        default="0",
        help=(
            "สร้างออเดอร์อัตโนมัติหลังอัดวิดีโอเสร็จ ใช้ร่วมกับ POST_RECORD_ACTION_CREATE_ORDER_PATTERN"
            "ใช้สำหรับอัดวิดีโอและส่ง sms อย่างรวดเร็วโดยไม่ต้อง sync ข้อมูลออเดอร์เข้ามาใน Order Center ก่อน"
        ),
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_ACTION_CREATE_ORDER_PATTERN": SettingKey(
        key="POST_RECORD_ACTION_CREATE_ORDER_PATTERN",
        dtype=str,
        default="",
        help=(
            "ต้องเปิด POST_RECORD_ACTION_CREATE_ORDER ก่อนจึงจะใช้ได้"
            "- ใช้ในการ validate ชื่อวิดีโอ"
            "- เป็น Pattern ที่ใช้ดึงข้อมูลสำหรับสร้างออเดอร์จากชื่อวิดีโอ เป็น regex เช่น "
            "  ^(?<order_number>[a-zA-Z0-9-_/.\s]+)\/(?<order_customerphone>0[0-9]{9})$"
            "  ^(?<order_trackingno>[a-zA-Z0-9-_/.\s]+)\/(?<order_customerphone>0[0-9]{9})$"
            "  ^(?<order_customerphone>0[0-9]{9})$"
            "  ^=(?<order_customerphone>0[0-9]{9})$"
        ),
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_ACTION_SHORTEN_URL": SettingKey(
        key="POST_RECORD_ACTION_SHORTEN_URL",
        dtype=bool,
        default="0",
        help="ทำการ shorten video url หลังจากอัดวิดีโอ",
    ),
    "POST_RECORD_ACTION_READY_TO_SHIP": SettingKey(
        key="POST_RECORD_ACTION_READY_TO_SHIP",
        dtype=bool,
        default="0",
        help="ปรับสถานะออเดอร์เป็น Ready To Ship หลังอัดวิดีโอเสร็จ",
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_ACTION_SEND_SMS": SettingKey(
        key="POST_RECORD_ACTION_SEND_SMS",
        dtype=bool,
        default="0",
        help="ส่งลิงค์ใบเสร็จทาง sms หลังปรับสถานะเป็น Ready To Ship",
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_ACTION_SEND_EMAIL": SettingKey(
        key="POST_RECORD_ACTION_SEND_EMAIL",
        dtype=bool,
        default="0",
        help="ส่งลิงค์ใบเสร็จทาง email หลังปรับสถานะเป็น Ready to ship",
    ),
    "POST_RECORD_ACTION_SEND_LON": SettingKey(
        key="POST_RECORD_ACTION_SEND_LON",
        dtype=bool,
        default="0",
        help="ส่งลิงค์ใบเสร็จทาง Line Officail Notification (LON) หลังปรับสถานะเป็น Ready To Ship",
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_ACTION_SEND_WEBHOOK_BEFORE_VIDEO_UPLOAD": SettingKey(
        key="POST_RECORD_ACTION_SEND_WEBHOOK_BEFORE_VIDEO_UPLOAD",
        dtype=bool,
        default="0",
        help="ส่ง post-record-webhook webhook วิดีโออัพโหลดเสร็จ",
        allow_customer_to_edit=False,
    ),
    # Pre record webhook
    "PRE_RECORD_WEBHOOK_ENABLE": SettingKey(
        key="PRE_RECORD_WEBHOOK_ENABLE",
        dtype=bool,
        default="0",
        help="เปิดใช้งาน PRE_RECORD_WEBHOOK",
        allow_customer_to_edit=False,
    ),
    "PRE_RECORD_WEBHOOK_CONFIG": SettingKey(
        key="PRE_RECORD_WEBHOOK_URL",
        dtype=dict,
        default=None,
        help="",
        allow_customer_to_edit=False,
    ),
    "WEBHOOK_AUTH_REQUEST_CONFIG": SettingKey(
        key="WEBHOOK_AUTH_REQUEST_CONFIG",
        dtype=dict,
        default=None,
        help="request config for getting auth token for webhook request",
        allow_customer_to_edit=False,
    ),
    # Post record webhook
    "POST_RECORD_ACTION_WEBHOOK_ENABLE": SettingKey(
        key="POST_RECORD_ACTION_WEBHOOK_ENABLE",
        dtype=bool,
        default="0",
        help="เปิดใช้งาน webhook",
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_WEBHOOK_CONFIG": SettingKey(
        key="POST_RECORD_WEBHOOK_CONFIG",
        dtype=dict,
        default=None,
        help="",
        allow_customer_to_edit=True,
    ),
    "POST_RECORD_WEBHOOK_URL": SettingKey(
        key="POST_RECORD_WEBHOOK_URL",
        dtype=str,
        default=None,
        allow_customer_to_edit=False,
    ),
    "POST_RECORD_WEBHOOK_AUTH": SettingKey(
        key="POST_RECORD_WEBHOOK_AUTH",
        dtype=str,
        default=None,
        allow_customer_to_edit=False,
    ),
    # Ready To Ship
    "READY_TO_SHIP_REQUIRE_VIDEO": SettingKey(
        key="READY_TO_SHIP_REQUIRE_VIDEO",
        dtype=bool,
        default="0",
        help="ต้องมีการอัดวิดีโอก่อนจึงจะสามารถปรับสถานเป็น ready to ship ได้",
        allow_customer_to_edit=False,
    ),
    "READY_TO_SHIP_REQUIRE_NO_FIXCASES": SettingKey(
        key="READY_TO_SHIP_REQUIRE_NO_FIXCASES",
        dtype=bool,
        default="0",
        help="ต้องไม่มี FixCase เปิดอยู่จึงจะสามารถปรับสถานะเป็น ready to ship ได้",
        allow_customer_to_edit=False,
    ),
    "READY_TO_SHIP_ORDER_NUMBER_REGEX": SettingKey(
        key="READY_TO_SHIP_ORDER_NUMBER_REGEX",
        dtype=str,
        default="",
        help="regular expression สำหรับแยก order number จาก barcode ที่แสกน ready to ship",
    ),
    # Ready to ship webhook
    "READY_TO_SHIP_WEBHOOK_ENABLE": SettingKey(
        key="POST_RECORD_ACTION_WEBHOOK_ENABLE",
        dtype=bool,
        default="0",
        help="เปิดใช้งาน webhook ready to ship",
        allow_customer_to_edit=False,
    ),
    "READY_TO_SHIP_WEBHOOK_URL": SettingKey(
        key="READY_TO_SHIP_WEBHOOK_URL",
        dtype=str,
        default=None,
        allow_customer_to_edit=False,
    ),
    "READY_TO_SHIP_WEBHOOK_AUTH": SettingKey(
        key="READY_TO_SHIP_WEBHOOK_AUTH",
        dtype=str,
        default=None,
        allow_customer_to_edit=False,
    ),
    # Fix Case
    "FIXCASE_SEND_OPEN_MESSAGE": SettingKey(
        key="FIXCASE_SEND_OPEN_MESSAGE",
        dtype=bool,
        default="1",
        help="ส่ง sms แจ้งเตือนเมื่อทำการเปิด Fix Case",
        allow_customer_to_edit=True,
    ),
    "FIXCASE_SEND_CLOSE_MESSAGE": SettingKey(
        key="FIXCASE_SEND_CLOSE_MESSAGE",
        dtype=bool,
        default="1",
        help="ส่ง sms แจ้งเตือนเมื่อทำการเปิด Fix Case",
        allow_customer_to_edit=True,
    ),
    "FIXCASE_LON_SEND_OPEN_MESSAGE": SettingKey(
        key="FIXCASE_LON_SEND_OPEN_MESSAGE",
        dtype=bool,
        default="0",
        help="ส่ง Line Official Notification (LON) แจ้งเตือนเมื่อทำการเปิด Fix Case",
        allow_customer_to_edit=True,
    ),
    "FIXCASE_LON_SEND_CLOSE_MESSAGE": SettingKey(
        key="FIXCASE_LON_SEND_CLOSE_MESSAGE",
        dtype=bool,
        default="0",
        help="ส่ง Line Official Notification (LON) แจ้งเตือนเมื่อทำการปิด Fix Case",
        allow_customer_to_edit=True,
    ),
    "POST_CLOSE_FIXCASE_ACTION_ID": SettingKey(
        key="POST_CLOSE_FIXCASE_ACTION_ID",
        dtype=str,
        default=None,
        help=(
            "id ของ action หลังการปิด FixCase\n"
            "- IMindService.close_case = ส่ง api ปิด ticket ของ iMind"
        ),
        allow_customer_to_edit=False,
    ),
    # Receipt
    "COMPANY_NAME": SettingKey(
        key="COMPANY_NAME",
        dtype=str,
        help="ชื่อบริษัท",
        allow_customer_to_edit=True,
    ),
    "COMPANY_ADDRESS": SettingKey(
        key="ADDRESS_LINE",
        dtype=str,
        help="ที่อยู่บริษัท",
        allow_customer_to_edit=True,
    ),
    "COMPANY_ADMIN_TEL": SettingKey(
        key="COMPANY_ADMIN_TEL",
        dtype=str,
        default="",
        help="เบอร์โทร admin แสดงในหน้าใบเสร็จ สำหรับให้ลูกค้าแจ้งเวลาในวิดีโอจัดของไม่ถูกต้อง",
        allow_customer_to_edit=True,
    ),
    "COMPANY_ADMIN_LINE_NAME": SettingKey(
        key="COMPANY_ADMIN_LINE_NAME",
        dtype=str,
        default="",
        help="ชื่อ Line admin แสดงในหน้าใบเสร็จ สำหรับให้ลูกค้าแจ้งเวลาในวิดีโอจัดของไม่ถูกต้อง",
        allow_customer_to_edit=True,
    ),
    "COMPANY_ADMIN_LINE_URL": SettingKey(
        key="COMPANY_ADMIN_LINE_URL",
        dtype=str,
        default="",
        help="URL Line admin แสดงในหน้าใบเสร็จ สำหรับให้ลูกค้าแจ้งเวลาในวิดีโอจัดของไม่ถูกต้อง",
        allow_customer_to_edit=True,
    ),
    "HIDE_RECEIPT": SettingKey(
        key="HIDE_RECEIPT",
        dtype=bool,
        default="0",
        help="ซ่อนใบเสร็จ",
        allow_customer_to_edit=True,
    ),
    "HIDE_SCAN_LOGS": SettingKey(
        key="HIDE_SCAN_LOGS",
        dtype=bool,
        default="0",
        help="ซ่อนประวัติการแสกน",
        allow_customer_to_edit=True,
    ),
    "RECEIPT_FOOTER": SettingKey(
        key="RECEIPT_FOOTER",
        dtype=str,
        default="",
        help="ส่วนท้ายใบเสร็จ สามารถเขียนเป็น html ได้",
        allow_customer_to_edit=True,
    ),
    "GOOGLE_DRIVE_SHARE_DRIVE_ID": SettingKey(
        key="GOOGLE_DRIVE_SHARE_DRIVE_ID",
        dtype=str,
        default="",
        group="GOOGLE_DRIVE",
        help="",
    ),
    "GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE": SettingKey(
        key="GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE",
        dtype=bool,
        default="0",
        group="GOOGLE_DRIVE",
        help=(
            "*ต้องเปิดใช้งาน GOOGLE_DRIVE_SHARE_DRIVE_ID ก่อน\n"
            "เปิดใช้งาน ลบไฟล์ใน share drive อัตโนมัติ"
        ),
    ),
    "GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION": SettingKey(
        key="GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION",
        dtype=int,
        default=0,
        group="GOOGLE_DRIVE",
        help=(
            "*ต้องเปิดใช้งาน GOOGLE_DRIVE_SHARE_DRIVE_ID ก่อน\n"
            "ระยะเวลาเก็บไฟล์ใน share drive (หน่วยเป็น วัน) ไฟล์ที่มีอายุเกินที่ตั้งค่าไว้จะถูกลบอัตโนมัติ"
        ),
    ),
    # WARNING
    "LOW_RECORD_CREDIT_WARNING_AMOUNT": SettingKey(
        key="WARNING_LOW_CREDIT_TRESHOLD",
        dtype=int,
        default=0,
        group="WARNING",
        help="จำนวนขั้นต่ำในการแจ้งเตือน (record credit) กรอก 0 ถ้าไม่ต้องการให้แจ้งเตือน",
        allow_customer_to_edit=True,
    ),
    "LOW_SMS_CREDIT_WARNING_AMOUNT": SettingKey(
        key="WARNING_LOW_CREDIT_TRESHOLD",
        dtype=int,
        default=0,
        group="WARNING",
        help="จำนวนขั้นต่ำในการแจ้งเตือน (sms credit), กรอก 0 ถ้าไม่ต้องการให้แจ้งเตือน",
        allow_customer_to_edit=True,
    ),
    # MQTT
    "USE_MQTT": SettingKey(
        key="USE_MQTT",
        dtype=bool,
        default="0",
        group="MQTT",
        help="เปิดใช้งาน mqtt สำหรับ API start/stop recording",
        allow_customer_to_edit=False,
    ),
    # Shopee Chat API
    "SHOPEE_CHAT_API": SettingKey(
        key="SHOPEE_CHAT_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่าเปิด-ปิดการใช้งาน Chat API ของแต่ละร้านค้า",
        allow_customer_to_edit=True,
    ),
    "SHOPEE_READY_TO_SHIP_MESSAGE": SettingKey(
        key="SHOPEE_READY_TO_SHIP_MESSAGE",
        dtype=str,
        default="",
        help="ออเดอร์ {order_number} ของคุณได้ทำการแพคเสร็จแล้ว\nสามารถตรวจสอบคลิปการแพคได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ",
        allow_customer_to_edit=True,
    ),
    # Lazada Chat API
    "LAZADA_CHAT_API": SettingKey(
        key="LAZADA_CHAT_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่าเปิด-ปิดการใช้งาน Chat API ของแต่ละร้านค้า",
        allow_customer_to_edit=True,
    ),
    "LAZADA_READY_TO_SHIP_MESSAGE": SettingKey(
        key="LAZADA_READY_TO_SHIP_MESSAGE",
        dtype=str,
        default="",
        help="ออเดอร์ {order_number} ของคุณได้ทำการแพคเสร็จแล้ว\nสามารถตรวจสอบคลิปการแพคได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ",
        allow_customer_to_edit=True,
    ),
    # Shopee API
    "SHOPEE_API": SettingKey(
        key="SHOPEE_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่าร้านค้า Shopee API",
        allow_customer_to_edit=True,
    ),
    # Lazada API
    "LAZADA_API": SettingKey(
        key="LAZADA_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่าร้านค้า Lazada API",
        allow_customer_to_edit=True,
    ),
    # TikTok Shop API
    "TIKTOK_SHOP_API": SettingKey(
        key="TIKTOK_SHOP_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่าร้านค้า TikTok Shop API",
        allow_customer_to_edit=True,
    ),
    # TikTok Shop API
    "TIKTOK_SHOP_V2_API": SettingKey(
        key="TIKTOK_SHOP_V2_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่าร้านค้า TikTok Shop V2 API",
        allow_customer_to_edit=True,
    ),
    # Shipnity
    "SHIPNITY_API": SettingKey(
        key="SHIPNITY_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่า Shipnity API",
        allow_customer_to_edit=True,
    ),
    # Nex Gen Commerce
    "NEX_GEN_COMMERCE_API": SettingKey(
        key="NEX_GEN_COMMERCE_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่า Nex Gen Commerce API",
        allow_customer_to_edit=True,
    ),
    "WEIGHT_SCALE_PREFER_UNIT": SettingKey(
        key="WEIGHT_SCALE_PREFER_UNIT",
        dtype=str,
        default="auto",
        help="ตั้งค่าหน่วยวัดนำ้หนัก, (g, kg, auto)",
        allow_customer_to_edit=True,
    ),
    "WEIGHT_SLIP_PRINT_OPTIONS": SettingKey(
        key="WEIGHT_SLIP_PRINT_CONFIG",
        dtype=dict,
        default=json.dumps(DEFAULTS["WEIGHT_SLIP_PRINT_CONFIG"]),
        help="ตั้งค่า printer (สำหรับ printnode)",
        allow_customer_to_edit=False,
    ),
    "WEIGHT_ONLY_MODE_ENABLE": SettingKey(
        key="WEIGHT_ONLY_MODE_ENABLE",
        dtype=bool,
        default=False,
        help="โหมดบันทึกน้ำหนักเท่านั้น, แสกนครั้งเดียว ไม่บันทึกวิดีโอ",
    ),
    "WEIGHT_ONLY_MODE_PRINT_METHOD": SettingKey(
        key="WEIGHT_ONLY_MODE_PRINT_METHOD",
        dtype=str,
        default="webprint",
        help="วิธีการพิมพ์ใบเสร็จ, (printnode, webprint)",
    ),
    # CHAT API Limits
    "SHOPEE_CHAT_DAILY_LIMIT": SettingKey(
        key="SHOPEE_CHAT_DAILY_LIMIT",
        dtype=int,
        default=1000,
        help=(
            "Limit จำนวนส่ง chat api ของ shopee แต่ละร้านค้าจะนับจำนวนแยกกัน, "
            "ถ้าให้เป็น 1000 และมี 3 ร้านค้าจะส่งได้สูงสุด 3000 ข้อความต่อวัน"
        ),
        allow_customer_to_edit=False,
    ),
    "LAZADA_CHAT_DAILY_LIMIT": SettingKey(
        key="LAZADA_CHAT_DAILY_LIMIT",
        dtype=int,
        default=1000,
        help=(
            "Limit จำนวนส่ง chat api ของ lazada แต่ละร้านค้าจะนับจำนวนแยกกัน, "
            "ถ้าให้เป็น 1000 และมี 3 ร้านค้าจะส่งได้สูงสุด 3000 ข้อความต่อวัน"
        ),
        allow_customer_to_edit=False,
    ),
    "HOMEPAGE_URL": SettingKey(
        key="HOMEPAGE_URL",
        dtype=str,
        default="auto",
        help="ตั้งค่าหน้าแรกสำหรับลูกค้าที่ไม่ใช้ record",
        allow_customer_to_edit=False,
    ),
    "ENABLE_NO_RECORD_MODE": SettingKey(
        key="ENABLE_NO_RECORD_MODE",
        dtype=bool,
        default=False,
        help="ปิดการใช้งานการอัดวิดีโอ ใช้แต่ scan เช็คของ",
        allow_customer_to_edit=False,
    ),
    "DISABLED_PRE_RECORD_CHECK": SettingKey(
        key="DISABLED_PRE_RECORD_CHECK",
        dtype=bool,
        default=False,
        help="ปิดการตรวจสอบก่อนอัดวิดีโอ",
        allow_customer_to_edit=False,
    ),
    "ALLOW_NEGATIVE_CREDIT": SettingKey(
        key="ALLOW_NEGATIVE_CREDIT",
        dtype=bool,
        default=False,
        help="อนุญาตให้เครดิตลบได้",
        allow_customer_to_edit=False,
    ),
    # "ALLOW_THAI_CHARACTER_IN_BARCODE": SettingKey(
    #     key="ALLOW_THAI_CHARACTER_IN_BARCODE",
    #     dtype=bool,
    #     default=False,
    #     help="อนุญาตให้ใช้ตัวอักษรไทยใน barcode",
    #     allow_customer_to_edit=False
    # )
    "ZORT_PRE_ORDER_TRANSFORM": SettingKey(
        key="ZORT_PRE_ORDER_TRANSFORM",
        dtype=dict,
        default=None,
        help=(
            "ตั้งค่าการแปลงข้อมูล Pre-Order สำหรับ zort, ดู ZortPreOrderTransformAPIView\n",
            "Dict Keys:\n"
            "- zort_storename: string\n"
            "- zort_apikey: string\n"
            "- zort_apisecret: string\n"
            "- sku_suffix: string, default = 'pre'\n",
        ),
    ),
    "API_SMS_REFUND_UNDELIVERED": SettingKey(
        key="API_SMS_REFUND_UNDELIVERED",
        dtype=bool,
        default=True,
        help="ส่ง sms โดยไม่สนใจว่าจะถึงหรือไม่ถึงลูกค้า",
    ),
    "SMS_WEBHOOK_ENABLE": SettingKey(
        key="SMS_WEBHOOK_ENABLE",
        dtype=bool,
        default=False,
        help="เปิดใช้งาน SMS Webhook",
        allow_customer_to_edit=True,
    ),
    "SMS_WEBHOOK_CONFIG": SettingKey(
        key="SMS_WEBHOOK_CONFIG",
        dtype=dict,
        default=None,
        help=(
            "ตั้งค่า SMS Webhook\n"
            "Dict Keys:\n"
            "- url: string\n"
            "- method: string, 'GET'|'POST'\n"
            "- headers: dict\n"
            "- params: dict\n"
        ),
        allow_customer_to_edit=True,
    ),
    "RECORD_FAILED_TRANSCODE_SHOW_ERROR": SettingKey(
        key="RECORD_FAILED_TRANSCODE_SHOW_ERROR",
        dtype=bool,
        default=False,
        help="แสดง error ในหน้าบันทึกวิดีโอ กรณีที่แปลงไฟล์ไม่สำเร็จ",
        allow_customer_to_edit=False,
    ),
    "PICK_ITEM_RENAME_BY_PRODUCT": SettingKey(
        key="PICK_ITEM_RENAME_BY_PRODUCT",
        dtype=bool,
        default=False,
        help="เปลี่ยนชื่อสินค้าในหน้า pick item ให้เป็นชื่อสินค้าที่ตั้งค่าในระบบ Product (กรณี Sku ตรงกัน)",
        allow_customer_to_edit=False,
    ),
    "PICKORDER_PRINT_START_DATE": SettingKey(
        key="PICKORDER_PRINT_START_DATE",
        dtype=str,
        default=None,
        help="วันที่เริ่มต้นในการพิมพ์ใบรับของ",
        allow_customer_to_edit=True,
    ),
    # Still Image Capture
    "CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE": SettingKey(
        key="CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE",
        dtype=bool,
        default="0",
        help="กรณีบันทึกรูปที่มีรายการซ้ำ ต้องกรอกรหัสผ่าน SUPERVISOR จึงจะถ่ายรูปได้",
        allow_customer_to_edit=True,
    ),
    "PRINT_RECEIPT_TAX_INVOICE_2_COPY": SettingKey(
        key="PRINT_RECEIPT_TAX_INVOICE_2_COPY",
        dtype=bool,
        default="0",
        help="",
        allow_customer_to_edit=True,
    ),
    "POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK": SettingKey(
        key="POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK",
        dtype=bool,
        default="0",
        help="แชร์ลิงค์ภาพถ่าย Google Drive หลังอัปโหลดรูปภาพเสร็จ",
        allow_customer_to_edit=True,
    ),
    # DobySync (dobybot-connect)
    "DOBYBOT_CONNECT_VERSION": SettingKey(
        key="DOBYBOT_CONNECT_VERSION",
        dtype=int,
        default=1,
        help="เวอร์ชั่นของ dobybot connect (1=dobybot-connect, 2=dobysync)",
        allow_customer_to_edit=True,
    ),
    "DOBYSYNC_CLIENT": SettingKey(
        key="DOBYSYNC_CLIENT_CONFIG",
        dtype=dict,
        default=None,
        help="",
        allow_customer_to_edit=False,
        secret=True,
    ),
    "NOCNOC_API": SettingKey(
        key="NOCNOC_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่า NocNoc API",
        allow_customer_to_edit=True,
    ),
    "LINE_MY_SHOP_API": SettingKey(
        key="LINE_MY_SHOP_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่า LINE MY SHOP API",
        allow_customer_to_edit=True,
    ),
    # Shipping: เชื่อมต่อ API ขนส่ง
    "AUTO_GENERATE_TRACKINGNO_ENABLE": SettingKey(
        key="AUTO_GENERATE_TRACKINGNO_ENABLE",
        dtype=bool,
        default="0",
        help="เมื่อมีออเดอร์ระบบจะทำการขอเลข tracking number อัตโนมัติ",
        allow_customer_to_edit=True,
    ),
    "AUTO_GENERATE_TRACKINGNO_PROVIDER": SettingKey(
        key="AUTO_GENERATE_TRACKINGNO_PROVIDER",
        dtype=str,
        default=None,
        help="เมื่อมีออเดอร์ระบบจะทำการสร้างใบปะหน้าของขนส่งที่เลือก",
        allow_customer_to_edit=True,
    ),
    "AUTO_GENERATE_TRACKINGNO_SHOPS": SettingKey(
        key="AUTO_GENERATE_TRACKINGNO_SHOPS",
        dtype=list,
        default="[]",
        help="รายชื่อ shop ที่จะสร้าง tracking number อัตโนมัติ",
        allow_customer_to_edit=True,
    ),
    "SHIPPING_SENDER": SettingKey(
        key="SHIPPING_SENDER",
        dtype=dict,
        default=dict,
        help=(
            "key = [id, name, phone, address, province, province_en, district, district_en, subdistrict, subdistrict_en, postcode]"
            "สำหรับใช้กับ setting shipping"
        ),
        allow_customer_to_edit=True,
    ),
    "FLASH_API_CONFIG": SettingKey(
        key="FLASH_API_CONFIG",
        dtype=dict,
        default=None,
        help=(
            "ตั้งค่า Flash API\n"
            "Dict Keys:\n"
            "- sub_merchant_id: int\n"
            "- base_url: str\n"
            "- token: str\n"
        ),
        allow_customer_to_edit=True,
    ),
    "WATERMARK_LOGO": SettingKey(
        key="WATERMARK_LOGO",
        dtype=str,
        default=None,
        help="URL ของ watermark logo, png, 200x200px, สำหรับทำ watermark video ใน desktop app",
        allow_customer_to_edit=True,
    ),
    "ENABLE_WATERMARK": SettingKey(
        key="ENABLE_WATERMARK",
        dtype=bool,
        default=False,
        help="เปิดใช้งาน watermark ใน desktop app",
        allow_customer_to_edit=True,
    ),
    "RECORD_TAGS": SettingKey(
        key="RECORD_TAGS",
        dtype=dict,
        default=list,
        help=(
            "ตั้งค่า tag สำหรับบันทึกวิดิโอ\n"
            "Examples:\n"
            """
            RECORD_TAGS: [
                { label: 'box', barcodes: ['B1', 'B2', 'B3'] },
                { label: 'bag', barcodes: ['A1', 'A2', 'A3'] },
                { label: 'bottle', barcodes: ['C1', 'C2', 'C3'] }
            ]
            """
        ),
        allow_customer_to_edit=True,
    ),
    "LOG_ROCKET_ENABLE": SettingKey(
        key="LOG_ROCKET_ENABLE",
        dtype=bool,
        default=False,
        help="เปิดใช้งาน LogRocket",
        allow_customer_to_edit=False,
    ),
    "WINDOW_NETWORK_FOLDER_PATH": SettingKey(
        key="WINDOW_NETWORK_FOLDER_PATH",
        dtype=str,
        default="",
        help="""
        Path สำหรับบริษัทที่ต้องการเก็บไฟล์วิดีโอไว้ในเซิฟเวอร์ของบริษัท เช่น
        - X:\\Videos\\"
        - \\\\server1\\video
        """,
        allow_customer_to_edit=False,
    ),
    "ENABLE_P_SCORE": SettingKey(
        key="ENABLE_P_SCORE",
        dtype=bool,
        default=False,
        help="เปิดใช้งาน Feature P-Score (Packing Score) แสดง P-Score ในใบจัดสินค้า",
        allow_customer_to_edit=True,
    ),
    "RECORD_SCAN_INPUT_DISABLE": SettingKey(
        key="RECORD_SCAN_INPUT_DISABLE",
        dtype=bool,
        default=False,
        help="ปิดใช้งานช่องสแกนบันทึกวิดีโอ",
        allow_customer_to_edit=False,
    ),
    "ETAX_SELLER": SettingKey(
        key="ETAX_SELLER",
        dtype=dict,
        default=None,
        help="ตั้งค่า ETax Seller สำหรับ Data One Asia",
        allow_customer_to_edit=True,
    ),
    "ETAX_DOCUMENT_FOLDER_ID": SettingKey(
        key="ETAX_DOCUMENT_FOLDER_ID",
        dtype=str,
        default=None,
        help="ตั้งค่า ShareDrive Folder ID สำหรับ Tax Invoice",
        allow_customer_to_edit=False,
    ),
    "ETAX_DOCUMENT_ID_MODE": SettingKey(
        key="ETAX_DOCUMENT_ID_MODE",
        dtype=str,
        help="running_number, order_number",
        default="order_number",
        allow_customer_to_edit=False,
    ),
    "ETAX_ENABLE_PICKSLIP_QR_CODE": SettingKey(
        key="ETAX_ENABLE_PICKSLIP_QR_CODE",
        dtype=bool,
        help="enable for print etax form with qrcode",
        default=False,
        allow_customer_to_edit=True,
    ),
    "ETAX_ALLOW_ON_ORDER_RECEIVED": SettingKey(
        key="ETAX_ALLOW_ON_ORDER_RECEIVED",
        dtype=bool,
        help="ออก E-Tax Invoice ได้เมื่อสถานะออเดอร์เป็น รับของ",
        default=False,
        allow_customer_to_edit=True,
    ),
    "ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED": SettingKey(
        key="ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED",
        dtype=bool,
        help="ลูกค้า ยกเลิก / คืนของ ยกเลิก etax อัตโนมัติ",
        default=False,
        allow_customer_to_edit=True,
    ),
    "ETAX_CREATE_ACTION_CREDIT_AMOUNT": SettingKey(
        key="ETAX_CREATE_CREDIT_AMOUNT",
        dtype=float,
        default=3,
        help="จำนวนเครดิตเมื่อ สร้าง เอกสาร ETax",
        allow_customer_to_edit=False,
    ),
    "ETAX_UPDATE_ACTION_CREDIT_AMOUNT": SettingKey(
        key="ETAX_UPDATE_ACTION_CREDIT_AMOUNT",
        dtype=float,
        default=3,
        allow_customer_to_edit=False,
        help="จำนวนเครดิตเมื่อ แก้ไข เอกสาร ETax",
    ),
    "ETAX_CANCEL_ACTION_CREDIT_AMOUNT": SettingKey(
        key="ETAX_CANCEL_ACTION_CREDIT_AMOUNT",
        dtype=float,
        default=3,
        allow_customer_to_edit=False,
        help="จำนวนเครดิตเมื่อ ยกเลิก เอกสาร ETax",
    ),
    "ETAX_CREDIT_NOTE_ACTION_CREDIT_AMOUNT": SettingKey(
        key="ETAX_CREDIT_NOTE_ACTION_CREDIT_AMOUNT",
        dtype=float,
        default=1,
        allow_customer_to_edit=False,
        help="จำนวนเครดิตเมื่อสร้างเอกสาร เครดิตโน้ต",
    ),
    # "ETAX_CUSTOM_MESSAGE_CHAT": SettingKey(
    #     key="ETAX_CUSTOM_MESSAGE_CHAT",
    #     dtype=str,
    #     default="",
    #     allow_customer_to_edit=True,
    # ),
    # "ETAX_ALLOW_SEND_TO_PLATFORM_CHAT": SettingKey(
    #     key="ETAX_ALLOW_SEND_TO_PLATFORM_CHAT",
    #     dtype=bool,
    #     default=False,
    #     help="อนุญาติให้ส่งลิงค์ใบกำกับภาษี ETax ไปในแชทของแพลตฟอร์ม",
    #     allow_customer_to_edit=True,
    # ),
    # "ETAX_SEND_LINK_TRIGGER": SettingKey(
    #     key="ETAX_SEND_LINK_TRIGGER",
    #     dtype=str,
    #     default="ready_to_ship",
    #     help="ส่งฟอร์ม ETax ตามสถานะของออเดอร์ ready_to_ship,order_received",
    #     allow_customer_to_edit=True,
    # ),
    "ETAX_DOC_CREATE_DATE": SettingKey(
        key="ETAX_DOC_CREATE_DATE",
        dtype=str,
        default="create_doc_date",
        help=(
            "เลือกว่าจะใช้ order_date, ready_to_ship_date, create_doc_date "
            "เป็นวันที่สร้างเอกสาร ETax Invoice"
        ),
        allow_customer_to_edit=True,
    ),
    "ETAX_HEADER_MESSAGE_BILL": SettingKey(
        key="ETAX_HEADER_MESSAGE_BILL",
        dtype=str,
        default="สแกนเพื่อขอใบกำกับภาษีอิเล็กทรอนิกส์",
        help="ข้อความหัวบิลสำหหรับสแกนเพื่อขอใบกำกับภาษีอิเล็กทรอนิกส์",
        allow_customer_to_edit=True,
    ),
    "ETAX_FOOTER_MESSAGE_BILL": SettingKey(
        key="ETAX_FOOTER_MESSAGE_BILL",
        dtype=str,
        default="",
        help="ข้อความท้ายบิลสำหรับสแกนเพื่อขอใบกำกับภาษีอิเล็กทรอนิกส์",
        allow_customer_to_edit=True,
    ),
    "ETAX_SMS_MESSAGE": SettingKey(
        key="ETAX_SMS_MESSAGE",
        dtype=str,
        default="คลิกลิงค์นี้เพื่อขอใบกำกับภาษี {sms_etax_link} ",
        help="ข้อความและลิงค์ส่งทาง SMS ",
        allow_customer_to_edit=True,
    ),
    "BKP_PREDICT_ORDER_LEVEL": SettingKey(
        key="BKP_PREDICT_ORDER_LEVEL",
        dtype=bool,
        default="0",
        help="เมื่อมีออเดอร์ระบบจะทำการทำนายระดับความยากของออเดอร์",
        allow_customer_to_edit=False,
    ),
    "ETAX_HIDE_SKU": SettingKey(
        key="ETAX_HIDE_SKU",
        dtype=bool,
        default="0",
        help="ซ่อน sku ที่แสดงที่หน้า etax และขึ้นลำดับตามรายการสินค้าแทนเช่น 1,2,3,4,...",
        allow_customer_to_edit=True,
    ),
    "ETAX_ORDER_OPEN_DAYS": SettingKey(
        key="ETAX_ORDER_OPEN_DAYS",
        dtype=int,
        default=10,
        help="กำหนดจำนวนวันที่เปิดใบกำกับภาษีได้หลังจากวันที่สั่งซื้อ (เช่น ตั้งค่า 7 จะเปิดได้ถึง 7 วันหลังวันที่สั่งซื้อ)",
        allow_customer_to_edit=False,
    ),
    "EASY_ETAX_TOKEN": SettingKey(
        key="EASY_ETAX_TOKEN",
        dtype=str,
        default=None,
        help="Secret key สำหรับการยืนยันตัวตน (encoded jwt) สำหรับใช้งาน Easy ETax From Token",
        allow_customer_to_edit=False,
        secret=True,
    ),
    "ETAX_BRANCHES": SettingKey(
        key="ETAX_BRANCHES",
        dtype=list,
        default=list,
        help="สาขาทีeใช้งาน ETax, example {'number': '00000', 'name': 'สำนักงานใหญ่', 'address': 'ที่อยู่ xxxx'}",
        allow_customer_to_edit=True,
    ),
    "ETAX_SMS_CREDIT_AMOUNT": SettingKey(
        key="ETAX_SMS_CREDIT_AMOUNT",
        dtype=float,
        default=1,
        help="จำนวน credit ที่ตัด สำหรับการส่ง sms ต่อ 1รอบ",
        allow_customer_to_edit=False,
    ),
    "RECEIPT_SHOW_ETAX_BUTTON": SettingKey(
        key="RECEIPT_SHOW_ETAX_BUTTON",
        dtype=bool,
        default=True,
        help="ซ่อนปุ่มขอ E-Tax ในใบเสร็จ",
        allow_customer_to_edit=True,
    ),
    "ETAX_SHOW_CONSENT_CHECKBOX": SettingKey(
        key="ETAX_SHOW_CONSENT_CHECKBOX",
        dtype=bool,
        default=True,
        help="แสดง checkbox ยอมรับข้อตกลงยินยอมให้ใช้ข้อมูลทางการตลาด",
        allow_customer_to_edit=True,
    ),
    "ETAX_SHOW_DOBYBOT_CONTACT": SettingKey(
        key="ETAX_SHOW_DOBYBOT_CONTACT",
        dtype=bool,
        default=True,
        help="แสดงข้อมูลติดต่อ dobybot ในหน้า ETax",
        allow_customer_to_edit=True,
    ),
    "ETAX_RETRIEVAL_DAY": SettingKey(
        key="ETAX_RETRIEVAL_DAY",
        dtype=int,
        default=15,
        help="วันที่ในแต่ละเดือนสำหรับการดึงใบกำกับภาษี (เช่น 1 หมายถึงวันที่ 1 ของเดือน)",
        allow_customer_to_edit=False,
    ),
    "ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST": SettingKey(
        key="ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST",
        dtype=bool,
        default=False,
        help="สร้างเอกสาร ETax อัตโนมัติเมื่อลูกค้าขอใบกำกับภาษีจาก platform",
        allow_customer_to_edit=True,
    ),
    "ETAX_AUTO_START_DATE": SettingKey(
        key="ETAX_AUTO_START_DATE",
        dtype=str,
        default=None,
        help="วันที่เริ่มต้นสร้างเอกสาร ETax อัตโนมัติ หากปล่อยเป็นค่าว่าง จะใช้วันที่ 1 ของเดือนปัจจุบัน",
        allow_customer_to_edit=True,
    ),
    "ETAX_WELCOME_MESSAGE": SettingKey(
        key="ETAX_WELCOME_MESSAGE",
        dtype=str,
        default="",
        help="ข้อความต้อนรับในหน้า ETax สำหรับลูกค้า",
        allow_customer_to_edit=True,
    ),
    "ETAX_AUTO_CANCEL_MODE": SettingKey(
        key="ETAX_AUTO_CANCEL_MODE",
        dtype=str,
        default="cancel",  # cancel, credit_note
        help=(
            "ยกเลิก/สร้างใบลดหนี้ อัตโนมัติ ก่อนที่เอกสารส่งกรม เมื่อสถานะออเดอร์เป็น ยกเลิก"
            "cancel - ยกเลิก"
            "credit_note - สร้างใบลดหนี้"
        ),
        allow_customer_to_edit=True,
    ),
    "ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF": SettingKey(
        key="ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF",
        dtype=str,
        default="cancel",  # do_nothing credit_note
        help=(
            "ยกเลิก/สร้างใบลดหนี้ อัตโนมัติก่อนที่เอกสารส่งกรม เมื่อสถานะออเดอร์เป็น ยกเลิก"
            "do_nothing - ไม่ทำอะไร"
            "credit_note - สร้างใบลดหนี้"
        ),
        allow_customer_to_edit=True,
    ),
    "ETAX_EMAIL_REPLACEMENT": SettingKey(
        key="ETAX_EMAIL_REPLACEMENT",
        dtype=str,
        default="",
        help="เปลี่ยน email ใน ETax ให้เป็น email ที่ต้องการ",
        allow_customer_to_edit=True,
    ),
    "ETAX_LOGO": SettingKey(
        key="ETAX_LOGO",
        dtype=str,
        default="",
        help="URL ของ logo สำหรับใช้ใน ETax",
        allow_customer_to_edit=False,
    ),
    "ETAX_AUTO_ZORT_TAG": SettingKey(
        key="ETAX_AUTO_ZORT_TAG",
        dtype=bool,
        default=False,
        help="เพิ่ม tag ใน order zort อัตโนมัติ เมื่อสร้าง ETax หรือยกเลิก หรือสร้าง CN",
        allow_customer_to_edit=True,
    ),
    "ETAX_WEBHOOK_ENABLE": SettingKey(
        key="ETAX_WEBHOOK_ENABLE",
        dtype=bool,
        default=False,
        help="เปิดใช้งาน ETax Webhook",
        allow_customer_to_edit=True,
    ),
    "ETAX_WEBHOOK_CONFIG": SettingKey(
        key="ETAX_WEBHOOK_CONFIG",
        dtype=dict,
        default=None,
        help=(
            "ตั้งค่า ETax Webhook\n"
            "Dict Keys:\n"
            "- url: string\n"
            "- method: string, 'GET'|'POST'\n"
            "- headers: dict\n"
            "- params: dict\n"
        ),
        allow_customer_to_edit=True,
    ),
    "ETAX_AUTO_ACTION_WHEN_ORDER_CHANGE": SettingKey(
        key="ETAX_AUTO_ACTION_WHEN_ORDER_CHANGE",
        dtype=str,
        default="do_nothing",
        help=(
            "กิจกรรมที่ทำหลังจากออเดอร์มีการเปลี่ยนแปลง สำหรับ ETax\n"
            "- do_nothing: ไม่ทำอะไร\n"
            "- cancel: ยกเลิกใบกำกับภาษี\n"
            "- cancel_and_renew\n"
        ),
        allow_customer_to_edit=True,
    ),
    "ETAX_BUYER_REQUEST_ETAX_HANDLING_MODE": SettingKey(
        key="ETAX_BUYER_REQUEST_ETAX_HANDLING_MODE",
        dtype=str,
        default="auto",
        help=(
            "ตั้งค่า mode การทำงานในการรับข้อมูลการขอใบกำกับภาษีจากผู้ซื้อ\n"
            "auto - ระบบจะสร้างใบกำกับภาษี (D1A) อัพโหลดไฟล์ขึ้น Google Drive และส่ง email ให้ลูกค้า\n"
            "manual - ระบบจะบันทึกข้อมูลผู้ซื้อลงใน PickOrder.order_json และรอให้ staff / api มาสร้างใบกำกับ\n"
        ),
        allow_customer_to_edit=False,
    ),
    "ETAX_SMS_SENDER": SettingKey(
        key="ETAX_SMS_SENDER",
        dtype=str,
        default="ETAX",
        help="ตั้งค่า ETax SMS Sender",
        allow_customer_to_edit=False,
    ),
    "ETAX_BYPASS_CUTOFF_DATE_CHECK": SettingKey(
        key="ETAX_BYPASS_CUTOFF_DATE_CHECK",
        dtype=bool,
        default=False,
        help="ข้ามการตรวจสอบวันทีตัดรอบกรมสรรพากร ETax (ETAX_RETRIEVAL_DAY)",
        allow_customer_to_edit=False,
    ),
    "JAMBOLIVE_WEBHOOK_CONFIG": SettingKey(
        key="JAMBOLIVE_WEBHOOK_CONFIG",
        dtype=dict,
        default=json.dumps(DEFAULTS["JAMBOLIVE_WEBHOOK_CONFIG"]),
        help=(
            "ตั้งค่า Jambolive Webhook\n"
            "Dict Keys:\n"
            "- url: string\n"
            "- method: string, 'GET'|'POST'\n"
            "- headers: dict\n"
            "- params: dict\n"
        ),
        allow_customer_to_edit=True,
    ),
    "CF_SHOPS_WEBHOOK_CONFIG": SettingKey(
        key="CF_SHOPS_WEBHOOK_CONFIG",
        dtype=dict,
        default=json.dumps(DEFAULTS["CF_SHOPS_WEBHOOK_CONFIG"]),
        help=(
            "ตั้งค่า CFShop Webhook\n"
            "Dict Keys:\n"
            "- url: string\n"
            "- method: string, 'GET'|'POST'\n"
            "- headers: dict\n"
            "- params: dict\n"
        ),
        allow_customer_to_edit=True,
    ),
    "CUSTOM_BRAND": SettingKey(
        key="CUSTOM_BRAND",
        dtype=str,
        default=None,
        help="ชื่อแบรนด์ที่กำหนดเอง เช่น ANTS",
        allow_customer_to_edit=False,
    ),
    "ETAX_REQUEST_WEBHOOK_ENABLE": SettingKey(
        key="ETAX_REQUEST_WEBHOOK_ENABLE",
        dtype=bool,
        default=False,
        help="เปิดใช้งาน ETax Request Webhook",
        allow_customer_to_edit=True,
    ),
    "ETAX_REQUEST_WEBHOOK_CONFIG": SettingKey(
        key="ETAX_REQUEST_WEBHOOK_CONFIG",
        dtype=dict,
        default=None,
        help=(
            "ตั้งค่า ETax Webhook\n"
            "Dict Keys:\n"
            "- url: string\n"
            "- method: string, 'GET'|'POST'\n"
            "- headers: dict\n"
            "- params: dict\n"
        ),
        allow_customer_to_edit=True,
    ),
    "ETAX_SHIPPING_FEE_SKU": SettingKey(
        key="ETAX_SHIPPING_FEE_SKU",
        dtype=str,
        default="shipping-fee",
        help="SKU ของค่าส่ง",
        allow_customer_to_edit=False,
    ),
    # Serial No.
    "RECORD_SCAN_SERIAL_NO_MODE": SettingKey(
        key="RECORD_SCAN_SERIAL_NO_MODE",
        dtype=str,
        default="NONE",
        help=(
            "ตั้งค่าโหมดการสแกนสินค้าด้วยระบบ Serial No:\n"
            "- NONE: ปิดการสแกน Serial No\n"
            "- SERIAL_NO_ONLY: สแกนเฉพาะ Serial No เท่านั้น\n"
            "- SERIAL_NO_AND_SKU: สแกน Serial No และ SKU"
        ),
        allow_customer_to_edit=True,
    ),
    # WooCommerce API
    "WOO_COMMERCE_API": SettingKey(
        key="WOO_COMMERCE_API",
        dtype=dict,
        default=dict,
        help="ตั้งค่าร้านค้า WooCommerce API",
        allow_customer_to_edit=True,
    ),
}
