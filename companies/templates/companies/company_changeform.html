{% extends 'admin/change_form.html' %}


{% block content %}
    <a href="https://everything-dobybot.anvil.app/#!?form=CompanyEditForm&company_id={{object_id}}">Edit in anvil</a>
    {{ block.super }}
    {% comment %} <script src="https://anvil.works/embed.js"></script> {% endcomment %}
    {% comment %} <iframe style="width:100%" data-anvil-embed src="https://everything-dobybot.anvil.app/#!?form=CompanyEditForm&company_id={{object_id}}"></iframe> {% endcomment %}
    {% comment %} <iframe src="https://everything-dobybot.anvil.app/#!?form=CompanyEditForm&company_id={{object_id}}"></iframe> {% endcomment %}

    <script src="https://storage.googleapis.com/dobybot-public-bucket/js/jquery/jquery.min.js"></script>
    <script src="https://storage.googleapis.com/dobybot-public-bucket/js/jquery/json-viewer/jquery.json-viewer.min.js"></script>
    <link rel="stylesheet" href="https://storage.googleapis.com/dobybot-public-bucket/js/jquery/json-viewer/jquery.json-viewer.min.css">
    <style>
        .json-dict {
            margin-left: 0 !important;
        }
    </style>
    <script>
        $(document).ready(() => {
            var show_json_viewer = true

            const container = $('.field-setting_json_str > div')
            container.append('<div class="jsonviewer" style="margin-left: 170px;">test json viwere</div>')
            container.find('label').append('<br>')
            container.find('label').append('<a href="!#" id="toggle-json-viewer">Toggle Viewer</button>')

            const div_plaintext = $('.field-setting_json_str > div > .readonly')
            const div_jsonviewer = $('.field-setting_json_str > div > .jsonviewer')

            console.log({ div_jsonviewer, div_jsonviewer })
            const jsondata = JSON.parse(div_plaintext.text())
            console.log(jsondata)

            div_plaintext.css("white-space", "pre-wrap")
            div_plaintext.hide()

            div_jsonviewer.jsonViewer(jsondata);

            $('#toggle-json-viewer').click((e) => {
                e.preventDefault();
                if (show_json_viewer) {
                    show_json_viewer = false
                    div_jsonviewer.hide()
                    div_plaintext.show()
                } else {
                    show_json_viewer = true
                    div_jsonviewer.show()
                    div_plaintext.hide()
                }
            })
        })
        
    </script>
{% endblock %}