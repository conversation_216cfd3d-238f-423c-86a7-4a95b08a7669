from django.conf import settings
from django.core.management.base import BaseCommand

from companies.models.models import Company
from services.dobybot_connect.dobysync import DobySyncIntegratorService
from services.dobybot_connect.dobysync_types import DobySyncClient
from users.models import User


class Command(BaseCommand):
    help = "Set dobysync product webhook"

    def handle(self, *args, **options):
        dobysync_companies = Company.objects.filter(
            settings_json__DOBYBOT_CONNECT_VERSION=2
        )
        for company in dobysync_companies:
            user = User.objects.filter(
                company=company, username__startswith="dobybot-connect"
            ).first()
            if not user:
                raise Exception(f"User not found for company {company}")

            client = DobySyncClient(**company.get_setting("DOBYSYNC_CLIENT"))
            client.client = DobySyncIntegratorService.update_client(
                client.client.uuid,
                {
                    "config": {
                        "order_webhook": {
                            "url": settings.CLOUD_TASK_HOST
                            + "/api/picking/orders/sync/",
                            "headers": {
                                "Authorization": "Token " + user.auth_token.key
                            },
                        },
                        "product_webhook": {
                            "url": settings.CLOUD_TASK_HOST
                            + "/api/picking/dobysync/sync-products/",
                            "headers": {
                                "Authorization": "Token " + user.auth_token.key
                            },
                        },
                    }
                },
            )
            company.set_setting("DOBYSYNC_CLIENT", client.dict(), commit=True)
