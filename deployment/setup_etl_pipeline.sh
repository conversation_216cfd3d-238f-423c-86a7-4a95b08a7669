#!/bin/bash

# Setup ETL Pipeline to BigQuery
# This script sets up the complete ETL pipeline infrastructure

set -e

# Configuration - Update these values
PROJECT_ID=${PROJECT_ID:-"your-gcp-project-id"}
REGION=${REGION:-"asia-southeast1"}
ETL_BUCKET_NAME=${ETL_BUCKET_NAME:-"dobybot-etl-bucket"}
APP_URL=${APP_URL:-"https://your-app-url.com"}

echo "Setting up ETL Pipeline to BigQuery..."
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "ETL Bucket: $ETL_BUCKET_NAME"
echo "App URL: $APP_URL"

# 1. Create GCS bucket for ETL files (if it doesn't exist)
echo ""
echo "1. Creating GCS bucket for ETL files..."
if ! gsutil ls -b gs://$ETL_BUCKET_NAME > /dev/null 2>&1; then
    gsutil mb -p $PROJECT_ID -l $REGION gs://$ETL_BUCKET_NAME
    echo "Created bucket: gs://$ETL_BUCKET_NAME"
else
    echo "Bucket already exists: gs://$ETL_BUCKET_NAME"
fi

# 2. Enable required APIs
echo ""
echo "2. Enabling required Google Cloud APIs..."
gcloud services enable bigquery.googleapis.com --project=$PROJECT_ID
gcloud services enable cloudfunctions.googleapis.com --project=$PROJECT_ID
gcloud services enable cloudscheduler.googleapis.com --project=$PROJECT_ID
gcloud services enable cloudtasks.googleapis.com --project=$PROJECT_ID
gcloud services enable storage.googleapis.com --project=$PROJECT_ID

# 3. Deploy Cloud Function
echo ""
echo "3. Deploying BigQuery Loader Cloud Function..."
BUCKET_NAME=$ETL_BUCKET_NAME ./deployment/deploy_cloud_function.sh

# 4. Create Cloud Tasks queue for ETL jobs
echo ""
echo "4. Creating Cloud Tasks queue for ETL jobs..."
if ! gcloud tasks queues describe etl-export --location=$REGION --project=$PROJECT_ID > /dev/null 2>&1; then
    gcloud tasks queues create etl-export \
        --location=$REGION \
        --project=$PROJECT_ID \
        --max-concurrent-dispatches=5 \
        --max-dispatches-per-second=1
    echo "Created Cloud Tasks queue: etl-export"
else
    echo "Cloud Tasks queue already exists: etl-export"
fi

# 5. Create Cloud Scheduler job for hourly ETL
echo ""
echo "5. Creating Cloud Scheduler job for hourly ETL..."
read -p "Enter API token for authentication: " API_TOKEN

if ! gcloud scheduler jobs describe etl-hourly-export --location=$REGION --project=$PROJECT_ID > /dev/null 2>&1; then
    gcloud scheduler jobs create http etl-hourly-export \
        --schedule="0 * * * *" \
        --uri="$APP_URL/api/etl/export/" \
        --http-method=POST \
        --headers="Content-Type=application/json,Authorization=Bearer $API_TOKEN" \
        --message-body='{"job_type": "full_export"}' \
        --time-zone="Asia/Bangkok" \
        --location=$REGION \
        --project=$PROJECT_ID
    echo "Created Cloud Scheduler job: etl-hourly-export"
else
    echo "Cloud Scheduler job already exists: etl-hourly-export"
fi

# 6. Set up BigQuery datasets (example for company 1)
echo ""
echo "6. Setting up example BigQuery dataset..."
DATASET_NAME="dobybot_company_1"
if ! bq ls -d $PROJECT_ID:$DATASET_NAME > /dev/null 2>&1; then
    bq mk --location=$REGION --description="Dobybot data for company 1" $PROJECT_ID:$DATASET_NAME
    echo "Created BigQuery dataset: $DATASET_NAME"
else
    echo "BigQuery dataset already exists: $DATASET_NAME"
fi

echo ""
echo "✅ ETL Pipeline setup completed!"
echo ""
echo "Next steps:"
echo "1. Add 'etl' to INSTALLED_APPS in Django settings"
echo "2. Run Django migrations: python manage.py makemigrations etl && python manage.py migrate"
echo "3. Add ETL URLs to main urls.py: path('api/etl/', include('etl.urls'))"
echo "4. Update environment variables:"
echo "   - GS_ETL_BUCKET_NAME=$ETL_BUCKET_NAME"
echo "5. Test the pipeline:"
echo "   - Manual: python manage.py export_orders_to_bigquery --company-id 1 --dry-run"
echo "   - API: POST $APP_URL/api/etl/export/"
echo ""
echo "Cloud Function URL (for testing):"
echo "https://$REGION-$PROJECT_ID.cloudfunctions.net/bigquery-loader-http"
