#!/bin/bash

# Deploy BigQuery Loader Cloud Function
# This script deploys the Cloud Function that processes CSV files and loads them to BigQuery

set -e

# Configuration
PROJECT_ID=${PROJECT_ID:-"your-gcp-project-id"}
FUNCTION_NAME="bigquery-loader"
REGION=${REGION:-"asia-southeast1"}
BUCKET_NAME=${BUCKET_NAME:-"your-etl-bucket"}

echo "Deploying BigQuery Loader Cloud Function..."
echo "Project: $PROJECT_ID"
echo "Function: $FUNCTION_NAME"
echo "Region: $REGION"
echo "Trigger Bucket: $BUCKET_NAME"

# Deploy the function
gcloud functions deploy $FUNCTION_NAME \
    --runtime python39 \
    --trigger-bucket $BUCKET_NAME \
    --entry-point load_csv_to_bigquery \
    --source cloud_functions/bigquery_loader/ \
    --region $REGION \
    --project $PROJECT_ID \
    --memory 512MB \
    --timeout 540s \
    --max-instances 10

echo "Cloud Function deployed successfully!"
echo "Function will be triggered when files are uploaded to gs://$BUCKET_NAME"

# Optional: Deploy HTTP version for testing
echo ""
echo "Deploying HTTP version for testing..."
gcloud functions deploy ${FUNCTION_NAME}-http \
    --runtime python39 \
    --trigger-http \
    --entry-point hello_world \
    --source cloud_functions/bigquery_loader/ \
    --region $REGION \
    --project $PROJECT_ID \
    --allow-unauthenticated

echo "HTTP function deployed for testing at:"
echo "https://$REGION-$PROJECT_ID.cloudfunctions.net/${FUNCTION_NAME}-http"
