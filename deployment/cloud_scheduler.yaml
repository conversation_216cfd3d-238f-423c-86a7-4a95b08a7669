# Cloud Scheduler configuration for hourly ETL jobs
# This file can be used with gcloud scheduler jobs create http

# Example command to create the scheduler job:
# gcloud scheduler jobs create http etl-hourly-export \
#   --schedule="0 * * * *" \
#   --uri="https://your-app-url.com/api/etl/export/" \
#   --http-method=POST \
#   --headers="Content-Type=application/json,Authorization=Bearer YOUR_API_TOKEN" \
#   --message-body='{"job_type": "full_export"}' \
#   --time-zone="Asia/Bangkok"

# For multiple companies, create separate jobs:
# gcloud scheduler jobs create http etl-hourly-export-company-1 \
#   --schedule="0 * * * *" \
#   --uri="https://your-app-url.com/api/etl/export/" \
#   --http-method=POST \
#   --headers="Content-Type=application/json,Authorization=Bearer COMPANY_1_API_TOKEN" \
#   --message-body='{"job_type": "full_export"}' \
#   --time-zone="Asia/Bangkok"

# Alternative: Use Cloud Tasks to trigger for all companies
# gcloud scheduler jobs create http etl-hourly-trigger-all \
#   --schedule="0 * * * *" \
#   --uri="https://your-app-url.com/api/etl/trigger-all-companies/" \
#   --http-method=POST \
#   --headers="Content-Type=application/json,Authorization=Bearer SYSTEM_TOKEN" \
#   --time-zone="Asia/Bangkok"
