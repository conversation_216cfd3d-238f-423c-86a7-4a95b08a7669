"""
Cloud Function to load CSV files from GCS to BigQuery
Triggered when CSV files are uploaded to the ETL bucket
"""

import json
import logging
from datetime import datetime
from google.cloud import bigquery
from google.cloud import storage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def upsert_data_from_csv(bq_client, target_table_id, csv_uri, schema, table_type):
    """Upsert data from CSV using MERGE statement to avoid duplicates"""

    # Create temporary table for new data
    temp_table_id = f"{target_table_id}_temp_{int(datetime.now().timestamp())}"

    # Determine merge key
    if table_type == "orders":
        merge_key = "order_id"
    else:  # order_items
        merge_key = "order_item_id"

    try:
        # Create temporary table
        temp_table = bigquery.Table(temp_table_id, schema=schema)
        temp_table = bq_client.create_table(temp_table)
        logger.info(f"Created temporary table: {temp_table_id}")

        # Load CSV into temporary table
        job_config = bigquery.LoadJobConfig(
            source_format=bigquery.SourceFormat.CSV,
            skip_leading_rows=1,
            schema=schema,
            write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
            allow_quoted_newlines=True,
            allow_jagged_rows=False,
            max_bad_records=0,
        )

        load_job = bq_client.load_table_from_uri(
            csv_uri, temp_table_id, job_config=job_config
        )
        load_job.result()  # Wait for completion
        logger.info(f"Loaded {load_job.output_rows} rows into temporary table")

        # Perform MERGE operation
        field_names = [field.name for field in schema]
        field_list = ", ".join(field_names)
        update_list = ", ".join(
            [
                f"target.{field} = source.{field}"
                for field in field_names
                if field != merge_key
            ]
        )

        merge_query = f"""
        MERGE `{target_table_id}` AS target
        USING `{temp_table_id}` AS source
        ON target.{merge_key} = source.{merge_key}
        WHEN MATCHED THEN
            UPDATE SET {update_list}
        WHEN NOT MATCHED THEN
            INSERT ({field_list})
            VALUES ({", ".join([f"source.{field}" for field in field_names])})
        """

        merge_job = bq_client.query(merge_query)
        merge_job.result()  # Wait for completion

        logger.info(
            f"MERGE operation completed. Affected rows: {merge_job.num_dml_affected_rows}"
        )
        return True

    except Exception as e:
        logger.error(f"Error in upsert operation: {e}")
        return False

    finally:
        # Clean up temporary table
        try:
            bq_client.delete_table(temp_table_id)
            logger.info(f"Cleaned up temporary table: {temp_table_id}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary table: {e}")


def load_csv_to_bigquery(event, context):
    """
    Cloud Function triggered by GCS object creation
    Loads CSV files to BigQuery tables
    """
    try:
        # Parse the event
        bucket_name = event["bucket"]
        file_name = event["name"]

        logger.info(f"Processing file: gs://{bucket_name}/{file_name}")

        # Only process ETL CSV files
        if not file_name.startswith("etl/"):
            logger.info(f"Skipping non-ETL file: {file_name}")
            return

        # Determine table type from file path
        if "/orders/" in file_name and file_name.endswith(".csv"):
            table_type = "orders"
        elif "/order_items/" in file_name and file_name.endswith(".csv"):
            table_type = "order_items"
        else:
            logger.info(f"Skipping non-CSV or unrecognized file: {file_name}")
            return

        # Extract company ID from file path
        # Expected format: etl/orders/company_123/orders_2024-01-01_2024-01-31_20240101_120000.csv
        path_parts = file_name.split("/")
        if len(path_parts) < 3:
            logger.error(f"Invalid file path format: {file_name}")
            return

        company_folder = path_parts[2]  # company_123
        if not company_folder.startswith("company_"):
            logger.error(f"Invalid company folder format: {company_folder}")
            return

        company_id = company_folder.replace("company_", "")

        # Initialize BigQuery client
        bq_client = bigquery.Client()

        # Define dataset and table names
        dataset_name = f"dobybot_company_{company_id}"
        table_name = table_type  # 'orders' or 'order_items'

        # Create dataset if it doesn't exist
        dataset_id = f"{bq_client.project}.{dataset_name}"
        try:
            bq_client.get_dataset(dataset_id)
        except Exception:
            dataset = bigquery.Dataset(dataset_id)
            dataset.location = "US"
            dataset.description = f"Dobybot data for company {company_id}"
            bq_client.create_dataset(dataset)
            logger.info(f"Created dataset: {dataset_id}")

        # Define table schemas
        if table_type == "orders":
            schema = [
                bigquery.SchemaField("order_id", "INTEGER"),
                bigquery.SchemaField("uuid", "STRING"),
                bigquery.SchemaField("company_id", "INTEGER"),
                bigquery.SchemaField("order_number", "STRING"),
                bigquery.SchemaField("order_saleschannel", "STRING"),
                bigquery.SchemaField("order_customer", "STRING"),
                bigquery.SchemaField("order_customerphone", "STRING"),
                bigquery.SchemaField("order_trackingno", "STRING"),
                bigquery.SchemaField("order_warehousecode", "STRING"),
                bigquery.SchemaField("order_shippingchannel", "STRING"),
                bigquery.SchemaField("order_total_quantity", "INTEGER"),
                bigquery.SchemaField("order_total_price", "NUMERIC"),
                bigquery.SchemaField("order_oms", "STRING"),
                bigquery.SchemaField("order_marketplace", "STRING"),
                bigquery.SchemaField("order_marketplaceshop", "STRING"),
                bigquery.SchemaField("order_date", "DATE"),
                bigquery.SchemaField("order_json", "JSON"),
                bigquery.SchemaField("packing_json", "JSON"),
                bigquery.SchemaField("receipt_url", "STRING"),
                bigquery.SchemaField("short_receipt_url", "STRING"),
                bigquery.SchemaField("ready_to_ship", "BOOLEAN"),
                bigquery.SchemaField("ready_to_ship_timestamp", "TIMESTAMP"),
                bigquery.SchemaField("create_date", "TIMESTAMP"),
                bigquery.SchemaField("update_date", "TIMESTAMP"),
                bigquery.SchemaField("etl_processed_at", "TIMESTAMP"),
            ]
        else:  # order_items
            schema = [
                bigquery.SchemaField("order_item_id", "STRING"),
                bigquery.SchemaField("order_id", "INTEGER"),
                bigquery.SchemaField("order_uuid", "STRING"),
                bigquery.SchemaField("company_id", "INTEGER"),
                bigquery.SchemaField("order_number", "STRING"),
                bigquery.SchemaField("product_id", "INTEGER"),
                bigquery.SchemaField("sku", "STRING"),
                bigquery.SchemaField("name", "STRING"),
                bigquery.SchemaField("quantity", "INTEGER"),
                bigquery.SchemaField("unit_text", "STRING"),
                bigquery.SchemaField("price_per_unit", "NUMERIC"),
                bigquery.SchemaField("discount", "STRING"),
                bigquery.SchemaField("discount_amount", "NUMERIC"),
                bigquery.SchemaField("total_price", "NUMERIC"),
                bigquery.SchemaField("product_type", "INTEGER"),
                bigquery.SchemaField("serial_no_list", "JSON"),
                bigquery.SchemaField("sku_type", "STRING"),
                bigquery.SchemaField("order_date", "DATE"),
                bigquery.SchemaField("etl_processed_at", "TIMESTAMP"),
            ]

        # Create table if it doesn't exist
        table_id = f"{dataset_id}.{table_name}"
        try:
            bq_client.get_table(table_id)
        except Exception:
            table = bigquery.Table(table_id, schema=schema)
            bq_client.create_table(table)
            logger.info(f"Created table: {table_id}")

        # Use MERGE for upsert instead of simple append
        success = upsert_data_from_csv(
            bq_client, table_id, f"gs://{bucket_name}/{file_name}", schema, table_type
        )

        if not success:
            raise Exception(f"Failed to upsert data for {table_type}")

        # Get job statistics
        destination_table = bq_client.get_table(table_id)
        logger.info(
            f"Successfully processed {file_name} to {table_id}. "
            f"Total rows in table: {destination_table.num_rows}"
        )

        return {
            "status": "success",
            "file": file_name,
            "table": table_id,
            "total_rows": destination_table.num_rows,
        }

    except Exception as e:
        logger.error(f"Error processing file {file_name}: {str(e)}")
        raise e


def hello_world(request):
    """HTTP Cloud Function for testing"""
    return f"BigQuery Loader Cloud Function is running!"
