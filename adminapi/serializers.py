from email.policy import default
from rest_framework import serializers
from dynamic_rest.serializers import DynamicModelSerializer, DynamicRelationField
from adminapi.mixins import WriteOnceMixin
from companies.models import Company
from logger.models import VideoRecordLog
from picking.models import PickOrder, ProductSet
from users.models import User
from wallets.models import (
    GoogleDrivePackage,
    GoogleDriveTransaction,
    RecordPackage,
    RecordTransaction,
    SmsTransaction,
    Wallet,
)
from django.contrib.auth.models import Group, Permission
from rest_framework.authtoken.models import Token


class AdminCompanySerializer(DynamicModelSerializer):
    wallet = DynamicRelationField("AdminWalletSerializer", embed=True)
    package_name = serializers.ReadOnlyField(source="get_package_display")

    class Meta:
        model = Company
        fields = "__all__"
        read_only_fields = ["uuid", "wallet"]


class AdminWalletSerializer(DynamicModelSerializer, WriteOnceMixin):
    company = DynamicRelationField("AdminCompanySerializer")
    recordpackage_set = DynamicRelationField(
        "AdminRecordPackageSerializer", many=True, embed=True
    )
    googledrivepackage_set = DynamicRelationField(
        "AdminGoogleDrivePackageSerializer", many=True, embed=True
    )

    record_balance = serializers.SerializerMethodField()
    def get_record_balance(self, obj: Wallet):
        return obj.get_record_balance()
    
    realtime_record_balance = serializers.SerializerMethodField()
    def get_realtime_record_balance(self, obj: Wallet):
        return obj.get_realtime_record_balance()

    google_drive_limit = serializers.SerializerMethodField()
    def get_google_drive_limit(self, obj: Wallet):
        return obj.get_google_drive_limit()

    class Meta:
        model = Wallet
        fields = "__all__"
        read_only_fields = [
            "sms_balance",
            "record_balance",
            "realtime_record_balance",
            "google_drive_usage",
            "google_drive_limit",
            "recordpackage_set",
            "googledrivepackage_set",
        ]
        write_once_fields = ["company"]


class AdminRecordPackageSerializer(DynamicModelSerializer):
    class Meta:
        model = RecordPackage
        fields = "__all__"


class AdminGoogleDrivePackageSerializer(DynamicModelSerializer):
    class Meta:
        model = GoogleDrivePackage
        fields = "__all__"


class AdminSmsTransactionSerializer(DynamicModelSerializer, WriteOnceMixin):
    company = serializers.ReadOnlyField(source="wallet.company_id")
    wallet = DynamicRelationField(AdminWalletSerializer)

    class Meta:
        model = SmsTransaction
        fields = "__all__"
        read_only_fields = [
            "date",
            "type",
            "wallet",
            "description",
            "value",
            "running_balance",
            "create_date",
            "create_by",
        ]


class AdminRecordTransactionSerializer(DynamicModelSerializer, WriteOnceMixin):
    company = serializers.ReadOnlyField(source="wallet.company_id")
    wallet = DynamicRelationField(AdminWalletSerializer)

    class Meta:
        model = RecordTransaction
        fields = "__all__"
        read_only_fields = [
            "date",
            "type",
            "wallet",
            "description",
            "value",
            "running_balance",
            "create_date",
            "create_by",
        ]


class AdminGoogleDriveTransactionSerializer(DynamicModelSerializer, WriteOnceMixin):
    company = serializers.ReadOnlyField(source="wallet.company_id")
    wallet = DynamicRelationField(AdminWalletSerializer)

    class Meta:
        model = GoogleDriveTransaction
        fields = "__all__"
        read_only_fields = [
            "date",
            "type",
            "wallet",
            "description",
            "value",
            "running_balance",
            "create_date",
            "create_by",
        ]


class AdminUserTokenSerializer(DynamicModelSerializer):
    class Meta:
        model = Token
        fields = ["key", "user_id", "created"]


class AdminGroupSerializer(DynamicModelSerializer):
    class Meta:
        model = Group
        fields = ["id", "name"]


class AdminUserSerializer(DynamicModelSerializer):
    company = DynamicRelationField("AdminCompanySerializer")
    auth_token = DynamicRelationField("AdminUserTokenSerializer")
    password = serializers.CharField(write_only=True, required=False)

    is_owner = serializers.BooleanField(write_only=True, default=False)
    create_token = serializers.BooleanField(write_only=True, default=False)
    groups = DynamicRelationField(
        "AdminGroupSerializer",
        many=True,
        embed=True,
        read_only=True,
    )

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "password",
            "is_active",
            "first_name",
            "last_name",
            "company",
            "auth_token",
            "is_owner",
            "create_token",
            "groups",
        ]


class AdminPickOrderSerializer(DynamicModelSerializer):
    company = DynamicRelationField("AdminCompanySerializer", read_only=True)

    class Meta:
        model = PickOrder
        fields = "__all__"


class AdminVideoRecordLogSerializer(DynamicModelSerializer):
    class Meta:
        model = VideoRecordLog
        fields = "__all__"


class AdminProductSetSerializer(DynamicModelSerializer):
    class Meta:
        model = ProductSet
        fields = "__all__"
