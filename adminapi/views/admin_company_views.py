from typing import Optional, Literal, Tuple
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response

from datetime import timedelta
from django.utils import timezone
from companies.models import Company
from companies.serializers.serializers import CompanySerializer
from services.google.drive.drive import GoogleDriveService
from wallets.models import (
    GoogleDrivePackage,
    RecordPackage,
    RecordTransaction,
    RecordTransactionType,
    Wallet,
)
from users.models import User
from django.contrib.auth.models import Group, Permission
from pydantic import BaseModel, Field, ValidationError
from adminapi.schema.dobybot_payment import Order, MainPackage, AddonPackage
from django.utils.decorators import method_decorator
from django.db import transaction

import string
import random


def get_easy_password(length=8):
    chars = string.ascii_uppercase + string.digits
    return "".join(random.choice(chars) for _ in range(length))


class CompanyRegisterSerializer(serializers.ModelSerializer):
    record_packages = serializers.SerializerMethodField()
    gdrive_packages = serializers.SerializerMethodField()

    class Meta:
        model = Company
        fields = [
            "id",
            "uuid",
            "name",
            "account_suffix",
            "sms_balance",
            "record_balance",
            # "expire_date",
            "is_active",
            "is_active_remark",
            "google_drive_usage",
            "google_drive_limit",
            # "wallet_last_update_datetime",
            "package",
            "force_redirect",
            "web_logo",
            "record_packages",
            "gdrive_packages",
        ]

    def get_record_packages(self, company: Company):
        record_packages = RecordPackage.objects.filter(
            wallet__company=company, is_active=True
        ).values(
            "id", "name", "balance", "realtime_balance", "create_date", "expire_date"
        )
        return [record_packages]

    def get_gdrive_packages(self, company: Company):
        gdrive_packages = GoogleDrivePackage.objects.filter(
            wallet__company=company, is_active=True
        ).values("id", "name", "storage_amount_gb", "create_date", "expire_date")
        return [gdrive_packages]


class CompanyRegisterAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    @method_decorator(transaction.atomic)
    def post(self, request):
        try:
            order = Order(**request.data)
        except ValidationError as e:
            raise serializers.ValidationError(e.errors())

        # create company
        company = Company.objects.create(
            **order.company.model_dump(exclude={"dobybot_uuid", "logo"}), is_setup=False
        )

        # create Wallet
        wallet = Wallet.objects.create(
            company=company,
            version=2,
        )
        company.refresh_from_db()

        # setup packages
        self.topup_packages(company, order)

        # create user & setup permissions
        if company.package == Company.PACKAGE_RECORD_ONLY:
            company.set_setting("CONFIRM_RECORD_PASSWORD_ENABLE", True)
            company.set_setting("CONFIRM_RECORD_PASSWORD_CONFIG", ["DUPLICATE_RECORD"])
            company.set_setting("POST_RECORD_ACTION_SHARE_VIDEO_LINK", True)
            company.set_setting("FIXCASE_SEND_CLOSE_MESSAGE", False)
            company.set_setting("FIXCASE_SEND_OPEN_MESSAGE", False)
            # admin_group = Group.objects.get(name="AdminRecordOnly")
        elif company.package == Company.PACKAGE_FULL_INTEGRATION:
            company.set_setting("POST_RECORD_ACTION_SEND_SMS", True)
            company.set_setting("CONFIRM_RECORD_PASSWORD_ENABLE", True)
            company.set_setting("STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG", True)
            company.set_setting(
                "CONFIRM_RECORD_PASSWORD_CONFIG",
                ["FIXCASE", "DUPLICATE_RECORD", "REMARK", "ORDER_NOT_FOUND"],
            )
            company.set_setting("POST_RECORD_ACTION_SHARE_VIDEO_LINK", True)
            company.set_setting("POST_RECORD_ACTION_READY_TO_SHIP", True)
            company.set_setting("RECORD_PIECE_SCAN_MODE_ENABLE", True)
            company.set_setting("MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS", True)
            company.set_setting("LOW_RECORD_CREDIT_WARNING_AMOUNT", 0)
            # admin_group = Group.objects.get(name="owner")

        # password = get_easy_password()
        # user: User = User.objects.create_user(
        #     username=f'admin{company.account_suffix}',
        #     password=password,
        #     is_superuser=False,
        #     is_staff=False,
        #     company=company,
        # )

        # perm_add_videorecordlog = Permission.objects.get(codename="add_videorecordlog")
        # user.user_permissions.add(perm_add_videorecordlog)
        # user.groups.set([admin_group])

        user, password = self.create_admin_user(company, "admin")

        # create share drive
        # share_drive_url = ''
        # if (GoogleDrivePackage.objects.filter(wallet=wallet, is_active=True).exists()
        #         and not company.get_setting('GOOGLE_DRIVE_SHARE_DRIVE_ID')):
        #     gdrive = GoogleDriveService()
        #     share_drive = gdrive.create_share_drive(name=f'DBB-{company.name}')
        #     share_drive_url = f'https://drive.google.com/drive/folders/{share_drive["id"]}'

        serializer = CompanyRegisterSerializer(company)
        return Response(
            {
                "company": serializer.data,
                "login": {"username": user.username, "password": password},
                "share_drive_url": "",
            }
        )

    @staticmethod
    def create_admin_user(
        company: Company, username: str, password: str = None, is_temporary: bool = True
    ) -> Tuple[User, str]:
        if company.package == Company.PACKAGE_RECORD_ONLY:
            admin_group = Group.objects.get(name="AdminRecordOnly")
        elif company.package == Company.PACKAGE_FULL_INTEGRATION:
            admin_group = Group.objects.get(name="owner")
        else:
            raise NotImplementedError(f"Unknown company package: {company.package}")

        if not password:
            password = get_easy_password()

        user: User = User.objects.create_user(
            username=f"{username}{company.account_suffix}",
            password=password,
            is_superuser=False,
            is_staff=False,
            company=company,
            is_temporary=is_temporary,
        )

        perm_add_videorecordlog = Permission.objects.get(codename="add_videorecordlog")
        user.user_permissions.add(perm_add_videorecordlog)
        user.groups.set([admin_group])
        return user, password

    @classmethod
    def topup_packages(cls, company: Company, order: Order):
        for item in order.purchase_items:
            if item.type == "main_package":
                cls.topup_main_package(company, order, item.detail)
            elif item.type == "addon_package":
                cls.topup_addon_package(company, order, item.detail)
            elif item.type == "product":
                pass  # do nothing
            elif item.type == "entrance_fee":
                pass  # do nothing
            elif item.type == "shipping_fee":
                pass  # do nothing
            else:
                raise NotImplementedError(f"Unknown purchase item type: {item.type}")

    @staticmethod
    def topup_main_package(company: Company, order: Order, main_package: MainPackage):
        # Disable all main package
        RecordPackage.objects.filter(wallet=company.wallet, is_main=True).update(
            is_main=False
        )
        GoogleDrivePackage.objects.filter(wallet=company.wallet, is_main=True).update(
            is_active=False
        )

        # Update company data
        DBB_PACKAGES = {
            "RECORD_ONLY": Company.PACKAGE_RECORD_ONLY,
            "RECORD_CONNECT": Company.PACKAGE_FULL_INTEGRATION,
            "RECORD_CONNECT_AUTOPRINT": Company.PACKAGE_FULL_INTEGRATION,
        }
        company.package = DBB_PACKAGES[main_package.package_type]
        company.web_logo = order.company.logo
        company.expire_date = timezone.now() + timedelta(
            days=main_package.duration_days
        )
        company.save()

        # Create new main RecordPackage
        if main_package.record_amount > 0:
            pkg = RecordPackage.objects.create(
                wallet=company.wallet,
                name=main_package.name,
                package_detail=main_package.model_dump(),
                order_number=order.order_number,
                balance=main_package.record_amount,
                realtime_balance=main_package.record_amount,
                is_main=True,
                is_active=True,
                create_date=timezone.now(),
                expire_date=timezone.now() + timedelta(days=main_package.duration_days),
            )
            wallet: Wallet = company.wallet

            last_balance = RecordTransaction.get_last_running_balance(
                wallet=company.wallet
            )
            RecordTransaction.objects.create(
                date=timezone.localdate(),
                type=RecordTransactionType.DEPOSIT.value,
                wallet=company.wallet,
                description=f"Topup {main_package.record_amount:,.2f} record credit, by system",
                value=main_package.record_amount,
                running_balance=last_balance + main_package.record_amount,
                create_by=None,
                deduct_from=pkg,
            )

        # Create new main GoogleDrivePackage
        if main_package.storage_amount_gb > 0:
            GoogleDrivePackage.objects.create(
                wallet=company.wallet,
                name=main_package.name,
                package_detail=main_package.model_dump(),
                order_number=order.order_number,
                storage_amount_gb=main_package.storage_amount_gb,
                is_main=True,
                is_active=True,
                create_date=timezone.now(),
                expire_date=timezone.now() + timedelta(days=main_package.duration_days),
            )

    @staticmethod
    def topup_addon_package(
        company: Company, order: Order, addon_package: AddonPackage
    ):
        if addon_package.record_amount > 0:
            pkg = RecordPackage.objects.create(
                wallet=company.wallet,
                name=addon_package.name,
                package_detail=addon_package.model_dump(),
                order_number=order.order_number,
                balance=addon_package.record_amount,
                realtime_balance=addon_package.record_amount,
                is_main=False,
                is_active=True,
                create_date=timezone.now(),
                expire_date=timezone.now()
                + timedelta(days=addon_package.duration_days),
            )

            last_balance = RecordTransaction.get_last_running_balance(
                wallet=company.wallet
            )
            RecordTransaction.objects.create(
                date=timezone.localdate(),
                type=RecordTransactionType.DEPOSIT.value,
                wallet=company.wallet,
                description=f"Topup {addon_package.record_amount:,.2f} record credit, by system",
                value=addon_package.record_amount,
                running_balance=addon_package.record_amount + last_balance,
                create_by=None,
                deduct_from=pkg,
            )

        if addon_package.storage_amount_gb > 0:
            GoogleDrivePackage.objects.create(
                wallet=company.wallet,
                name=addon_package.name,
                package_detail=addon_package.model_dump(),
                order_number=order.order_number,
                storage_amount_gb=addon_package.storage_amount_gb,
                is_main=False,
                is_active=True,
                create_date=timezone.now(),
                expire_date=timezone.now()
                + timedelta(days=addon_package.duration_days),
            )


class CompanyTopupAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    @method_decorator(transaction.atomic)
    def post(self, request):
        try:
            order = Order(**request.data)

        except ValidationError as e:
            raise serializers.ValidationError(e.errors())

        # check if purchase item is not have type 'main_package' or 'addon_package'
        if not any(
            item.type in ["main_package", "addon_package"]
            for item in order.purchase_items
        ):
            return Response(
                {
                    "message": "Do nothing because not have main_package or addon_package in purchase_items",
                    "status": "DO_NOTHING",
                }
            )

        company: Company = Company.objects.get(uuid=order.company.dobybot_uuid)
        wallet: Wallet = company.wallet
        if wallet.version == 1:
            # 1. Call topup_packages
            CompanyRegisterAPI.topup_packages(company, order)

            # 2. Create record package from old wallet.record_balance
            main_record_pkg = RecordPackage.objects.filter(
                wallet=wallet, is_main=True
            ).first()
            if not main_record_pkg:
                raise Exception("Main record package not found")

            RecordPackage.objects.create(
                wallet=company.wallet,
                name="Convert from wallet v1",
                package_detail={},
                order_number="-",
                balance=wallet.record_balance,
                realtime_balance=wallet.record_balance,
                is_main=False,
                is_active=True,
                create_date=timezone.now(),
                expire_date=main_record_pkg.expire_date - timedelta(seconds=1),
            )

            # 3. Create gdrive package from old wallet.google_drive_limit or from order google drive package
            main_gdrive_pkg = GoogleDrivePackage.objects.filter(
                wallet=wallet, is_main=True
            ).first()

            old_gdrive_limit = wallet.google_drive_limit / 1000
            new_gdrive_limit = (
                main_gdrive_pkg.storage_amount_gb if main_gdrive_pkg else 0
            )
            gdrive_limit = max([old_gdrive_limit, new_gdrive_limit])
            if main_gdrive_pkg:
                main_gdrive_pkg.storage_amount_gb = gdrive_limit
                main_gdrive_pkg.save()
            else:
                GoogleDrivePackage.objects.create(
                    wallet=company.wallet,
                    name="Convert from wallet v1",
                    package_detail={},
                    order_number="-",
                    storage_amount_gb=gdrive_limit,
                    is_main=True,
                    is_active=True,
                    create_date=timezone.now(),
                    expire_date=main_record_pkg.expire_date - timedelta(seconds=1),
                )

            wallet.version = 2
            wallet.save()

            return Response({"message": "OK", "status": "OK"})

        if wallet.version == 2:
            CompanyRegisterAPI.topup_packages(company, order)

            return Response({"message": "OK", "status": "OK"})
