from rest_framework.views import APIView
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import TokenAuthentication
from rest_framework import serializers
from django.db.models import Q

from companies.models.models import Company
from users.models import User
from utils.query_string import parse_qs


class CompanyPkField(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        return Company.objects.all()


class UserDeviceStatusListAPI(ListAPIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class QueryString(serializers.Serializer):
        company = CompanyPkField(required=False)

    class Serializer(serializers.Serializer):
        username = serializers.CharField()
        devices = serializers.JSONField()
        company_id = serializers.IntegerField()

    def get_serializer_class(self):
        return self.Serializer

    def get_queryset(self):
        qs = self.QueryString(data=parse_qs(self.request.GET))
        qs.is_valid(raise_exception=True)
        qs = qs.validated_data

        filters = []
        if qs.get('company'):
            filters.append(Q(company=qs['company']))

        return User.objects.filter(*filters)
