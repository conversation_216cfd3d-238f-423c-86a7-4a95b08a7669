from rest_framework import serializers
from rest_framework.views import APIView
from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response
from companies.models.models import Company
from logger.models import SmsLog
from logger.serializers import SmsLogSerializer
from picking.models import PickOrder

from sms.services.messaging import MessagingService


class AdminSendSMSAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        mobile = serializers.CharField(max_length=20)
        text = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        data = validator.validated_data
        service = MessagingService(company=data["company"])
        msg_log = service.send_sms(to=data["mobile"], text=data["text"], remark="A0000")
        serializer = SmsLogSerializer(msg_log)
        return Response(serializer.data)


class AdminSendLONAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        template_name = serializers.CharField(default="UPDATE_ORDER_STATUS")
        mobile = serializers.CharField(max_length=20)
        order_number = serializers.CharField()
        lon_data = serializers.JSONField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        data = validator.validated_data
        company: Company = data["company"]
        order_number = data.get("order_number")

        pick_order = PickOrder.get_by_number(company=company, number=order_number)
        if not pick_order:
            return Response({"error": "Order not found, please check order_number and company"}, status=404)

        params = {
            "seller": pick_order.order_saleschannel,
            "order_number": pick_order.order_number,
            "logistics": pick_order.order_saleschannel,
            "tracking_no": pick_order.order_trackingno,
            "delivery_date": "N/A",
            "remark": "",
            "check_vdo_link": pick_order.receipt_url,
            "more_details_link": "",
            **data["lon_data"]
        }

        if not params['check_vdo_link']:
            params.pop('check_vdo_link')
        if not params['more_details_link']:
            params.pop('more_details_link')

        lon_data = [dict(param=key, value=params[key]) for key in params]

        service = MessagingService(company=data["company"])
        msg_log = service.send_lon_message(
            mobile=data["mobile"],
            template_name=data['template_name'],
            data=lon_data
        )

        serializer = SmsLogSerializer(msg_log)
        return Response(serializer.data)
