from django.db import transaction
from datetime import datetime
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import TokenAuthentication
from rest_framework import serializers
from cloudtasks.tasks import (
    create_predict_order_level_task,
    create_vrich_checkout_task,
    create_webhook_task,
    create_zort_update_order_status_task,
)

from companies.models.models import Company
from logger.models import VideoRecordLog
from picking.models import PickOrder
from wallets.models import (
    RecordPackage,
    RecordTransaction,
    RecordTransactionType,
    TransactionType,
    Wallet,
)
from django.db.models import F, Sum
from django.utils import timezone


class UpdateSmsSenderNameAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        old_sms_sender = serializers.CharField()
        new_sms_sender = serializers.CharField()
        dryrun = serializers.BooleanField(default=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        old_sms_sender = validator.validated_data["old_sms_sender"]
        new_sms_sender = validator.validated_data["new_sms_sender"]
        dryrun = validator.validated_data["dryrun"]

        companies = Company.objects.all()

        resultlist = []

        for company in companies:
            sms_sender = company.get_setting("SMS_SENDER")
            if sms_sender == old_sms_sender:
                if dryrun:
                    print(
                        "changing company: %s, old_sms_sender: %s, new_sms_sender: %s"
                        % (company.name, old_sms_sender, new_sms_sender)
                    )
                else:
                    company.set_setting("SMS_SENDER", new_sms_sender)
                    company.save()

                resultlist.append(
                    {
                        "id": company.id,
                        "name": company.name,
                        "sms_sender": company.get_setting("SMS_SENDER"),
                    }
                )

        return Response(resultlist)


class ManualCheckoutVRichAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        ready_to_ship_timestamp_from = serializers.DateTimeField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        company = validator.validated_data["company"]
        date_from = validator.validated_data["ready_to_ship_timestamp_from"]

        pick_orders = PickOrder.objects.filter(
            company=company,
            ready_to_ship_timestamp__gte=date_from,
            ready_to_ship=True,
            order_oms="vrich",
        )

        for pick_order in pick_orders:
            create_vrich_checkout_task(
                company_id=company.id, order_number=pick_order.order_number
            )

        return Response(
            {
                "message": "create create_vrich_checkout_task %s tasks"
                % len(pick_orders),
            }
        )


class ManualZortUpdateOrderStatusAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        ready_to_ship_timestamp_from = serializers.DateTimeField()
        order_json__status = serializers.CharField(required=False)
        dry_run = serializers.BooleanField(default=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        company = validator.validated_data["company"]
        data = validator.validated_data

        query = {
            "company": company,
            "ready_to_ship_timestamp__gte": data["ready_to_ship_timestamp_from"],
            "ready_to_ship": True,
        }
        if data.get("order_json__status"):
            query["order_json__status"] = data.get("order_json__status")

        pick_orders = PickOrder.objects.filter(**query)

        for pick_order in pick_orders:
            if data["dry_run"]:
                pass
                print("create_zort_update_order_status_task: ", pick_order.order_number)
            else:
                create_zort_update_order_status_task(pick_order=pick_order)

        return Response(
            {
                "message": "create_zort_update_order_status_task for %s orders"
                % len(pick_orders),
            }
        )


class ManualSendPostRecordWebhookAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        upload_date_from = serializers.DateTimeField()
        upload_date_to = serializers.DateTimeField()
        dry_run = serializers.BooleanField(default=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        company = validator.validated_data["company"]
        data = validator.validated_data

        vlogs = VideoRecordLog.objects.filter(
            company=company,
            upload_date__gte=data["upload_date_from"],
            upload_date__lte=data["upload_date_to"],
        )

        results = []
        for vlog in vlogs:
            if data["dry_run"]:
                print("create_webhook_task: ", vlog.id)
            else:
                create_webhook_task(
                    webhook_type="post-record-webhook",
                    payload={
                        "company": company.id,
                        "video_record_log": vlog.id,
                    },
                )
            results.append("sending post-record-webhook for %s" % vlog.id)

        return Response({"total": len(results), "details": results})

class TransferCreditAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        from_company = serializers.PrimaryKeyRelatedField(
            queryset=Company.objects.all()
        )
        to_company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        amount = serializers.DecimalField(max_digits=10, decimal_places=2)

    @transaction.atomic
    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        from_company: Company = data["from_company"]
        to_company: Company = data["to_company"]
        amount: int = data["amount"]

        from_wallet: Wallet = from_company.wallet
        to_wallet: Wallet = to_company.wallet

        tx: RecordTransaction = RecordTransaction.add_transaction(
            from_wallet,
            type=RecordTransactionType.TRANSFER,
            value=-amount,
            create_by=request.user,
            date=datetime.now().date(),
            description=f"Transfer to {to_company.name}",
        )

        if from_wallet.version == 2:
            pkg = from_wallet.get_fefo_record_package()

            RecordPackage.objects.select_for_update().filter(id=pkg.id).update(
                realtime_balance=F("realtime_balance") - amount
            )

        tx = RecordTransaction.add_transaction(
            to_wallet,
            type=RecordTransactionType.TRANSFER,
            value=amount,
            create_by=request.user,
            date=datetime.now().date(),
            description=f"Receive from {from_company.name}",
        )
        if to_wallet.version == 2:
            # pkg = to_wallet.get_fefo_record_package()

            # RecordPackage.objects.select_for_update().filter(id=pkg.id).update(
            #     balance=F("balance") + amount,
            #     realtime_balance=F("realtime_balance") + amount
            # )
            if from_wallet.version == 2:
                expire_date = from_wallet.get_fefo_record_package().expire_date
            else:
                expire_date = timezone.now() + timezone.timedelta(days=365 * 2)

            # Create new RecordPackage
            RecordPackage.objects.create(
                wallet=to_wallet,
                name=f"Transfer from {from_company.name}",
                package_detail={"record_amount": float(amount)},
                order_number="-",
                balance=amount,
                realtime_balance=amount,
                is_main=False,
                is_active=True,
                create_date=timezone.now(),
                expire_date=expire_date,
            )

        return Response(
            {
                "message": "Transfer success",
                "from_company": {
                    "name": from_company.name,
                    "realtime_record_balance": from_wallet.get_realtime_record_balance(),
                    "record_balance": from_wallet.get_record_balance(),
                },
                "to_company": {
                    "name": to_company.name,
                    "realtime_record_balance": to_wallet.get_realtime_record_balance(),
                    "record_balance": to_wallet.get_record_balance(),
                },
            }
        )


class ManualBKPPredictOrderLevelAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        order_date_from = serializers.DateField()
        order_date_to = serializers.DateField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        company = validator.validated_data["company"]
        data = validator.validated_data

        query = {
            "company": company,
            "order_date__gte": data["order_date_from"],
            "order_date__lte": data["order_date_to"],
        }

        pick_orders = PickOrder.objects.filter(**query)

        for pick_order in pick_orders:
            create_predict_order_level_task(pick_order=pick_order)

        return Response(
            {
                "message": f"create_predict_order_level_task for {len(pick_orders)} orders"
            }
        )
