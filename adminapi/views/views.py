from django.utils.decorators import method_decorator
from django.db import transaction
from django.shortcuts import render
from dynamic_rest.viewsets import DynamicModelViewSet
from adminapi.serializers import (
    AdminCompanySerializer,
    AdminGoogleDriveTransactionSerializer,
    AdminPickOrderSerializer,
    AdminProductSetSerializer,
    AdminRecordTransactionSerializer,
    AdminSmsTransactionSerializer,
    AdminUserSerializer,
    AdminWalletSerializer,
    AdminVideoRecordLogSerializer,
)
from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import (
    TokenAuthentication,
    SessionAuthentication,
    BasicAuthentication,
)
from companies.models import Company
from companies.models.models import SettingValue
from rest_framework.response import Response
from rest_framework.status import HTTP_405_METHOD_NOT_ALLOWED, HTTP_201_CREATED
from logger.models import VideoRecordLog
from picking.models import PickOrder, ProductSet
from users.models import User
from django.contrib.auth.models import Group, Permission
from wallets.models import (
    GoogleDriveTransaction,
    RecordTransaction,
    SmsTransaction,
    Wallet,
)
from rest_framework.authtoken.models import Token


class AdminCompanyViewset(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminCompanySerializer

    def get_queryset(self, queryset=None):
        return Company.objects.all()

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AdminWalletViewset(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminWalletSerializer

    def get_queryset(self, queryset=None):
        return Wallet.objects.all()

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


@method_decorator(transaction.atomic, "dispatch")
class AdminUserViewset(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminUserSerializer

    def get_queryset(self, queryset=None):
        return User.objects.exclude(is_superuser=True).all()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = self.perform_create(serializer)
        serializer = self.get_serializer(instance)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=HTTP_201_CREATED, headers=headers)

    def perform_create(self, serializer):
        data = {**serializer.validated_data}
        is_owner = data.pop("is_owner")
        create_token = data.pop("create_token")

        user = User.objects.create_user(**data)
        if is_owner:
            group_owner = Group.objects.get(name="owner")
            user.groups.add(group_owner)

        if create_token:
            Token.objects.create(user=user)

        return user

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AdminSmsTransactionViewset(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminSmsTransactionSerializer

    def get_queryset(self, queryset=None):
        return SmsTransaction.objects.all()

    def create(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AdminRecordTransactionViewset(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminRecordTransactionSerializer

    def get_queryset(self, queryset=None):
        return RecordTransaction.objects.all()

    def create(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AdminGoogleDriveTransactionViewset(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminGoogleDriveTransactionSerializer

    def get_queryset(self, queryset=None):
        return GoogleDriveTransaction.objects.all()

    def create(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AdminPickOrderViewSet(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminPickOrderSerializer

    def get_queryset(self, queryset=None):
        return PickOrder.objects.all()

    def create(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AdminVideoRecordLogViewSet(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminVideoRecordLogSerializer

    def get_queryset(self, queryset=None):
        return VideoRecordLog.objects.all()

    def create(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)

    def destroy(self, request, *args, **kwargs):
        return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


# class Admin$Viewset(DynamicModelViewSet):
#     permission_classes = [IsAdminUser]
#     authentication_classes = [SessionAuthentication, TokenAuthentication]
#     serializer_class = $

#     def get_queryset(self, queryset=None):
#         return $.objects.all()

#     def destroy(self, request, *args, **kwargs):
#         return Response(status=HTTP_405_METHOD_NOT_ALLOWED)


class AdminProductSetViewSet(DynamicModelViewSet):
    permission_classes = [IsAdminUser]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    serializer_class = AdminProductSetSerializer

    def get_queryset(self, queryset=None):
        return ProductSet.objects.all()
