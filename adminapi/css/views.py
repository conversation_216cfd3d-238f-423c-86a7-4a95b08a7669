import random
import shutil
import string
from typing import <PERSON><PERSON>, TypedDict
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import serializers, status

from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import TokenAuthentication

from adminapi.serializers import AdminCompanySerializer
from companies.models.models import Company

from companies.serializers.serializers import CompanySerializer
from core.storages import default_public_storage as storage
from users.models import User
from wallets.models import (
    GoogleDrivePackage,
    RecordPackage,
    RecordTransaction,
    RecordTransactionType,
    Wallet,
)
from django.contrib.auth.models import Group, Permission
from django.utils import timezone
from django.db.models import Sum, FloatField, Value
from datetime import timedelta
from decimal import Decimal
from django.db.models.functions import Coalesce


def get_easy_password(length=8):
    chars = string.ascii_uppercase + string.digits
    return "".join(random.choice(chars) for _ in range(length))


class CompanyCreateAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.ModelSerializer):
        class Meta:
            model = Company
            fields = ["name", "account_suffix", "web_logo"]

        web_logo = serializers.FileField(required=False)

        def validate_account_suffix(self, value: str):
            if not value.startswith("@"):
                raise serializers.ValidationError("Account suffix must start with '@'")
            return value

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        company = Company.objects.create(
            name=vdata["name"],
            account_suffix=vdata["account_suffix"],
            is_active=False,
            is_setup=False,
        )
        Wallet.objects.create(
            company=company,
            version=2,
        )
        company.refresh_from_db()

        web_logo = vdata.get("web_logo")
        if web_logo:
            file_path = f"company/{company.id}/public/{web_logo.name}"
            with storage.open(file_path, "wb") as fp:
                file_obj = web_logo.file
                file_obj.seek(0)
                shutil.copyfileobj(file_obj, fp)

            file_url = storage.url(file_path)
            company.web_logo = file_url
            company.save()

        serializer = AdminCompanySerializer(company)
        return Response(serializer.data, status=201)


class RecordPackageCreateAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        name = serializers.CharField()
        balance = serializers.IntegerField()
        expire_date = serializers.DateField()
        package = serializers.ChoiceField(choices=["001", "002"])

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        company: Company = vdata["company"]
        if company.is_active:
            if company.package != vdata["package"]:
                return Response(
                    {
                        "package": f"Invalid Package ({company.package=}, package={vdata['package']})"
                    },
                    status=400,
                )

        if not company.is_active:
            company.package = vdata["package"]
            company.is_active = True
            company.is_setup = True
            company.save()

        wallet: Wallet = company.wallet
        if wallet.version == 1:
            raise NotImplementedError("TODO: Migrate wallet v1 to v2")

        pkg = RecordPackage.objects.create(
            wallet=company.wallet,
            name=vdata["name"],
            package_detail={},
            order_number="-",
            balance=vdata["balance"],
            realtime_balance=vdata["balance"],
            is_main=False,
            is_active=True,
            create_date=timezone.now(),
            expire_date=vdata["expire_date"],
        )

        last_balance = RecordTransaction.get_last_running_balance(wallet=wallet)
        RecordTransaction.objects.create(
            date=timezone.localdate(),
            type=RecordTransactionType.DEPOSIT.value,
            wallet=wallet,
            description=f"Topup {vdata['balance']:,.2f} record credit, by CSS system",
            value=vdata["balance"],
            running_balance=vdata["balance"] + last_balance,
            create_by=None,
            deduct_from=pkg,
        )

        company.refresh_from_db()
        serializer = AdminCompanySerializer(company)
        return Response(serializer.data)


class GoogleDrivePackageCreateAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        name = serializers.CharField()
        storage_amount_gb = serializers.IntegerField()
        expire_date = serializers.DateField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        company: Company = vdata["company"]
        wallet: Wallet = company.wallet

        GoogleDrivePackage.objects.create(
            wallet=wallet,
            name=vdata["name"],
            package_detail={},
            order_number="-",
            storage_amount_gb=vdata["storage_amount_gb"],
            is_main=False,
            is_active=True,
            create_date=timezone.now(),
            expire_date=vdata["expire_date"],
        )

        company.refresh_from_db()
        serializer = AdminCompanySerializer(company)
        return Response(serializer.data)


class CompanyAdminUserCreateAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        company: Company = vdata["company"]

        if User.objects.filter(username=f"admin{company.account_suffix}").exists():
            return Response(
                {"code": "ALREADY_EXISTED", "message": "Admin user already created"},
                status=400,
            )

        user, password = self.create_admin_user(company, username="admin")
        return Response({"username": user.username, "password": password})

    @staticmethod
    def create_admin_user(
        company: Company,
        username: str,
        password: str = None,
    ) -> Tuple[User, str]:
        if company.package == Company.PACKAGE_RECORD_ONLY:
            admin_group = Group.objects.get(name="AdminRecordOnly")
        elif company.package == Company.PACKAGE_FULL_INTEGRATION:
            admin_group = Group.objects.get(name="owner")
        else:
            raise NotImplementedError(f"Unknown company package: {company.package}")

        if not password:
            password = get_easy_password()

        user: User = User.objects.create_user(
            username=f"{username}{company.account_suffix}",
            password=password,
            is_superuser=False,
            is_staff=False,
            company=company,
            is_temporary=False,
        )

        perm_add_videorecordlog = Permission.objects.get(codename="add_videorecordlog")
        user.user_permissions.add(perm_add_videorecordlog)
        user.groups.set([admin_group])
        return user, password


class CompanyCreditForecastAPIView(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    def get(self, request):
        thirty_days_ago = timezone.now().date() - timedelta(days=30)

        companies = Company.objects.all()
        forecast_data = []

        for company in companies:
            current_credit = company.wallet.get_record_balance()

            total_deductions = RecordTransaction.objects.filter(
                wallet=company.wallet,
                type__in=[
                    RecordTransactionType.DAILY_DEDUCT.value,
                    RecordTransactionType.ETAX_DAILY_DEDUCT.value,
                    RecordTransactionType.ETAX_SMS_DAILY_DEDUCT.value,
                ],
                date__gte=thirty_days_ago,
            ).aggregate(
                total=Coalesce(Sum("value"), Value(0), output_field=FloatField())
            )[
                "total"
            ]

            avg_credit_usage = Decimal(str(abs(total_deductions) / 30))

            days_until_empty = (
                float(current_credit / avg_credit_usage) if avg_credit_usage > 0 else 0
            )

            forecast_data.append(
                {
                    "id": company.id,
                    "company_name": company.name,
                    "current_credit": float(current_credit),
                    "avg_credit_usage": round(float(avg_credit_usage), 2),
                    "days_until_credit_runs_out": round(days_until_empty, 1),
                }
            )

        return Response(forecast_data, status=status.HTTP_200_OK)


class SummaryUsed(TypedDict):
    wallet: int
    total_used: Decimal

    # ทำอีก step นึืงสำหรับ wallet v2
    balance: Decimal


class CompanyCreditForecastAPIView2(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    def get(self, request):
        thirty_days_ago = timezone.now().date() - timedelta(days=30)

        forecast_data = []
        summary_used_by_wallet: list[SummaryUsed] = (
            RecordTransaction.objects.filter(
                type__in=[
                    RecordTransactionType.DAILY_DEDUCT.value,
                    RecordTransactionType.ETAX_DAILY_DEDUCT.value,
                    RecordTransactionType.ETAX_SMS_DAILY_DEDUCT.value,
                ],
                date__gte=thirty_days_ago,
            )
            .values("wallet")
            .annotate(
                total_used=Coalesce(Sum("value"), Value(0), output_field=FloatField()),
            )
        )
        summary_used = {w["wallet"]: w for w in summary_used_by_wallet}

        wallets = Wallet.objects.filter(
            id__in=[x["wallet"] for x in summary_used_by_wallet]
        ).prefetch_related("company")
        map_wallets = {w.id: w for w in wallets}

        v2_result = (
            RecordTransaction.objects.filter(
                wallet_id__in=[w.id for w in wallets if w.version == 2]
            )
            .values("wallet")
            .annotate(balance=Sum("value"))
        )
        for v2 in v2_result:
            wallet = summary_used.get(v2["wallet"])
            wallet["balance"] = v2["balance"]

        for used in summary_used.values():
            wallet = map_wallets.get(used["wallet"])

            # get total balance
            if wallet.version == 2:
                current_credit = used["balance"]
            else:
                current_credit = wallet.record_balance

            avg_credit_usage = Decimal(str(abs(used["total_used"] or 0) / 30))

            days_until_empty = (
                float(current_credit / avg_credit_usage) if avg_credit_usage > 0 else 0
            )

            forecast_data.append(
                {
                    "id": wallet.company_id,
                    "company_name": wallet.company.name,
                    "current_credit": float(current_credit),
                    "avg_credit_usage": round(float(avg_credit_usage), 2),
                    "days_until_credit_runs_out": round(days_until_empty, 1),
                }
            )

        # order by days_until_empty
        forecast_data.sort(key=lambda x: x["days_until_credit_runs_out"])

        return Response(forecast_data, status=status.HTTP_200_OK)
