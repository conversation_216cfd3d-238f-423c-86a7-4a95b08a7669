from django.urls import path, include
from dynamic_rest.routers import DynamicRouter
from . import views
from .views import (
    admin_user_views,
    admin_company_views,
    admin_views,
    admin_messaging_views,
)

router = DynamicRouter()
router.register("v1/resource/company", views.AdminCompanyViewset)
router.register("v1/resource/wallet", views.AdminWalletViewset)
router.register("v1/resource/user", views.AdminUserViewset)
router.register("v1/resource/sms-transaction", views.AdminSmsTransactionViewset)
router.register("v1/resource/record-transaction", views.AdminRecordTransactionViewset)
router.register(
    "v1/resource/google-drive-transaction", views.AdminGoogleDriveTransactionViewset
)
router.register("v1/resource/pick-order", views.AdminPickOrderViewSet)
router.register("v1/resource/video-record-log", views.AdminVideoRecordLogViewSet)
router.register("v1/resource/product-set", views.AdminProductSetViewSet)


# path = api/adminapi/
urlpatterns = router.urls + [
    path("css/", include("adminapi.css.urls")),
    path("v1/users/device-status/", admin_user_views.UserDeviceStatusListAPI.as_view()),
    path("v1/company/register/", admin_company_views.CompanyRegisterAPI.as_view()),
    path("v1/company/topup/", admin_company_views.CompanyTopupAPI.as_view()),
    path("v1/messaging/send-sms/", admin_messaging_views.AdminSendSMSAPI.as_view()),
    path("v1/messaging/send-lon/", admin_messaging_views.AdminSendLONAPI.as_view()),
    # ad-hoc
    path("update-sms-sender-name/", admin_views.UpdateSmsSenderNameAPIView.as_view()),
    path("manual-checkout-vrich/", admin_views.ManualCheckoutVRichAPIView.as_view()),
    path(
        "manual-zort-update-order-status/",
        admin_views.ManualZortUpdateOrderStatusAPIView.as_view(),
    ),
    path(
        "manual-send-post-record-webhook/",
        admin_views.ManualSendPostRecordWebhookAPIView.as_view(),
    ),
    path(
        "transfer-credit/", admin_views.TransferCreditAPIView.as_view()
    ),
    path(
        "manual-bkp-predict-order-level/",
        admin_views.ManualBKPPredictOrderLevelAPIView.as_view(),
    ),
]
