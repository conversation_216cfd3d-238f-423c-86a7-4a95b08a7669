from datetime import datetime
from typing import List, Literal, Dict, Optional, Union, Any
from pydantic import BaseModel, Field, condecimal


class Company(BaseModel):
    dobybot_uuid: Optional[str] = None
    name: str
    account_suffix: str
    logo: str


class Customer(BaseModel):
    full_name: str
    email: str
    phone: str
    peak_contact: Any


PRODUCT_TYPE = Literal["main_package", "addon_package", "product", "entrance_fee", "shipping_fee"]
PACKAGE_TYPE = Literal["RECORD_ONLY", "RECORD_CONNECT", "RECORD_CONNECT_AUTOPRINT"]


class PackageFeature(BaseModel):
    record: bool
    order_center: bool
    pick_slip: bool
    airway_bill: bool
    messaging: bool


class MainPackage(BaseModel):
    sku: str
    name: str

    # number of days, use 30.5 for monthly
    duration_days: float
    package_type: PACKAGE_TYPE = "RECORD_ONLY"

    feature: Optional[PackageFeature]
    recurring: bool = False
    record_amount: int  # number of records
    storage_amount_gb: int
    price: Optional[float] = 0
    entrance_fee: Optional[float] = 0
    is_active: bool = Field(
        description="สถานะแพ็กเกจ (True=เปิดขาย, False=ไม่เปิดขาย)",
        default=True,
    )
    peak_service_id: str = ""


class AddonPackage(BaseModel):
    """
    แพ็กเกจเสริม
    เช่น เพิ่ม Google Drive 1 TB, ซื้อ record credit เพิ่ม 1000 credit
    """

    # ซื้อได้เฉพาะ main package ที่กำหนดไว้ ถ้าเป็น None ทุก main package สามารถซื้อได้
    only_for_package: Union[MainPackage, str]

    # number of days, use 30.5 for monthly
    duration_days: float

    sku: str
    name: str
    price: float
    recurring: bool = False
    record_amount: int
    storage_amount_gb: int
    image: str = ""
    description: str = ""
    is_active: bool = Field(
        description="สถานะแพ็กเกจ (True=เปิดขาย, False=ไม่เปิดขาย)",
        default=True,
    )
    peak_service_id: str = ""


class Product(BaseModel):
    """
    สินค้า / บริการครั้งเดียว
    เช่น กล้อง webcam, ขาตั้งกล้อง, บริการติดตั้ง, บริการทำ excel template, บริการทำ white list sms
    """

    sku: str = Field(description="รหัสสินค้า")
    name: str = Field(description="ชื่อสินค้า")
    price: float = Field(description="ราคา")
    description: str = Field(description="รายละเอียดสินค้า")
    category: str = Field(description="หมวดหมู่สินค้า")
    image_urls: List[str] = Field(
        description="รูปภาพสินค้า",
        example=["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
    )
    is_active: bool = Field(
        description="สถานะสินค้า (True=เปิดขาย, False=ไม่เปิดขาย)",
        default=True,
    )
    peak_product_id: str = ""


class PurchaseItem(BaseModel):
    sku: str
    name: str
    quantity: int
    price_per_unit: condecimal(max_digits=10, decimal_places=4)
    price: condecimal(max_digits=10, decimal_places=4)
    type: PRODUCT_TYPE
    detail: Union[MainPackage, AddonPackage, Product, None] = None
    peak_ref_id: Union[str, None] = ""


# ORDER_STATUS = Literal["creating", "created", "paid", "cancelled"]


# class StatusLog(BaseModel):
#     timestamp: datetime = Field(default_factory=datetime.utcnow)
#     status: ORDER_STATUS
#     message: str
#     user: str  # email


# class ShippingAddress(BaseModel):
#     name: str = ""
#     address: str = ""
#     district: str = ""
#     amphoe: str = ""
#     province: str = ""
#     zipcode: str = ""


# class BillingAddress(BaseModel):
#     company_name: str = ""
#     tax_number: str = ""
#     address: str = ""
#     district: str = ""
#     amphoe: str = ""
#     province: str = ""
#     zipcode: str = ""


class Order(BaseModel):
    company: Company
    purchase_items: List[PurchaseItem]
    order_number: str

    # customer: Union[Customer, str]
    # shipping_address: ShippingAddress = Field(default_factory=ShippingAddress)
    # billing_address: BillingAddress = Field(default_factory=BillingAddress)

    # status: ORDER_STATUS = "created"
    # status_logs: List[StatusLog] = []
    # order_date: datetime = Field(default_factory=datetime.utcnow)
    # total_price: condecimal(max_digits=10, decimal_places=4)
    # recurring_price: condecimal(max_digits=10, decimal_places=4) = 0
    # total_qty: int

    # create_by: str  # email
    # create_date: datetime = Field(default_factory=datetime.utcnow)
    # payment_link: str = ""

    # peak_receipt_link: str = ""
    # peak_contact_id: str = ""
