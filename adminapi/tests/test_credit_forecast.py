from django.test import TestCase
from django.test.utils import tag
from django.utils import timezone
from datetime import timedelta
from core.tests import setup
from core.tests.testutils import TestCaseHelper, TestClient
from wallets.models import RecordTransaction, RecordTransactionType


class CompanyCreditForecastTestCase(TestCase, TestCaseHelper):
    def setUp(self):
        self.maxDiff = None
        result = setup.init()
        self.company = result["company"]
        self.user = result["user"]
        self.client = TestClient()
        self.client.login_with_authtoken()

        # Make user an admin
        self.user.is_staff = True
        self.user.save()

    def get_credit_forecast(self):
        response = self.client.get("/api/adminapi/css/credit-forecast/")
        print(response)
        print(response.json())
        return response

    def create_transaction(self, days_ago: int, value: float):
        """Helper method to create test transactions"""
        date = timezone.now().date() - timedelta(days=days_ago)
        return RecordTransaction.objects.create(
            date=date,
            type=RecordTransactionType.DAILY_DEDUCT.value,
            wallet=self.company.wallet,
            description=f"Test deduction {days_ago} days ago",
            value=value,
            running_balance=0,
        )

    @tag("forecast")
    def test_credit_forecast_with_no_transactions(self):
        """Test forecast when there are no transactions"""

        response = self.get_credit_forecast()
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(len(data), 1)  # Only our test company
        self.assertEqual(data[0]["company_name"], self.company.name)
        self.assertEqual(data[0]["avg_credit_usage"], 0)
        self.assertEqual(data[0]["days_until_credit_runs_out"], 0)

    @tag("forecast")
    def test_credit_forecast_with_transactions(self):
        """Test forecast with some transactions"""
        # Create transactions for last 30 days
        for i in range(30):
            self.create_transaction(i, -100)  # -100 credit per day

        # Set current balance
        self.company.wallet.record_balance = 1000
        self.company.wallet.save()

        response = self.get_credit_forecast()
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(len(data), 1)
        forecast = data[0]

        self.assertEqual(forecast["current_credit"], 1000)
        self.assertEqual(forecast["avg_credit_usage"], 100)  # 100 per day
        self.assertEqual(
            forecast["days_until_credit_runs_out"], 10.0
        )  # 1000/100 = 10 days

    @tag("forecast")
    def test_credit_forecast_with_mixed_transaction_types(self):
        """Test forecast with different types of transactions"""
        # Create different types of transactions
        RecordTransaction.objects.create(
            date=timezone.now().date() - timedelta(days=1),
            type=RecordTransactionType.DAILY_DEDUCT.value,
            wallet=self.company.wallet,
            value=-50,
            running_balance=0,
            description="Daily deduction",
        )

        RecordTransaction.objects.create(
            date=timezone.now().date() - timedelta(days=1),
            type=RecordTransactionType.ETAX_DAILY_DEDUCT.value,
            wallet=self.company.wallet,
            value=-30,
            running_balance=0,
            description="ETAX deduction",
        )

        RecordTransaction.objects.create(
            date=timezone.now().date() - timedelta(days=1),
            type=RecordTransactionType.DEPOSIT.value,  # Should not be counted
            wallet=self.company.wallet,
            value=1000,
            running_balance=1000,
            description="Deposit",
        )

        self.company.wallet.record_balance = 800
        self.company.wallet.save()

        response = self.get_credit_forecast()
        self.assertEqual(response.status_code, 200)

        data = response.json()
        forecast = data[0]

        self.assertEqual(forecast["current_credit"], 800)
        self.assertEqual(
            forecast["avg_credit_usage"], 2.67
        )  # (50+30)/30 ≈ 2.67 per day
        self.assertEqual(
            forecast["days_until_credit_runs_out"], 300.0
        )  # 800/2.67 ≈ 299.6 days then round decimal to 300
