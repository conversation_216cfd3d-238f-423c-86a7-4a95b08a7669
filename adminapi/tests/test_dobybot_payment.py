import datetime
from decimal import Decimal
from django.test import TestCase, tag
from unittest import skipIf
import adminapi.schema.dobybot_payment as dbp

from core.tests.testutils import Test<PERSON>aseHelper, TestClient
from core.tests import setup
from rest_framework.authtoken.models import Token
from users.models import User
from companies.models import Company
from wallets.models import GoogleDrivePackage, RecordPackage, RecordTransaction, Wallet
from wallets.tests.models.dbb_payment_data import orders
from django.forms.models import model_to_dict
import copy

class DobybotPaymentRegisterCompanyTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        result = setup.init()
        self.company: Company = result['company']
        self.user: User = result['user']
        self.wallet: Wallet = result['wallet']

        self.client = TestClient()
        self.client.login_with_authtoken()

    @tag('register-company', '1')
    @skipIf(True, "this will create share drive")
    def test_register_company(self):
        order = orders[0]
        response = self.client.post('/api/adminapi/v1/company/register/', data=order, format='json')

        print(response.status_code)
        print(response.json())
        pass

    @tag('register-company')
    def test_topup_migrate_wallte_v1_to_v2(self):
        order = orders[0]
        order['company']['dobybot_uuid'] = self.company.uuid

        wallet: Wallet = self.company.wallet
        wallet.topup_record_balance(1000, self.user, 'test1')
        wallet.google_drive_limit = 100_000
        wallet.save()

        response = self.client.post('/api/adminapi/v1/company/topup/', data=order, format='json')
        self.assertEqual(response.status_code, 200)

        wallet.refresh_from_db()
        self.assertEqual(wallet.version, 2)
        self.assertEqual(wallet.get_google_drive_limit(), 2_000_000)
        self.assertEqual(wallet.get_record_balance(), 101_000)
        self.assertEqual(wallet.get_fefo_record_package().name, "Convert from wallet v1")

        self.assertEqual(RecordPackage.objects.filter(wallet=wallet).count(), 2)
        self.assertEqual(GoogleDrivePackage.objects.filter(wallet=wallet).count(), 1)

    @tag('register-company')
    def test_register_company2(self):
        order = copy.deepcopy(orders[0])
        response = self.client.post('/api/adminapi/v1/company/register/', data=order, format='json')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        company_id = data['company']['id']
        company: Company = Company.objects.get(id=company_id)
        wallet: Wallet = company.wallet

        pkg = wallet.get_fefo_record_package()

        tx = RecordTransaction.objects.filter(wallet=wallet).first()
        self.assertMatch(model_to_dict(tx), {
            'id': int,
            'date': datetime.date.today(),
            'type': '101',
            'wallet': int,
            'description': 'Topup 100,000.00 record credit, by system',
            'value': Decimal('100000.00'),
            'running_balance': Decimal('100000.00'),
            'create_by': None,
            'deduct_from': pkg.id
        })
        
    @tag('register-company')
    def test_no_package(self):
        order = copy.deepcopy(orders[0])
        order['purchase_items'] = [item for item in order['purchase_items'] if item['type'] not in ['main_package', 'addon_package']]
        response = self.client.post('/api/adminapi/v1/company/topup/', data=order, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['message'], 'Do nothing because not have main_package or addon_package in purchase_items')
        self.assertEqual(response.json()['status'], 'DO_NOTHING')
        # self.assertEqual(response.json(), {'record_package': ['This field may not be null.']})
        
    
