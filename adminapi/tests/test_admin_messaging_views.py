from django.test import TestCase
from django.test.utils import tag
from core.tests.testutils import Test<PERSON>ase<PERSON>elper, TestClient
from core.tests import setup
from unittest import skipIf

SEND_REAL_SMS = False

# python manage.py test --tag=


class TestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        self.user, self.company, self.wallet = setup.init().values()
        self.client = TestClient()
        self.client.login_with_authtoken()

    @tag("admin-sms")
    @skipIf(not SEND_REAL_SMS, "This will send real sms")
    def test_admin_send_sms(self):
        response = self.client.post(
            "/api/adminapi/v1/messaging/send-sms/",
            data={
                "company": self.company.id,
                "mobile": "0889519856",
                "text": "Hello World",
            },
            format="json",
        )

        print(response)
        print(response.json())

    @tag("admin-lon")
    @skipIf(not SEND_REAL_SMS, "This will send real sms")
    def test_admin_send_lon(self):
        pick_orders = setup.create_test_pickorders(self.company)

        response = self.client.post(
            "/api/adminapi/v1/messaging/send-lon/",
            data={
                "company": self.company.id,
                "mobile": "0889519856",
                "template_name": "UPDATE_ORDER_STATUS",
                "order_number": pick_orders[0].order_number,
                "lon_data": {
                    "order_number": "",
                    "check_vdo_link": None,
                    "more_details_link": None,
                },
            },
            format="json",
        )

        print(response)
        print(response.json())
