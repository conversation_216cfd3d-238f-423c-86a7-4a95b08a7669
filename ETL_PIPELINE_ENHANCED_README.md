# Enhanced ETL Pipeline with Deduplication & Schema Management

## 🎯 Solutions to Your Concerns

### 1. Single Source of Truth for BigQuery Schema ✅

**Problem**: Schema defined in multiple places causing maintenance issues.

**Solution**: Centralized schema management with `BigQuerySchemaManager` class.

#### How it works:
- **Single Definition**: All schemas defined in `etl/bigquery_schemas.py`
- **Version Control**: Schema versioning with `SCHEMA_VERSION`
- **Auto-Export**: Schemas exported to JSON for Cloud Function
- **Validation**: Commands to validate schema consistency

#### Adding New Fields:
```bash
# Add new field to orders table
python manage.py manage_bigquery_schema --add-field orders new_field STRING NULLABLE "Description"

# Validate schema consistency
python manage.py manage_bigquery_schema --validate-schema orders --company-id 1

# Export schemas for Cloud Function
python manage.py manage_bigquery_schema --export-schemas
```

### 2. Deduplication & Update Handling ✅

**Problem**: Order updates create duplicate data in BigQuery.

**Solution**: UPSERT operations using BigQuery MERGE statements.

#### How it works:
- **Record Hashing**: Each record gets a hash for change detection
- **UPSERT Logic**: MERGE statements update existing records or insert new ones
- **Deterministic IDs**: Order items get consistent UUIDs based on order data
- **Audit Fields**: Track ETL processing time and schema version

#### Key Features:
- ✅ No duplicate orders when data is updated
- ✅ Efficient updates using BigQuery MERGE
- ✅ Change tracking with record hashes
- ✅ Partitioned tables for better performance

## 🏗️ Enhanced Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Cloud Scheduler │───▶│ Django API       │───▶│ ETL Service V2  │
│ (Hourly)        │    │ Enhanced         │    │ (Deduplication) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ BigQuery Tables │◀───│ Cloud Function   │◀───│ GCS Bucket      │
│ (Partitioned)   │    │ (UPSERT Logic)   │    │ (CSV Files)     │
│ - orders        │    │                  │    │                 │
│ - order_items   │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📊 Enhanced BigQuery Schema

### Orders Table (with deduplication fields):
```sql
CREATE TABLE `project.dobybot_company_1.orders` (
  -- Existing fields...
  order_id INT64 NOT NULL,
  uuid STRING NOT NULL,
  company_id INT64 NOT NULL,
  -- ... all existing fields ...
  
  -- New audit fields
  etl_processed_at TIMESTAMP NOT NULL,
  etl_schema_version STRING NOT NULL,
  record_hash STRING NOT NULL,  -- For deduplication
)
PARTITION BY DATE(order_date)
CLUSTER BY company_id, order_date;
```

### Order Items Table (with deduplication fields):
```sql
CREATE TABLE `project.dobybot_company_1.order_items` (
  -- Existing fields...
  order_item_id STRING NOT NULL,  -- Deterministic UUID
  order_id INT64 NOT NULL,
  -- ... all existing fields ...
  
  -- New audit fields
  etl_processed_at TIMESTAMP NOT NULL,
  etl_schema_version STRING NOT NULL,
  record_hash STRING NOT NULL,  -- For deduplication
)
PARTITION BY DATE(order_date)
CLUSTER BY company_id, order_date, order_id;
```

## 🔧 Schema Management Commands

### Export Schemas
```bash
# Export current schemas to JSON files for Cloud Function
python manage.py manage_bigquery_schema --export-schemas
```

### Add New Field
```bash
# Add new field to orders table
python manage.py manage_bigquery_schema \
  --add-field orders customer_segment STRING NULLABLE "Customer segment classification" \
  --company-id 1

# Add new field to order_items table  
python manage.py manage_bigquery_schema \
  --add-field order_items profit_margin NUMERIC NULLABLE "Profit margin percentage" \
  --company-id 1
```

### Validate Schema
```bash
# Validate schema consistency between code and BigQuery
python manage.py manage_bigquery_schema --validate-schema orders --company-id 1
python manage.py manage_bigquery_schema --validate-schema order_items --company-id 1
```

## 🔄 Deduplication Process

### 1. Record Hash Calculation
```python
# Each record gets a hash excluding ETL fields
def calculate_record_hash(record_data):
    hash_data = {k: v for k, v in record_data.items() 
                if not k.startswith('etl_') and k != 'record_hash'}
    sorted_data = json.dumps(hash_data, sort_keys=True, default=str)
    return hashlib.sha256(sorted_data.encode()).hexdigest()
```

### 2. UPSERT Operation
```sql
-- BigQuery MERGE statement for deduplication
MERGE `project.dataset.orders` AS target
USING `project.dataset.orders_temp` AS source
ON target.order_id = source.order_id
WHEN MATCHED THEN
    UPDATE SET 
        order_customer = source.order_customer,
        order_total_price = source.order_total_price,
        -- ... all fields except order_id
WHEN NOT MATCHED THEN
    INSERT (order_id, uuid, company_id, ...)
    VALUES (source.order_id, source.uuid, source.company_id, ...)
```

## 🚀 Migration Guide

### Step 1: Update Services
```python
# Use enhanced services
from etl.services_v2 import ETLServiceV2

# In your views or management commands
etl_service = ETLServiceV2()
etl_service.run_etl_job(etl_job)
```

### Step 2: Update Cloud Function
```bash
# Deploy enhanced Cloud Function with UPSERT logic
gcloud functions deploy bigquery-loader \
    --runtime python39 \
    --trigger-bucket your-etl-bucket \
    --entry-point load_csv_to_bigquery \
    --source cloud_functions/bigquery_loader/
```

### Step 3: Migrate Existing Data
```bash
# Add new fields to existing tables
python manage.py manage_bigquery_schema \
  --add-field orders etl_schema_version STRING REQUIRED "Schema version" \
  --company-id 1

python manage.py manage_bigquery_schema \
  --add-field orders record_hash STRING REQUIRED "Record hash for deduplication" \
  --company-id 1
```

## 📈 Performance Improvements

### Table Partitioning
- **Partition by**: `order_date` (daily partitions)
- **Cluster by**: `company_id`, `order_date`, `order_id`
- **Benefits**: Faster queries, lower costs

### Query Optimization
```sql
-- Efficient queries using partitioning and clustering
SELECT *
FROM `project.dobybot_company_1.orders`
WHERE company_id = 1
  AND order_date >= '2024-01-01'
  AND order_date <= '2024-01-31'
```

## 🔍 Monitoring & Troubleshooting

### Check for Duplicates
```sql
-- Find potential duplicates by record_hash
SELECT record_hash, COUNT(*) as count
FROM `project.dobybot_company_1.orders`
GROUP BY record_hash
HAVING COUNT(*) > 1
```

### Schema Version Tracking
```sql
-- Check schema versions in use
SELECT 
  etl_schema_version,
  COUNT(*) as records,
  MIN(etl_processed_at) as first_processed,
  MAX(etl_processed_at) as last_processed
FROM `project.dobybot_company_1.orders`
GROUP BY etl_schema_version
ORDER BY last_processed DESC
```

### ETL Job Monitoring
```python
# Check recent ETL jobs
from etl.models import ETLJob

recent_jobs = ETLJob.objects.filter(
    company_id=1,
    status='completed'
).order_by('-completed_at')[:10]

for job in recent_jobs:
    print(f"{job.uuid}: {job.total_orders} orders, {job.total_order_items} items")
```

## 🎯 Best Practices

### 1. Schema Changes
- Always use management commands for schema changes
- Test schema changes in development first
- Export schemas after changes
- Validate consistency across environments

### 2. Data Quality
- Monitor record hashes for unexpected changes
- Set up alerts for failed ETL jobs
- Regular data validation queries
- Backup before major schema changes

### 3. Performance
- Use date range filters in queries
- Leverage partitioning and clustering
- Monitor BigQuery slot usage
- Optimize CSV file sizes (aim for 100MB-1GB per file)

This enhanced ETL pipeline solves both of your concerns:
1. ✅ **Single source of truth** for schemas with centralized management
2. ✅ **No duplicate data** with UPSERT operations and deduplication

The system is now production-ready with proper schema versioning, deduplication, and performance optimizations!
