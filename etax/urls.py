from django.urls import path
from . import views

# /api/etax/

urlpatterns = [
    path("webhook/", views.D1AsiaWebhookHandlerAPI.as_view()),
    # Public
    path("get-etax/", views.GetLastestTaxDocumentAPIView.as_view()),
    path("create/", views.TaxDocumentCreateAPIView.as_view()),
    path("update/", views.TaxDocumentUpdateAPIView.as_view()),
    path("get-company-data/", views.GetCompanyFullDataAPIView.as_view()),
    path("get-etax-detail/", views.TaxDocumentGetDetailAPIView.as_view()),

    path("easy-order-from-token/", views.EasyOrderFromTokenAPIView.as_view()),
    path("easy-order/token/", views.EasyOrderFromToken2APIView.as_view()),

    path("get-etax-company-setting/<str:cid>/", views.ETaxGetCompanySetting.as_view()),
    path("create-document/", views.OrderTaxInvoiceAPIView.as_view()),

    path("download/check/", views.CheckDownloadTaxDocumentAPIView.as_view()),
    path("download/", views.DownloadTaxDocumentAPIView.as_view()),

    # Private
    path("cancel/", views.TaxDocumentCancelAPIView.as_view()),
    path("send-link/", views.SendETaxLinkViaSMSAPIView.as_view()),
    path(
        "easy-orders/check-etax/<str:order_number>/",
        views.OrderHasETaxCheckAPIView.as_view(),
    ),
    # Auto ETax
    path("auto-etax/tasks/handler/", views.AutoETaxTaskHandlerAPIView.as_view()),
    path("credit-note/create/", views.TaxDocumentCreditNoteCreateAPIView.as_view()),
    # path("credit-note/cancel/", views.CreditNoteCancelAPIView.as_view()),
    path("receipt/create/", views.TaxDocumentReceiptCreateAPIView.as_view()),
    path("receipt/update/", views.TaxDocumentReceiptUpdateAPIView.as_view()),
    path("receipt/cancel/", views.TaxDocumentReceiptCancelAPIView.as_view()),
    path('cancel-and-renew/tasks/handler/', views.AutoCancelAndRenewETaxTaskAPIView.as_view()),

    # Cloud Scheduler
    path("recheck-cancel-order-etax/", views.AutoCancelETaxTaskSchedulerAPI.as_view()),

    # Debugger
    path("monitor/", views.MonitorETaxDocumentAPIView.as_view()),
    path("debugger/", views.MonitorETaxDocumentAPIView.as_view()),
    path("debugger/retry/", views.EtaxRetryAPIView.as_view()),
    path("debugger/cancel/", views.EtaxCancelAPIView.as_view()),
    path("debugger/update/", views.EtaxUpdateAPIView.as_view()),
    path("debugger/cancel-and-renew/", views.EtaxCancelAndRenewAPIView.as_view()),
]
