from collections import OrderedDict
from rest_framework import serializers

from companies.models.models import Company
from core.serializers.serializers import KCharField
from etax.models import TaxDocument


class TaxDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = TaxDocument
        fields = [
            "id",
            "uuid",
            "order_number",
            "create_date",
            "status",
            "status",
            # "doc_info",
            "doc_url",
            "doc_type",
            "edit_count",
            "buyer",
            "is_consent_marketing",
        ]


class TaxDocumentMonitorSerializer(serializers.ModelSerializer):
    company_name = serializers.CharField(source="company.name")
    doc_info2 = serializers.JSONField()
    status_reason2 = serializers.JSONField()

    class Meta:
        model = TaxDocument
        # fields = "__all__"
        exclude = [
            "pick_order",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)

        if "doc_info" in data and isinstance(data["doc_info"], dict):
            data["doc_info"] = OrderedDict(sorted(data["doc_info"].items()))

            items = []
            for item in data["doc_info"]["items"]:

                items.append(OrderedDict(sorted(item.items())))

            data["doc_info"]["items"] = items

        return data


class GetTaxDocumentSerializer(serializers.Serializer):

    order_number = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    order_uuid = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    company_uuid = serializers.CharField()
    tax_document_uuid = serializers.CharField(
        allow_blank=False,
        allow_null=True,
        required=False,
    )


class TaxDocumentBuyerInfoSerializer(serializers.Serializer):

    order_number = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    order_uuid = serializers.CharField(
        allow_blank=False,
        allow_null=True,
        required=False,
    )
    company_uuid = serializers.CharField()
    tax_document_uuid = serializers.CharField(
        allow_blank=False,
        allow_null=True,
        required=False,
    )

    # Personal info fields

    buyer_name = serializers.CharField(required=False, allow_blank=True)
    tax_id = serializers.CharField(required=False, allow_blank=True)
    address = serializers.CharField(required=False, allow_blank=True)
    email = serializers.CharField(required=False, allow_blank=True)
    phone_number = serializers.CharField(required=False, allow_blank=True)

    # Company info fields
    company_name = serializers.CharField(required=False, allow_blank=True)
    branch_name = serializers.CharField(required=False, allow_blank=True)
    branch_code = serializers.CharField(required=False, allow_blank=True)

    is_personal = serializers.BooleanField(default=None)

    def validate(self, data):
        personal_fields = [
            data.get("buyer_name", "").strip(),
            data.get("tax_id", "").strip(),
            data.get("address", "").strip(),
            data.get("email", "").strip(),
            data.get("phone_number", "").strip(),
        ]

        # Count how many personal fields are filled
        filled_personal_fields = len([field for field in personal_fields if field])

        # If all 5 personal fields are filled, it's personal data
        data["is_personal"] = filled_personal_fields == 5

        return data


class CompanyBuyerSerializer(serializers.Serializer):
    order_number = serializers.CharField(
        allow_blank=True,  # Don't allow empty strings
        allow_null=True,  # Allow null
        required=False,
    )
    order_uuid = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    company_uuid = serializers.CharField()
    tax_document_uuid = serializers.CharField(
        allow_blank=False,  # Don't allow empty strings
        allow_null=True,  # Allow null
        required=False,  # Make it optional
    )

    # Personal info fields
    buyer_name = KCharField(required=False, allow_blank=True)
    tax_id = KCharField(required=False, allow_blank=True)
    address = KCharField(required=False, allow_blank=True)
    email = KCharField(required=False, allow_blank=True)
    phone_number = KCharField(required=False, allow_blank=True)
    company_name = KCharField(required=False, allow_blank=True)
    branch_name = KCharField(required=False, allow_blank=True)
    branch_code = KCharField(required=False, allow_blank=True)
    is_consent_marketing = serializers.BooleanField(required=False)
    create_by = KCharField(required=False, allow_blank=True)
    post_code = KCharField(required=False, allow_blank=True)


# class CompanyUUIDKeyRelatedField(serializers.PrimaryKeyRelatedField):
#     def to_internal_value(self, data):
#         company = Company.objects.filter(uuid=data).first()

#         if not company:
#             self.fail("does_not_exist", pk_value=data)

#         return company


class PersonalBuyerSerializer(serializers.Serializer):
    order_number = serializers.CharField(
        allow_blank=True,  # Don't allow empty strings
        allow_null=True,  # Allow null values
        required=False,
    )
    order_uuid = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )

    company_uuid = serializers.CharField()
    tax_document_uuid = serializers.CharField(
        allow_blank=False,  # Don't allow empty strings
        allow_null=True,  # Allow null values
        required=False,  # Make it optional
    )
    buyer_name = KCharField(required=False, allow_blank=True)
    tax_id = KCharField(required=False, allow_blank=True)
    address = KCharField(required=False, allow_blank=True)
    email = KCharField(required=False, allow_blank=True)
    phone_number = KCharField(required=False, allow_blank=True)
    is_consent_marketing = serializers.BooleanField(required=False, allow_null=True)
    create_by = KCharField(required=False, allow_blank=True)
    post_code = KCharField(required=False, allow_blank=True)


class TaxInfoSerializer(serializers.Serializer):
    taxId = serializers.CharField(
        max_length=13,
        required=True,
        error_messages={
            "required": "Tax ID is required",
            "max_length": "Tax ID must not exceed 13 characters",
        },
    )
    name = serializers.CharField(
        max_length=255,
        required=True,
        error_messages={"required": "Company name is required"},
    )
    branch = serializers.CharField(max_length=255, allow_blank=True, required=False)
    branchCode = serializers.CharField(max_length=5, allow_blank=True, required=False)
    zipCode = serializers.CharField(
        max_length=5,
        required=True,
        error_messages={
            "required": "Zip code is required",
            "max_length": "Zip code must not exceed 5 characters",
        },
    )
    addressLocal = serializers.CharField()
    contactGroup = serializers.IntegerField(
        required=True,
        min_value=1,
        error_messages={
            "required": "Contact group is required",
            "min_value": "Contact group must be a positive integer",
        },
    )


class TaxInfoListSerializer(serializers.Serializer):
    results = TaxInfoSerializer(many=True)
