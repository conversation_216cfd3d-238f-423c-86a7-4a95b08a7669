from rest_framework import serializers
from etax.error_prevention.utils import eq, field_detail_and_desc, get_field_description
from etax.error_prevention.order_json_error_prevention import not_zero, positive_number


class D1A_NONVAT_RowCheckSerializer(serializers.Serializer):

    # ลำดับรายการ
    TRADE_LINE_ID = serializers.IntegerField()
    # รหัสสินค้า
    PRODUCT_ID = serializers.Char<PERSON>ield(max_length=35)
    # ชื่อสินค้า
    PRODUCT_NAME = serializers.Char<PERSON>ield()
    # ราคาต่อหน่วย
    PRODUCT_CHARGEAMT = serializers.FloatField(validators=[positive_number])
    # จํานวนสินค้า
    PRODUCT_BILLED_QTY = serializers.FloatField(validators=[positive_number, not_zero])
    # มูลค่าส่วนลดหรือค่าธรรมเนียม ต่อรายการ
    PRODUCT_ACTUALAMT = serializers.FloatField()
    # มูลค่าสินค้าที่นํามาคิดภาษี (ไม่รวมภาษี)
    PRODUCT_TAX_BASISAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าภาษีเฉพาะรายการสินค้านั้นๆ
    PRODUCT_TAX_CALAMT = serializers.Float<PERSON>ield(validators=[positive_number])
    # ภาษีมูลค่าเพิ่ม
    PRODUCT_SUM_TAXTOTALAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าสินค้ารวม (ไม่รวมภาษี)
    PRODUCT_SUM_NETLINE_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าสินค้ารวม (รวมภาษีแล้ว)
    PRODUCT_SUM_TOTALAMT = serializers.FloatField(validators=[positive_number])

    def validate(self, attrs):
        product_chargeamt = attrs["PRODUCT_CHARGEAMT"]
        product_billed_qty = attrs["PRODUCT_BILLED_QTY"]
        product_actualamt = attrs["PRODUCT_ACTUALAMT"]
        product_sum_netline_totalamt = attrs["PRODUCT_SUM_NETLINE_TOTALAMT"]
        product_sum_totalamt = attrs["PRODUCT_SUM_TOTALAMT"]

        errors = []

        # PRODUCT_SUM_NETLINE_TOTALAMT
        if not eq(
            (product_chargeamt * product_billed_qty) - product_actualamt,
            product_sum_netline_totalamt,
        ):
            errors.append(
                {
                    "PRODUCT_SUM_NETLINE_TOTALAMT": "(PRODUCT_CHARGEAMT * PRODUCT_BILLED_QTY)  - PRODUCT_ACTUALAMT != PRODUCT_SUM_NETLINE_TOTALAMT",
                    **field_detail_and_desc(
                        attrs,
                        [
                            "PRODUCT_CHARGEAMT",
                            "PRODUCT_BILLIED_QTY",
                            "PRODUCT_ACTUALAMT",
                            "PRODUCT_SUM_NETLINE_TOTALAMT",
                        ],
                    ),
                },
            )

        if errors:
            raise serializers.ValidationError(errors)

        return attrs


class D1A_NONVAT_CheckSerializer(serializers.Serializer):

    # 2 Exchanged Document หัวเรื่องเอกสาร

    # เลขที่เอกสาร
    DOC_ID = serializers.CharField(max_length=35)

    # ชื่อเอกสาร
    DOC_NAME = serializers.CharField(max_length=35)

    # รหัสประเภทเอกสาร
    DOC_TYPE = serializers.CharField()

    # วันเดือนปี ที่ออกเอกสาร
    DOC_ISSUE_DATE = serializers.DateTimeField()

    # วันเดือนปีและเวลาที่สร้างเอกสาร
    DOC_CREATE_DATE = serializers.DateTimeField()

    # --------------------------------------------------------

    # 3 SupplyChainTrade Transaction ธุรกรรมทางการค้า
    # 3.1 ApplicableHeaderTradeAgreement ข้อตกลงทางการค้า
    # 3.1.1 SellerTradeParty ผู้ขาย
    # ชื่อผู้ขาย
    SELLER_NAME = serializers.CharField(max_length=255)

    # ******* SpecifiedTaxRegistration ข้อมูลการลงทะเบียนผู้เสียภาษีอากร

    # เลขประจำตัวผู้เสียภาษีอากร
    SELLER_TAX_ID = serializers.CharField(max_length=35)
    SELLER_TYPE_TAX = serializers.CharField()
    SELLER_BRANCH = serializers.CharField()
    SELLER_BRANCH_NAME = serializers.CharField()

    # ******* PostalTradeAddress ที่อยู่ (ผู้ขาย)

    # รหัสไปรษณีย์ (ผู้ขาย)
    SELLER_POSTCODE = serializers.CharField(max_length=16)

    # ที่อยู่บรรทัดที่ 1 (ผู้ขาย)
    SELLER_ADDRESS = serializers.CharField(max_length=255)

    # รหัสประเทศ (ผู้ขาย)
    SELLER_COUNTRY_ID = serializers.CharField(max_length=255)

    # 3.1.2 BuyerTradeParty ผู้ซื้อ
    # ชื่อผู้ซื้อ
    BUYER_NAME = serializers.CharField(max_length=255)

    # ******* SpecifiedTaxRegistration ข้อมูลการลงทะเบียนผู้เสียภาษีอากร

    # เลขประจำตัวผู้เสียภาษีอากร
    BUYER_TAX_ID = serializers.CharField(max_length=35)
    BUYER_TYPE_TAX = serializers.CharField()
    BUYER_BRANCH = serializers.CharField(allow_blank=True)
    BUYER_BRANCH_NAME = serializers.CharField(allow_blank=True)

    # ******* DefinedTradeContact รายละเอียดการติดต่อ
    # *******.3 EmailURIUniversal Communication การติดต่อทางอีเมล
    # อีเมล
    BUYER_CONTACT_EMAIL = serializers.CharField(max_length=255, allow_blank=True)

    # ******* PostalTradeAddress ที่อยู่ (ผู้ซื้อ)
    # รหัสไปรษณีย์ (ผู้ซื้อ)
    BUYER_POSTCODE = serializers.CharField(max_length=16)

    # ที่อยู่บรรทัดที่ 1 (ผู้ซื้อ)
    BUYER_ADDRESS = serializers.CharField(max_length=255)

    # รหัสประเทศ (ผู้ซื้อ)
    BUYER_COUNTRY_ID = serializers.CharField(max_length=255)

    # 3.3 ApplicableHeaderTradeSettlement ข้อมูลการชำระเงินทางการค้า
    # 3.3.2 ApplicableTradeTax ข้อมูลภาษี
    # รหัสประเภทภาษี
    TAX_TYPE_CODE = serializers.CharField()
    # อัตราภาษี
    TAX_CALCULATED_RATE = serializers.CharField()

    # 3.3.5 SpecifiedTradeSettlementHeaderMonetarySummation การสรุปมูลค่าการชำระเงินทาง
    # มูลค่าสินค้า/บริการ (ไม่รวมภาษีมูลค่าเพิ่ม ทศนิยม 2 ตำแหน่ง)
    TAX_BASIS_AMOUNT = serializers.FloatField(validators=[positive_number])
    # มูลค่าภาษีมูลค่าเพิ่ม
    TAX_CALCULATED_AMOUNT = serializers.FloatField(validators=[positive_number])
    # รวมมูลคาตามรายการ/มูลค่าที่ถูกต้อง
    MONEY_LINE_TOTALAMOUNT = serializers.FloatField(validators=[positive_number])
    # ส่วนลดท้ายบิล
    ALLOWANCE_SP_ACTUAL_AMOUNT = serializers.FloatField()
    # ส่วนลดทั้งหมด
    MONEY_ALLOWANCE_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าที่นำมาคิดภาษีมูลค่าเพิ่ม
    MONEY_TAXBASIS_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # จำนวนภาษีมูลค่าเพิ่ม (รวมทศนิยม 2 ต าแหน่ง)
    MONEY_TAX_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # ยอดเงินรวม/ ยอดเงินรวม ภาษีมูลค่าเพิ่ม
    MONEY_GRAND_TOTALAMT = serializers.FloatField(validators=[positive_number, not_zero])

    # ข้อมูลสินค้า
    items = D1A_NONVAT_RowCheckSerializer(many=True)

    def validate(self, attrs):
        product_items = attrs["items"]
        allowance_sp_actual_amount = attrs["ALLOWANCE_SP_ACTUAL_AMOUNT"]
        money_allowance_totalamt = attrs["MONEY_ALLOWANCE_TOTALAMT"]
        money_grand_total_amount = attrs["MONEY_GRAND_TOTALAMT"]
        sum_product_totalamt = sum([p["PRODUCT_SUM_TOTALAMT"] for p in product_items])
        sum_product_discount = sum([p["PRODUCT_ACTUALAMT"] for p in product_items])
        attrs["SUM_PRODUCT_TOTALAMT"] = sum_product_totalamt
        attrs["SUM_PRODUCT_DISCOUNT"] = sum_product_discount

        errors = []

        if not eq(sum_product_totalamt - allowance_sp_actual_amount,  money_grand_total_amount):
            errors.append(
                {
                    "MONEY_GRAND_TOTALAMT": "SUM_PRODUCT_TOTALAMT - ALLOWANCE_SP_ACTUAL_AMOUNT != MONEY_GRAND_TOTALAMT",
                    **field_detail_and_desc(
                        attrs,
                        [
                            "SUM_PRODUCT_TOTALAMT",
                            "ALLOWANCE_SP_ACTUAL_AMOUNT",
                            "MONEY_GRAND_TOTALAMT",
                        ],
                    ),
                },
            )

        if not eq(sum_product_discount + allowance_sp_actual_amount,  money_allowance_totalamt):
            errors.append(
                {
                    "MONEY_ALLOWANCE_TOTALAMT": "SUM_PRODUCT_DISCOUNT + ALLOWANCE_SP_ACTUAL_AMOUNT != MONEY_GRAND_TOTALAMT",
                    **field_detail_and_desc(
                        attrs,
                        [
                            "SUM_PRODUCT_DISCOUNT",
                            "ALLOWANCE_SP_ACTUAL_AMOUNT",
                            "MONEY_GRAND_TOTALAMT",
                        ],
                    ),
                },
            )

        if errors:
            raise serializers.ValidationError(errors)

        return attrs
