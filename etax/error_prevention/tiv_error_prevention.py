from rest_framework import serializers

from etax.error_prevention.order_json_error_prevention import (
    eq,
    not_zero,
    positive_number,
)
from etax.error_prevention.utils import field_detail_and_desc
from etax.models import VAT_TYPE_EXC, VAT_TYPE_INC


class D1ATIVRowCheckSerializer(serializers.Serializer):

    # ลำดับรายการ
    TRADE_LINE_ID = serializers.IntegerField()
    # รหัสสินค้า
    PRODUCT_ID = serializers.CharField(max_length=35)
    # ชื่อสินค้า
    PRODUCT_NAME = serializers.CharField()
    # ราคาต่อหน่วย
    PRODUCT_CHARGEAMT = serializers.FloatField(validators=[positive_number])
    # จํานวนสินค้า
    PRODUCT_BILLED_QTY = serializers.FloatField(validators=[positive_number, not_zero])
    # มูลค่าส่วนลดหรือค่าธรรมเนียม ต่อรายการ
    PRODUCT_ACTUALAMT = serializers.FloatField()
    # มูลค่าสินค้าที่นํามาคิดภาษี (ไม่รวมภาษี)
    PRODUCT_TAX_BASISAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าภาษีเฉพาะรายการสินค้านั้นๆ
    PRODUCT_TAX_CALAMT = serializers.F<PERSON><PERSON><PERSON>(validators=[positive_number])
    # ภาษีมูลค่าเพิ่ม
    PRODUCT_SUM_TAXTOTALAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าสินค้ารวม (ไม่รวมภาษี)
    PRODUCT_SUM_NETLINE_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าสินค้ารวม (รวมภาษีแล้ว)
    PRODUCT_SUM_TOTALAMT = serializers.FloatField(validators=[positive_number])

    def validate(self, attrs):
        vattype = self.context.get("vattype", VAT_TYPE_INC)
        product_chargeamt = attrs["PRODUCT_CHARGEAMT"]
        product_billed_qty = attrs["PRODUCT_BILLED_QTY"]
        product_actualamt = attrs["PRODUCT_ACTUALAMT"]
        product_tax_basisamt = attrs["PRODUCT_TAX_BASISAMT"]
        product_tax_calamt = attrs["PRODUCT_TAX_CALAMT"]
        product_sum_taxtotalamt = attrs["PRODUCT_SUM_TAXTOTALAMT"]
        product_sum_netline_totalamt = attrs["PRODUCT_SUM_NETLINE_TOTALAMT"]
        product_sum_totalamt = attrs["PRODUCT_SUM_TOTALAMT"]

        errors = []

        # PRODUCT_TAX_BASISAMT
        if not eq(product_sum_totalamt / 1.07, product_tax_basisamt):
            errors.append(
                {
                    "PRODUCT_TAX_BASISAMT": "PRODUCT_SUM_TOTALAMT / 1.07 != PRODUCT_TAX_BASISAMT",
                    "details": {
                        "PRODUCT_SUM_TOTALAMT": product_sum_totalamt,
                        "PRODUCT_TAX_BASISAMT": product_tax_basisamt,
                    },
                    "description_fields": {
                        "PRODUCT_SUM_TOTALAMT": "มูลค่าสินค้ารวม (รวมภาษีแล้ว)",
                        "PRODUCT_TAX_BASISAMT": "มูลค่าสินค้าที่นํามาคิดภาษี (ไม่รวมภาษี)",
                    },
                },
            )

        # PRODUCT_SUM_NETLINE_TOTALAMT
        if not eq(
            (product_chargeamt * product_billed_qty) - product_actualamt,
            product_sum_netline_totalamt,
        ):
            errors.append(
                {
                    "PRODUCT_SUM_NETLINE_TOTALAMT": "(PRODUCT_CHARGEAMT * PRODUCT_BILLED_QTY) - PRODUCT_ACTUALAMT != PRODUCT_SUM_NETLINE_TOTALAMT",
                    "details": {
                        "PRODUCT_CHARGEAMT": product_chargeamt,
                        "PRODUCT_BILLIED_QTY": product_billed_qty,
                        "PRODUCT_ACTUALAMT": product_actualamt,
                        "PRODUCT_SUM_NETLINE_TOTALAMT": product_sum_netline_totalamt,
                    },
                    "description_fields": {
                        "PRODUCT_CHARGEAMT": "ราคาต่อหน่วย",
                        "PRODUCT_BILLIED_QTY": "จํานวนสินค้า",
                        "PRODUCT_ACTUALAMT": "มูลค่าส่วนลดหรือค่าธรรมเนียม ต่อรายการ",
                        "PRODUCT_SUM_NETLINE_TOTALAMT": "มูลค่าสินค้ารวม (ไม่รวมภาษี)",
                    },
                },
            )

        # PRODUCT_TAX_CALAMT
        if vattype == VAT_TYPE_INC:
            if not eq(
                product_sum_netline_totalamt - (product_sum_netline_totalamt / 1.07),
                product_tax_calamt,
            ):
                errors.append(
                    {
                        "PRODUCT_TAX_CALAMT": "PRODUCT_SUM_NETLINE_TOTALAMT - (PRODUCT_SUM_NETLINE_TOTALAMT / 1.07) != PRODUCT_TAX_CALAMT",
                        "details": {
                            "PRODUCT_SUM_NETLINE_TOTALAMT": product_sum_netline_totalamt,
                            "PRODUCT_TAX_CALAMT": product_tax_calamt,
                        },
                        "description_fields": {
                            "PRODUCT_SUM_NETLINE_TOTALAMT": "มูลค่าสินค้ารวม (ไม่รวมภาษี)",
                            "PRODUCT_TAX_CALAMT": "มูลค่าภาษีเฉพาะรายการสินค้านั้นๆ",
                        },
                    },
                )

        if errors:
            raise serializers.ValidationError(errors)

        return attrs


class D1A_VAT_CheckSerializer(serializers.Serializer):

    # 2 Exchanged Document หัวเรื่องเอกสาร

    # เลขที่เอกสาร
    DOC_ID = serializers.CharField(max_length=35)

    # ชื่อเอกสาร
    DOC_NAME = serializers.CharField(max_length=35)

    # รหัสประเภทเอกสาร
    DOC_TYPE = serializers.CharField()

    # วันเดือนปี ที่ออกเอกสาร
    DOC_ISSUE_DATE = serializers.DateTimeField()

    # วันเดือนปีและเวลาที่สร้างเอกสาร
    DOC_CREATE_DATE = serializers.DateTimeField()

    # --------------------------------------------------------

    # 3 SupplyChainTrade Transaction ธุรกรรมทางการค้า
    # 3.1 ApplicableHeaderTradeAgreement ข้อตกลงทางการค้า
    # 3.1.1 SellerTradeParty ผู้ขาย
    # ชื่อผู้ขาย
    SELLER_NAME = serializers.CharField(max_length=255)

    # ******* SpecifiedTaxRegistration ข้อมูลการลงทะเบียนผู้เสียภาษีอากร

    # เลขประจำตัวผู้เสียภาษีอากร
    SELLER_TAX_ID = serializers.CharField(max_length=35)
    SELLER_TYPE_TAX = serializers.CharField()
    SELLER_BRANCH = serializers.CharField()
    SELLER_BRANCH_NAME = serializers.CharField()

    # ******* PostalTradeAddress ที่อยู่ (ผู้ขาย)

    # รหัสไปรษณีย์ (ผู้ขาย)
    SELLER_POSTCODE = serializers.CharField(max_length=16)

    # ที่อยู่บรรทัดที่ 1 (ผู้ขาย)
    SELLER_ADDRESS = serializers.CharField(max_length=255)

    # รหัสประเทศ (ผู้ขาย)
    SELLER_COUNTRY_ID = serializers.CharField(max_length=255)

    # 3.1.2 BuyerTradeParty ผู้ซื้อ
    # ชื่อผู้ซื้อ
    BUYER_NAME = serializers.CharField(max_length=255)

    # ******* SpecifiedTaxRegistration ข้อมูลการลงทะเบียนผู้เสียภาษีอากร

    # เลขประจำตัวผู้เสียภาษีอากร
    BUYER_TAX_ID = serializers.CharField(max_length=35)
    BUYER_TYPE_TAX = serializers.CharField()
    BUYER_BRANCH = serializers.CharField(allow_blank=True)
    BUYER_BRANCH_NAME = serializers.CharField(allow_blank=True)

    # ******* DefinedTradeContact รายละเอียดการติดต่อ
    # *******.3 EmailURIUniversal Communication การติดต่อทางอีเมล
    # อีเมล
    BUYER_CONTACT_EMAIL = serializers.CharField(max_length=255, allow_blank=True)

    # ******* PostalTradeAddress ที่อยู่ (ผู้ซื้อ)
    # รหัสไปรษณีย์ (ผู้ซื้อ)
    BUYER_POSTCODE = serializers.CharField(max_length=16)

    # ที่อยู่บรรทัดที่ 1 (ผู้ซื้อ)
    BUYER_ADDRESS = serializers.CharField(max_length=255)

    # รหัสประเทศ (ผู้ซื้อ)
    BUYER_COUNTRY_ID = serializers.CharField(max_length=255)

    # 3.3 ApplicableHeaderTradeSettlement ข้อมูลการชำระเงินทางการค้า
    # 3.3.2 ApplicableTradeTax ข้อมูลภาษี
    # รหัสประเภทภาษี
    TAX_TYPE_CODE = serializers.CharField()
    # อัตราภาษี
    TAX_CALCULATED_RATE = serializers.CharField()

    # 3.3.5 SpecifiedTradeSettlementHeaderMonetarySummation การสรุปมูลค่าการชำระเงินทาง
    # มูลค่าสินค้า/บริการ (ไม่รวมภาษีมูลค่าเพิ่ม ทศนิยม 2 ตำแหน่ง)
    TAX_BASIS_AMOUNT = serializers.FloatField(validators=[positive_number])
    # มูลค่าภาษีมูลค่าเพิ่ม
    TAX_CALCULATED_AMOUNT = serializers.FloatField(validators=[positive_number])
    # รวมมูลคาตามรายการ/มูลค่าที่ถูกต้อง
    MONEY_LINE_TOTALAMOUNT = serializers.FloatField(validators=[positive_number])
    # ส่วนลดท้ายบิล
    ALLOWANCE_SP_ACTUAL_AMOUNT = serializers.FloatField()
    # ส่วนลดทั้งหมด
    MONEY_ALLOWANCE_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # มูลค่าที่นำมาคิดภาษีมูลค่าเพิ่ม
    MONEY_TAXBASIS_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # จำนวนภาษีมูลค่าเพิ่ม (รวมทศนิยม 2 ต าแหน่ง)
    MONEY_TAX_TOTALAMT = serializers.FloatField(validators=[positive_number])
    # ยอดเงินรวม/ ยอดเงินรวม ภาษีมูลค่าเพิ่ม
    MONEY_GRAND_TOTALAMT = serializers.FloatField(
        validators=[positive_number, not_zero]
    )

    # ข้อมูลสินค้า
    items = D1ATIVRowCheckSerializer(many=True)

    def validate(self, attrs):
        vattype = self.context.get("vattype", VAT_TYPE_INC)
        product_items = attrs["items"]
        money_line_totalamount = attrs["MONEY_LINE_TOTALAMOUNT"]
        allowance_sp_actual_amount = attrs["ALLOWANCE_SP_ACTUAL_AMOUNT"]
        money_allowance_totalamt = attrs["MONEY_ALLOWANCE_TOTALAMT"]
        money_taxbasis_totalamt = attrs["MONEY_TAXBASIS_TOTALAMT"]
        money_tax_totalamt = attrs["MONEY_TAX_TOTALAMT"]
        money_grand_totalamt = attrs["MONEY_GRAND_TOTALAMT"]

        sum_product_totalamt = sum(
            [product["PRODUCT_SUM_TOTALAMT"] for product in product_items]
        )
        sum_product_discount = sum(
            [product["PRODUCT_ACTUALAMT"] for product in product_items]
        )
        sum_product_price_before_discount = sum(
            [
                product["PRODUCT_CHARGEAMT"] * product["PRODUCT_BILLED_QTY"]
                for product in product_items
            ]
        )

        attrs["SUM_PRODUCT_DISCOUNT"] = sum_product_discount
        attrs["SUM_PRODUCT_TOTALAMT"] = sum_product_totalamt
        attrs["SUM_PRODUCT_PRICE_BEFORE_DISCOUNT"] = sum_product_price_before_discount

        errors = []

        # MONEY_LINE_TOTALAMOUNT
        if not eq(money_grand_totalamt / 1.07, money_line_totalamount):
            errors.append(
                {
                    "MONEY_LINE_TOTALAMOUNT": "MONEY_GRAND_TOTALAMT / 1.07 != MONEY_LINE_TOTALAMOUNT",
                    **field_detail_and_desc(
                        attrs, ["MONEY_LINE_TOTALAMOUNT", "MONEY_GRAND_TOTALAMT"]
                    ),
                },
            )

        # MONEY_ALLOWANCE_TOTALAMT
        if not eq(
            sum_product_discount + allowance_sp_actual_amount, money_allowance_totalamt
        ):
            errors.append(
                {
                    "MONEY_ALLOWANCE_TOTALAMT": "SUM_PRODUCT_DISCOUNT + ALLOWANCE_SP_ACTUAL_AMOUNT != MONEY_ALLOWANCE_TOTALAMT",
                    **field_detail_and_desc(
                        attrs,
                        [
                            "MONEY_ALLOWANCE_TOTALAMT",
                            "SUM_PRODUCT_DISCOUNT",
                            "ALLOWANCE_SP_ACTUAL_AMOUNT",
                        ],
                    ),
                }
            )

        # MONEY_TAX_BASIS_TOTAL_AMOUNT
        if not eq(money_grand_totalamt / 1.07, money_taxbasis_totalamt):
            errors.append(
                {
                    "MONEY_TAXBASIS_TOTALAMT": "MONEY_GRAND_TOTALAMT != MONEY_TAXBASIS_TOTALAMT",
                    **field_detail_and_desc(
                        attrs, ["MONEY_TAXBASIS_TOTALAMT", "MONEY_GRAND_TOTALAMT"]
                    ),
                },
            )

        # MONEY_TAX_TOTALAMT
        if not eq(money_grand_totalamt * 7 / 107, money_tax_totalamt):
            errors.append(
                {
                    "MONEY_TAX_TOTALAMT": "MONEY_GRAND_TOTALAMT * 7 / 107  != MONEY_TAX_TOTALAMT",
                    **field_detail_and_desc(
                        attrs, ["MONEY_TAX_TOTALAMT", "MONEY_GRAND_TOTALAMT"]
                    ),
                },
            )

        if vattype == VAT_TYPE_INC:
            # MONEY_GRAND_TOTALAMT
            if not eq(
                sum_product_totalamt - allowance_sp_actual_amount, money_grand_totalamt
            ):
                errors.append(
                    {
                        "MONEY_GRAND_TOTALAMT": "SUM_PRODUCT_TOTALAMT - ALLOWANCE_SP_ACTUAL_AMOUNT != MONEY_GRAND_TOTAL_AMOUNT",
                        **field_detail_and_desc(
                            attrs,
                            [
                                "SUM_PRODUCT_TOTALAMT",
                                "ALLOWANCE_SP_ACTUAL_AMOUNT",
                                "MONEY_GRAND_TOTALAMT",
                            ],
                        ),
                    },
                )

            if not eq(
                sum_product_totalamt - allowance_sp_actual_amount, money_grand_totalamt
            ):
                errors.append(
                    {
                        "MONEY_GRAND_TOTALAMT": "SUM_PRODUCT_TOTALAMT - ALLOWANCE_SP_ACTUAL_AMOUNT != MONEY_GRAND_TOTAL_AMOUNT",
                        **field_detail_and_desc(
                            attrs,
                            [
                                "SUM_PRODUCT_TOTALAMT",
                                "ALLOWANCE_SP_ACTUAL_AMOUNT",
                                "MONEY_GRAND_TOTALAMT",
                            ],
                        ),
                    },
                )
            if not eq(
                sum_product_price_before_discount - money_allowance_totalamt,
                money_grand_totalamt,
                tolerance=0.2,
            ):
                errors.append(
                    {
                        "SUM_PRODUCT_PRICE_BEFORE_DISCOUNT": "SUM_PRODUCT_PRICE_BEFORE_DISCOUNT - MONEY_ALLOWANCE_TOTALAMT != MONEY_GRAND_TOTALAMT",
                        **field_detail_and_desc(
                            attrs,
                            [
                                "SUM_PRODUCT_PRICE_BEFORE_DISCOUNT",
                                "MONEY_ALLOWANCE_TOTALAMT",
                                "MONEY_GRAND_TOTALAMT",
                            ],
                        ),
                    },
                )
        elif vattype == VAT_TYPE_EXC:
            if not eq(
                (sum_product_price_before_discount - money_allowance_totalamt) * 1.07,
                money_grand_totalamt,
                tolerance=0.2,
            ):
                errors.append(
                    {
                        "SUM_PRODUCT_PRICE_BEFORE_DISCOUNT": "SUM_PRODUCT_PRICE_BEFORE_DISCOUNT - MONEY_ALLOWANCE_TOTALAMT != MONEY_GRAND_TOTALAMT",
                        **field_detail_and_desc(
                            attrs,
                            [
                                "SUM_PRODUCT_PRICE_BEFORE_DISCOUNT",
                                "MONEY_ALLOWANCE_TOTALAMT",
                                "MONEY_GRAND_TOTALAMT",
                            ],
                        ),
                    },
                )

        if not eq(money_taxbasis_totalamt + money_tax_totalamt, money_grand_totalamt):
            errors.append(
                {
                    "MONEY_TAXBASIS_TOTALAMT": "MONEY_TAXBASIS_TOTALAMT + MONEY_TAX_TOTALAMT != MONEY_GRAND_TOTALAMT",
                    **field_detail_and_desc(
                        attrs,
                        [
                            "MONEY_TAXBASIS_TOTALAMT",
                            "MONEY_TAX_TOTALAMT",
                            "MONEY_GRAND_TOTALAMT",
                        ],
                    ),
                },
            )

        if errors:
            raise serializers.ValidationError(errors)

        return attrs


class SimpleD1ATIVRowCheckSerializer(D1ATIVRowCheckSerializer):
    PRODUCT_ACTUALAMT = serializers.CharField(allow_blank=True, required=False)
    PRODUCT_CHARGEAMT = serializers.FloatField()

    def validate_PRODUCT_ACTUALAMT(self, value):
        if not value:
            return 0
        return float(value)

    def validate(self, attrs):
        return attrs


class SimpleD1ATIVCheckSerializer(D1A_VAT_CheckSerializer):
    ALLOWANCE_SP_ACTUAL_AMOUNT = serializers.CharField(allow_blank=True, required=False)
    SELLER_POSTCODE = serializers.CharField(allow_blank=True, required=False)
    SELLER_COUNTRY_ID = serializers.CharField(allow_blank=True, required=False)
    BUYER_POSTCODE = serializers.CharField(allow_blank=True, required=False)
    BUYER_COUNTRY_ID = serializers.CharField(allow_blank=True, required=False)
    items = SimpleD1ATIVRowCheckSerializer(many=True)

    def validate_ALLOWANCE_SP_ACTUAL_AMOUNT(self, value):
        if not value:
            return 0
        return float(value)
