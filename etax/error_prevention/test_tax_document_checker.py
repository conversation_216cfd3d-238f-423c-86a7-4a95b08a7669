from django.test import SimpleTestCase, tag
from rest_framework import serializers
from pprint import pprint
from etax.error_prevention.cn_error_prevention import D1A_CN_CheckSerializer
from etax.error_prevention.rct_error_prevention import D1A_NONVAT_CheckSerializer
from etax.error_prevention.tiv_error_prevention import D1A_VAT_CheckSerializer


class TestDocument<PERSON><PERSON><PERSON>(SimpleTestCase):
    @tag("tax-document-checker")
    def test_document_row_checker_pass(self):
        data = {
            "DOC_ID": "RT-250126G3DE8WES-J2",
            "DALI_BY": "",
            "RD_FLAG": "N",
            "BUYER_ID": "",
            "DATETIME": "",
            "DOC_NAME": "ใบเสร็จรับเงิน/ใบกำกับภาษี",
            "DOC_TYPE": "T03",
            "PAYEE_ID": "",
            "PAYER_ID": "",
            "PAY_TERM": "",
            "BRANCH_ID": "3f329a7a-1496-490c-850e-dff7d3d34bf2",
            "BUYER_FAX": "",
            "DALI_DATE": "",
            "SELLER_ID": "",
            "SHIPTO_ID": "",
            "TENANT_ID": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
            "BUYER_NAME": "บริษัท ดูบายบอท จำกัด (สำนักงานใหญ่)",
            "DOC_REMARK": "",
            "DOC_STATUS": "NEW",
            "EMAIL_FLAG": "Y",
            "PAYEE_NAME": "",
            "PAYER_NAME": "",
            "PDF_BASE64": "",
            "REMARK_INV": "",
            "SELLER_FAX": "",
            "XML_BASE64": "",
            "DOC_CONTENT": "",
            "DOC_GLOB_ID": "",
            "DOC_SUBJECT": "",
            "INVOICEE_ID": "",
            "INVOICER_ID": "",
            "SELLER_NAME": "บริษัท คัสเวย์ เอ็มไพร์ จำกัด",
            "SHIPFROM_ID": "",
            "SHIPTO_NAME": "บริษัท ดูบายบอท จำกัด",
            "TENANT_CODE": "dt2024046",
            "BUYER_BRANCH": "00000",
            "BUYER_TAX_ID": "0105565020301",
            "DOC_PDF_FLAG": "",
            "DOC_PDF_SIZE": "",
            "DOC_XML_FLAG": "",
            "PAYEE_TAX_ID": "",
            "PAYER_TAX_ID": "",
            "REMARK_OTHER": "",
            "BUYER_ADDRESS": "245/68 ซอยรามคำแหง 112 ถนนรามคำแหง แขวงสะพานสูง เขตสะพานสูง กรุงเทพมหานคร 10240",
            "BUYER_GLOB_ID": "",
            "INVOICEE_NAME": "",
            "INVOICER_NAME": "",
            "PAYEE_ADDRESS": "",
            "PAYEE_GLOB_ID": "",
            "PAYER_ADDRESS": "",
            "PAYER_GLOB_ID": "",
            "SELLER_BRANCH": "00000",
            "SELLER_TAX_ID": "0105559130701",
            "SHIPFROM_NAME": "",
            "TAX_TYPE_CODE": "VAT",
            "BUYER_ADDRESS2": "",
            "BUYER_ORDER_ID": "",
            "BUYER_POSTCODE": "10240",
            "BUYER_TYPE_TAX": "TXID",
            "DOC_ISSUE_DATE": "2025-02-06T00:00:00+07:00",
            "MONEY_DIFF_AMT": "",
            "PAYEE_ADDRESS2": "",
            "PAYEE_POSTCODE": "",
            "PAYEE_TYPE_TAX": "",
            "PAYER_ADDRESS2": "",
            "PAYER_POSTCODE": "",
            "PAYER_TYPE_TAX": "",
            "REASON_DISPLAY": "",
            "REMARK_OTHER_2": "",
            "REMARK_OTHER_3": "",
            "SELLER_ADDRESS": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230",
            "SELLER_GLOB_ID": "",
            "SHIPTO_ADDRESS": "****** กาญจนาภิเษก แขวง บางแค เขต บางแค กทม 1... *หมายเหตุ: ออเดอร์ 250126G3DE8WES-J2 ออกเอกสารพร้อมกัน 2 ฉบับ คือ RT-250126G3DE8WES-J2 เฉพาะสินค้ามี VAT และ R-250126G3DE8WES-J2 เฉพาะสินค้าไม่มี VAT ",
            "SHIPTO_GLOB_ID": "",
            "BUYER_BIRTHDATE": "",
            "DOC_CREATE_DATE": "2025-02-06T15:18:11.956657+07:00",
            "INVOICEE_TAX_ID": "",
            "INVOICER_TAX_ID": "",
            "SELLER_ADDRESS2": "",
            "SELLER_POSTCODE": "10230",
            "SELLER_TYPE_TAX": "TXID",
            "SHIPTO_ADDRESS2": "",
            "SHIPTO_POSTCODE": "",
            "BUYER_COUNTRY_ID": "TH",
            "DOC_PURPOSE_CODE": "",
            "INVOICEE_ADDRESS": "",
            "INVOICEE_GLOB_ID": "",
            "INVOICER_ADDRESS": "",
            "INVOICER_GLOB_ID": "",
            "ORIGINAL_INVOICE": "",
            "PAYEE_COUNTRY_ID": "",
            "PAYER_COUNTRY_ID": "",
            "SHIPFROM_ADDRESS": "",
            "SHIPFROM_GLOB_ID": "",
            "BUYER_BRANCH_NAME": "00000",
            "BUYER_BUILDING_NO": "",
            "BUYER_DOCUMENT_ID": "",
            "INVOICEE_ADDRESS2": "",
            "INVOICEE_POSTCODE": "",
            "INVOICEE_TYPE_TAX": "",
            "INVOICER_ADDRESS2": "",
            "INVOICER_POSTCODE": "",
            "INVOICER_TYPE_TAX": "",
            "PAYMENT_TYPE_CODE": "",
            "SELLER_COUNTRY_ID": "TH",
            "SHIPFROM_ADDRESS2": "",
            "SHIPFROM_POSTCODE": "",
            "SHIPTO_COUNTRY_ID": "",
            "BUYER_CONTACT_NAME": "",
            "DOC_PURPOSE_DETAIL": "",
            "PAYEE_CONTACT_NAME": "",
            "PAYER_CONTACT_NAME": "",
            "SELLER_BRANCH_NAME": "สำนักงานใหญ่",
            "SELLER_BUILDING_NO": "",
            "ALLOWANCE_TYPE_CODE": "",
            "BUYER_BUILDING_NAME": "",
            "BUYER_CONTACT_EMAIL": "<EMAIL>",
            "BUYER_CONTACT_PHONE": "0886604941",
            "BUYER_DELIVERY_TYPE": "",
            "BUYER_DOC_TYPE_CODE": "",
            "INVOICEE_COUNTRY_ID": "",
            "INVOICER_COUNTRY_ID": "",
            "OCCURRENCE_DATETIME": "",
            "PAYEE_CONTACT_EMAIL": "",
            "PAYEE_CONTACT_PHONE": "",
            "PAYER_CONTACT_EMAIL": "",
            "PAYER_CONTACT_PHONE": "",
            "PAYMENT_DESCRIPTION": "",
            "SELLER_CONTACT_NAME": "",
            "SHIPFROM_COUNTRY_ID": "",
            "SHIPTO_CONTACT_NAME": "",
            "TAX_CALCULATED_RATE": "7",
            "BUYER_DOC_ISSUE_DATE": "",
            "INVOICER_BUILDING_NO": "",
            "PAYMENT_DUE_DATETIME": "",
            "SELLER_BUILDING_NAME": "",
            "SELLER_CONTACT_EMAIL": "",
            "SELLER_CONTACT_PHONE": "0804000500",
            "SELLER_DISTRICT_CODE": "",
            "SELLER_DISTRICT_NAME": "",
            "SELLER_PROVINCE_CODE": "",
            "SELLER_PROVINCE_NAME": "",
            "SHIPTO_CONTACT_EMAIL": "",
            "SHIPTO_CONTACT_PHONE": "",
            "ALLOWANCE_REASON_CODE": "",
            "ALLOWANCE_REASON_NAME": "",
            "BUYER_ORDER_TYPE_CODE": "",
            "INVOICEE_CONTACT_NAME": "",
            "INVOICER_CONTACT_NAME": "",
            "INVOICE_CURRENCY_CODE": "THB",
            "MONEY_CHARGE_TOTALAMT": "",
            "MONEY_ORIGINAL_AMOUNT": "",
            "SHIPFROM_CONTACT_NAME": "",
            "BUYER_ORDER_ISSUE_DATE": "",
            "INVOICEE_CONTACT_EMAIL": "",
            "INVOICEE_CONTACT_PHONE": "",
            "INVOICER_BUILDING_NAME": "",
            "INVOICER_CONTACT_EMAIL": "",
            "INVOICER_CONTACT_PHONE": "",
            "INVOICER_DISTRICT_CODE": "",
            "INVOICER_PROVINCE_CODE": "",
            "SHIPFROM_CONTACT_EMAIL": "",
            "SHIPFROM_CONTACT_PHONE": "",
            "ALLOWANCE_ACTUAL_AMOUNT": "",
            "SELLER_SUBDISTRICT_CODE": "",
            "SELLER_SUBDISTRICT_NAME": "",
            "BUYER_CONTACT_DEPARTMENT": "",
            "BUYER_DELIVERY_TYPE_CODE": "",
            "PAYEE_CONTACT_DEPARTMENT": "",
            "PAYER_CONTACT_DEPARTMENT": "",
            "INVOICER_SUBDISTRICT_CODE": "",
            "SELLER_CONTACT_DEPARTMENT": "",
            "SHIPTO_CONTACT_DEPARTMENT": "",
            "ALLOWANCE_CHARGE_INDICATOR": "",
            "INVOICEE_CONTACT_DEPARTMENT": "",
            "INVOICER_CONTACT_DEPARTMENT": "",
            "SHIPFROM_CONTACT_DEPARTMENT": "",
            "MONEY_EXCEPT_TAXBASIS_TOTALAMT": "",
            "TAX_BASIS_AMOUNT": 175.70,
            "TAX_CALCULATED_AMOUNT": 12.3,
            "MONEY_LINE_TOTALAMOUNT": 166.36,
            "ALLOWANCE_SP_ACTUAL_AMOUNT": 10,
            "MONEY_ALLOWANCE_TOTALAMT": 50,
            "MONEY_TAXBASIS_TOTALAMT": 166.36,
            "MONEY_TAX_TOTALAMT": 11.64,
            "MONEY_GRAND_TOTALAMT": 178,
            "items": [
                {
                    "TRADE_LINE_ID": 1,
                    "PRODUCT_ID": "GT_1",
                    "PRODUCT_NAME": "Ghost-1",
                    "PRODUCT_CHARGEAMT": 69,
                    "PRODUCT_BILLED_QTY": 2,
                    "PRODUCT_ACTUALAMT": 30,
                    "PRODUCT_TAX_BASISAMT": 100.93,
                    "PRODUCT_TAX_CALAMT": 7.07,
                    "PRODUCT_SUM_TAXTOTALAMT": 7.07,
                    "PRODUCT_SUM_NETLINE_TOTALAMT": 108,
                    "PRODUCT_SUM_TOTALAMT": 108,
                },
                {
                    "TRADE_LINE_ID": 2,
                    "PRODUCT_ID": "GT_2",
                    "PRODUCT_NAME": "Ghost-2",
                    "PRODUCT_CHARGEAMT": 30,
                    "PRODUCT_BILLED_QTY": 3,
                    "PRODUCT_ACTUALAMT": 10,
                    "PRODUCT_TAX_BASISAMT": 74.77,
                    "PRODUCT_TAX_CALAMT": 5.23,
                    "PRODUCT_SUM_TAXTOTALAMT": 5.23,
                    "PRODUCT_SUM_NETLINE_TOTALAMT": 80,
                    "PRODUCT_SUM_TOTALAMT": 80,
                },
            ],
        }

        checker = D1A_VAT_CheckSerializer(data=data)
        ok = checker.is_valid()

        self.assertEqual(ok, True)

    @tag("tax-document-checker")
    def test_document_row_checker_error(self):
        data = {
            "DOC_ID": "RT-250126G3DE8WES-J2",
            "DALI_BY": "",
            "RD_FLAG": "N",
            "BUYER_ID": "",
            "DATETIME": "",
            "DOC_NAME": "ใบเสร็จรับเงิน/ใบกำกับภาษี",
            "DOC_TYPE": "T03",
            "PAYEE_ID": "",
            "PAYER_ID": "",
            "PAY_TERM": "",
            "BRANCH_ID": "3f329a7a-1496-490c-850e-dff7d3d34bf2",
            "BUYER_FAX": "",
            "DALI_DATE": "",
            "SELLER_ID": "",
            "SHIPTO_ID": "",
            "TENANT_ID": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
            "BUYER_NAME": "บริษัท ดูบายบอท จำกัด (สำนักงานใหญ่)",
            "DOC_REMARK": "",
            "DOC_STATUS": "NEW",
            "EMAIL_FLAG": "Y",
            "PAYEE_NAME": "",
            "PAYER_NAME": "",
            "PDF_BASE64": "",
            "REMARK_INV": "",
            "SELLER_FAX": "",
            "XML_BASE64": "",
            "DOC_CONTENT": "",
            "DOC_GLOB_ID": "",
            "DOC_SUBJECT": "",
            "INVOICEE_ID": "",
            "INVOICER_ID": "",
            "SELLER_NAME": "บริษัท คัสเวย์ เอ็มไพร์ จำกัด",
            "SHIPFROM_ID": "",
            "SHIPTO_NAME": "บริษัท ดูบายบอท จำกัด",
            "TENANT_CODE": "dt2024046",
            "BUYER_BRANCH": "00000",
            "BUYER_TAX_ID": "0105565020301",
            "DOC_PDF_FLAG": "",
            "DOC_PDF_SIZE": "",
            "DOC_XML_FLAG": "",
            "PAYEE_TAX_ID": "",
            "PAYER_TAX_ID": "",
            "REMARK_OTHER": "",
            "BUYER_ADDRESS": "245/68 ซอยรามคำแหง 112 ถนนรามคำแหง แขวงสะพานสูง เขตสะพานสูง กรุงเทพมหานคร 10240",
            "BUYER_GLOB_ID": "",
            "INVOICEE_NAME": "",
            "INVOICER_NAME": "",
            "PAYEE_ADDRESS": "",
            "PAYEE_GLOB_ID": "",
            "PAYER_ADDRESS": "",
            "PAYER_GLOB_ID": "",
            "SELLER_BRANCH": "00000",
            "SELLER_TAX_ID": "0105559130701",
            "SHIPFROM_NAME": "",
            "TAX_TYPE_CODE": "VAT",
            "BUYER_ADDRESS2": "",
            "BUYER_ORDER_ID": "",
            "BUYER_POSTCODE": "10240",
            "BUYER_TYPE_TAX": "TXID",
            "DOC_ISSUE_DATE": "2025-02-06T00:00:00+07:00",
            "MONEY_DIFF_AMT": "",
            "PAYEE_ADDRESS2": "",
            "PAYEE_POSTCODE": "",
            "PAYEE_TYPE_TAX": "",
            "PAYER_ADDRESS2": "",
            "PAYER_POSTCODE": "",
            "PAYER_TYPE_TAX": "",
            "REASON_DISPLAY": "",
            "REMARK_OTHER_2": "",
            "REMARK_OTHER_3": "",
            "SELLER_ADDRESS": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230",
            "SELLER_GLOB_ID": "",
            "SHIPTO_ADDRESS": "****** กาญจนาภิเษก แขวง บางแค เขต บางแค กทม 1... *หมายเหตุ: ออเดอร์ 250126G3DE8WES-J2 ออกเอกสารพร้อมกัน 2 ฉบับ คือ RT-250126G3DE8WES-J2 เฉพาะสินค้ามี VAT และ R-250126G3DE8WES-J2 เฉพาะสินค้าไม่มี VAT ",
            "SHIPTO_GLOB_ID": "",
            "BUYER_BIRTHDATE": "",
            "DOC_CREATE_DATE": "2025-02-06T15:18:11.956657+07:00",
            "INVOICEE_TAX_ID": "",
            "INVOICER_TAX_ID": "",
            "SELLER_ADDRESS2": "",
            "SELLER_POSTCODE": "10230",
            "SELLER_TYPE_TAX": "TXID",
            "SHIPTO_ADDRESS2": "",
            "SHIPTO_POSTCODE": "",
            "BUYER_COUNTRY_ID": "TH",
            "DOC_PURPOSE_CODE": "",
            "INVOICEE_ADDRESS": "",
            "INVOICEE_GLOB_ID": "",
            "INVOICER_ADDRESS": "",
            "INVOICER_GLOB_ID": "",
            "ORIGINAL_INVOICE": "",
            "PAYEE_COUNTRY_ID": "",
            "PAYER_COUNTRY_ID": "",
            "SHIPFROM_ADDRESS": "",
            "SHIPFROM_GLOB_ID": "",
            "BUYER_BRANCH_NAME": "00000",
            "BUYER_BUILDING_NO": "",
            "BUYER_DOCUMENT_ID": "",
            "INVOICEE_ADDRESS2": "",
            "INVOICEE_POSTCODE": "",
            "INVOICEE_TYPE_TAX": "",
            "INVOICER_ADDRESS2": "",
            "INVOICER_POSTCODE": "",
            "INVOICER_TYPE_TAX": "",
            "PAYMENT_TYPE_CODE": "",
            "SELLER_COUNTRY_ID": "TH",
            "SHIPFROM_ADDRESS2": "",
            "SHIPFROM_POSTCODE": "",
            "SHIPTO_COUNTRY_ID": "",
            "BUYER_CONTACT_NAME": "",
            "DOC_PURPOSE_DETAIL": "",
            "PAYEE_CONTACT_NAME": "",
            "PAYER_CONTACT_NAME": "",
            "SELLER_BRANCH_NAME": "สำนักงานใหญ่",
            "SELLER_BUILDING_NO": "",
            "ALLOWANCE_TYPE_CODE": "",
            "BUYER_BUILDING_NAME": "",
            "BUYER_CONTACT_EMAIL": "<EMAIL>",
            "BUYER_CONTACT_PHONE": "0886604941",
            "BUYER_DELIVERY_TYPE": "",
            "BUYER_DOC_TYPE_CODE": "",
            "INVOICEE_COUNTRY_ID": "",
            "INVOICER_COUNTRY_ID": "",
            "OCCURRENCE_DATETIME": "",
            "PAYEE_CONTACT_EMAIL": "",
            "PAYEE_CONTACT_PHONE": "",
            "PAYER_CONTACT_EMAIL": "",
            "PAYER_CONTACT_PHONE": "",
            "PAYMENT_DESCRIPTION": "",
            "SELLER_CONTACT_NAME": "",
            "SHIPFROM_COUNTRY_ID": "",
            "SHIPTO_CONTACT_NAME": "",
            "TAX_CALCULATED_RATE": "7",
            "BUYER_DOC_ISSUE_DATE": "",
            "INVOICER_BUILDING_NO": "",
            "PAYMENT_DUE_DATETIME": "",
            "SELLER_BUILDING_NAME": "",
            "SELLER_CONTACT_EMAIL": "",
            "SELLER_CONTACT_PHONE": "0804000500",
            "SELLER_DISTRICT_CODE": "",
            "SELLER_DISTRICT_NAME": "",
            "SELLER_PROVINCE_CODE": "",
            "SELLER_PROVINCE_NAME": "",
            "SHIPTO_CONTACT_EMAIL": "",
            "SHIPTO_CONTACT_PHONE": "",
            "ALLOWANCE_REASON_CODE": "",
            "ALLOWANCE_REASON_NAME": "",
            "BUYER_ORDER_TYPE_CODE": "",
            "INVOICEE_CONTACT_NAME": "",
            "INVOICER_CONTACT_NAME": "",
            "INVOICE_CURRENCY_CODE": "THB",
            "MONEY_CHARGE_TOTALAMT": "",
            "MONEY_ORIGINAL_AMOUNT": "",
            "SHIPFROM_CONTACT_NAME": "",
            "BUYER_ORDER_ISSUE_DATE": "",
            "INVOICEE_CONTACT_EMAIL": "",
            "INVOICEE_CONTACT_PHONE": "",
            "INVOICER_BUILDING_NAME": "",
            "INVOICER_CONTACT_EMAIL": "",
            "INVOICER_CONTACT_PHONE": "",
            "INVOICER_DISTRICT_CODE": "",
            "INVOICER_PROVINCE_CODE": "",
            "SHIPFROM_CONTACT_EMAIL": "",
            "SHIPFROM_CONTACT_PHONE": "",
            "ALLOWANCE_ACTUAL_AMOUNT": "",
            "SELLER_SUBDISTRICT_CODE": "",
            "SELLER_SUBDISTRICT_NAME": "",
            "BUYER_CONTACT_DEPARTMENT": "",
            "BUYER_DELIVERY_TYPE_CODE": "",
            "PAYEE_CONTACT_DEPARTMENT": "",
            "PAYER_CONTACT_DEPARTMENT": "",
            "INVOICER_SUBDISTRICT_CODE": "",
            "SELLER_CONTACT_DEPARTMENT": "",
            "SHIPTO_CONTACT_DEPARTMENT": "",
            "ALLOWANCE_CHARGE_INDICATOR": "",
            "INVOICEE_CONTACT_DEPARTMENT": "",
            "INVOICER_CONTACT_DEPARTMENT": "",
            "SHIPFROM_CONTACT_DEPARTMENT": "",
            "MONEY_EXCEPT_TAXBASIS_TOTALAMT": "",
            "TAX_BASIS_AMOUNT": 175.70,
            "TAX_CALCULATED_AMOUNT": 12.3,
            "MONEY_LINE_TOTALAMOUNT": 166.36,
            "ALLOWANCE_SP_ACTUAL_AMOUNT": 10,
            "MONEY_ALLOWANCE_TOTALAMT": 50,
            "MONEY_TAXBASIS_TOTALAMT": 166.36,
            "MONEY_TAX_TOTALAMT": 11.64,
            "MONEY_GRAND_TOTALAMT": 178,
            "TAX_BASIS_AMOUNT": 175.70,
            "TAX_CALCULATED_AMOUNT": 12.3,
            "MONEY_LINE_TOTALAMOUNT": 166.36,
            "ALLOWANCE_SP_ACTUAL_AMOUNT": 10,
            "MONEY_ALLOWANCE_TOTALAMT": 0,
            "MONEY_TAXBASIS_TOTALAMT": 175.70,
            "MONEY_TAX_TOTALAMT": 12.3,
            "MONEY_GRAND_TOTALAMT": 178,
            "items": [
                {
                    "TRADE_LINE_ID": 1,
                    "PRODUCT_ID": "GT_1",
                    "PRODUCT_NAME": "Ghost-1",
                    "PRODUCT_CHARGEAMT": 69,
                    "PRODUCT_BILLED_QTY": 2,
                    "PRODUCT_ACTUALAMT": 30,
                    "PRODUCT_TAX_BASISAMT": 100.93,
                    "PRODUCT_TAX_CALAMT": 7.07,
                    "PRODUCT_SUM_TAXTOTALAMT": 7.07,
                    "PRODUCT_SUM_NETLINE_TOTALAMT": 108,
                    "PRODUCT_SUM_TOTALAMT": 108,
                },
                {
                    "TRADE_LINE_ID": 2,
                    "PRODUCT_ID": "GT_2",
                    "PRODUCT_NAME": "Ghost-2",
                    "PRODUCT_CHARGEAMT": 30,
                    "PRODUCT_BILLED_QTY": 3,
                    "PRODUCT_ACTUALAMT": 10,
                    "PRODUCT_TAX_BASISAMT": 74.77,
                    "PRODUCT_TAX_CALAMT": 5.23,
                    "PRODUCT_SUM_TAXTOTALAMT": 5.23,
                    "PRODUCT_SUM_NETLINE_TOTALAMT": 80,
                    "PRODUCT_SUM_TOTALAMT": 80,
                },
            ],
        }

        checker = D1A_VAT_CheckSerializer(data=data)
        ok = checker.is_valid()

        self.assertEqual(ok, False)
        pprint(checker.errors)

        self.assertTrue(True)

    @tag("tax-document-checker")
    def test_cn_documnet_pass(self):
        data = {
            "DOC_ID": "RT-250126G3DE8WES-J2",
            "DALI_BY": "",
            "RD_FLAG": "N",
            "BUYER_ID": "",
            "DATETIME": "",
            "DOC_NAME": "ใบเสร็จรับเงิน/ใบกำกับภาษี",
            "DOC_TYPE": "T03",
            "PAYEE_ID": "",
            "PAYER_ID": "",
            "PAY_TERM": "",
            "BRANCH_ID": "3f329a7a-1496-490c-850e-dff7d3d34bf2",
            "BUYER_FAX": "",
            "DALI_DATE": "",
            "SELLER_ID": "",
            "SHIPTO_ID": "",
            "TENANT_ID": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
            "BUYER_NAME": "บริษัท ดูบายบอท จำกัด (สำนักงานใหญ่)",
            "DOC_REMARK": "",
            "DOC_STATUS": "NEW",
            "EMAIL_FLAG": "Y",
            "PAYEE_NAME": "",
            "PAYER_NAME": "",
            "PDF_BASE64": "",
            "REMARK_INV": "",
            "SELLER_FAX": "",
            "XML_BASE64": "",
            "DOC_CONTENT": "",
            "DOC_GLOB_ID": "",
            "DOC_SUBJECT": "",
            "INVOICEE_ID": "",
            "INVOICER_ID": "",
            "SELLER_NAME": "บริษัท คัสเวย์ เอ็มไพร์ จำกัด",
            "SHIPFROM_ID": "",
            "SHIPTO_NAME": "บริษัท ดูบายบอท จำกัด",
            "TENANT_CODE": "dt2024046",
            "BUYER_BRANCH": "00000",
            "BUYER_TAX_ID": "0105565020301",
            "DOC_PDF_FLAG": "",
            "DOC_PDF_SIZE": "",
            "DOC_XML_FLAG": "",
            "PAYEE_TAX_ID": "",
            "PAYER_TAX_ID": "",
            "REMARK_OTHER": "",
            "BUYER_ADDRESS": "245/68 ซอยรามคำแหง 112 ถนนรามคำแหง แขวงสะพานสูง เขตสะพานสูง กรุงเทพมหานคร 10240",
            "BUYER_GLOB_ID": "",
            "INVOICEE_NAME": "",
            "INVOICER_NAME": "",
            "PAYEE_ADDRESS": "",
            "PAYEE_GLOB_ID": "",
            "PAYER_ADDRESS": "",
            "PAYER_GLOB_ID": "",
            "SELLER_BRANCH": "00000",
            "SELLER_TAX_ID": "0105559130701",
            "SHIPFROM_NAME": "",
            "TAX_TYPE_CODE": "VAT",
            "BUYER_ADDRESS2": "",
            "BUYER_ORDER_ID": "",
            "BUYER_POSTCODE": "10240",
            "BUYER_TYPE_TAX": "TXID",
            "DOC_ISSUE_DATE": "2025-02-06T00:00:00+07:00",
            "MONEY_DIFF_AMT": "",
            "PAYEE_ADDRESS2": "",
            "PAYEE_POSTCODE": "",
            "PAYEE_TYPE_TAX": "",
            "PAYER_ADDRESS2": "",
            "PAYER_POSTCODE": "",
            "PAYER_TYPE_TAX": "",
            "REASON_DISPLAY": "",
            "REMARK_OTHER_2": "",
            "REMARK_OTHER_3": "",
            "SELLER_ADDRESS": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230",
            "SELLER_GLOB_ID": "",
            "SHIPTO_ADDRESS": "****** กาญจนาภิเษก แขวง บางแค เขต บางแค กทม 1... *หมายเหตุ: ออเดอร์ 250126G3DE8WES-J2 ออกเอกสารพร้อมกัน 2 ฉบับ คือ RT-250126G3DE8WES-J2 เฉพาะสินค้ามี VAT และ R-250126G3DE8WES-J2 เฉพาะสินค้าไม่มี VAT ",
            "SHIPTO_GLOB_ID": "",
            "BUYER_BIRTHDATE": "",
            "DOC_CREATE_DATE": "2025-02-06T15:18:11.956657+07:00",
            "INVOICEE_TAX_ID": "",
            "INVOICER_TAX_ID": "",
            "SELLER_ADDRESS2": "",
            "SELLER_POSTCODE": "10230",
            "SELLER_TYPE_TAX": "TXID",
            "SHIPTO_ADDRESS2": "",
            "SHIPTO_POSTCODE": "",
            "BUYER_COUNTRY_ID": "TH",
            "DOC_PURPOSE_CODE": "",
            "INVOICEE_ADDRESS": "",
            "INVOICEE_GLOB_ID": "",
            "INVOICER_ADDRESS": "",
            "INVOICER_GLOB_ID": "",
            "ORIGINAL_INVOICE": "",
            "PAYEE_COUNTRY_ID": "",
            "PAYER_COUNTRY_ID": "",
            "SHIPFROM_ADDRESS": "",
            "SHIPFROM_GLOB_ID": "",
            "BUYER_BRANCH_NAME": "00000",
            "BUYER_BUILDING_NO": "",
            "BUYER_DOCUMENT_ID": "",
            "INVOICEE_ADDRESS2": "",
            "INVOICEE_POSTCODE": "",
            "INVOICEE_TYPE_TAX": "",
            "INVOICER_ADDRESS2": "",
            "INVOICER_POSTCODE": "",
            "INVOICER_TYPE_TAX": "",
            "PAYMENT_TYPE_CODE": "",
            "SELLER_COUNTRY_ID": "TH",
            "SHIPFROM_ADDRESS2": "",
            "SHIPFROM_POSTCODE": "",
            "SHIPTO_COUNTRY_ID": "",
            "BUYER_CONTACT_NAME": "",
            "DOC_PURPOSE_DETAIL": "",
            "PAYEE_CONTACT_NAME": "",
            "PAYER_CONTACT_NAME": "",
            "SELLER_BRANCH_NAME": "สำนักงานใหญ่",
            "SELLER_BUILDING_NO": "",
            "ALLOWANCE_TYPE_CODE": "",
            "BUYER_BUILDING_NAME": "",
            "BUYER_CONTACT_EMAIL": "<EMAIL>",
            "BUYER_CONTACT_PHONE": "0886604941",
            "BUYER_DELIVERY_TYPE": "",
            "BUYER_DOC_TYPE_CODE": "",
            "INVOICEE_COUNTRY_ID": "",
            "INVOICER_COUNTRY_ID": "",
            "OCCURRENCE_DATETIME": "",
            "PAYEE_CONTACT_EMAIL": "",
            "PAYEE_CONTACT_PHONE": "",
            "PAYER_CONTACT_EMAIL": "",
            "PAYER_CONTACT_PHONE": "",
            "PAYMENT_DESCRIPTION": "",
            "SELLER_CONTACT_NAME": "",
            "SHIPFROM_COUNTRY_ID": "",
            "SHIPTO_CONTACT_NAME": "",
            "TAX_CALCULATED_RATE": "7",
            "BUYER_DOC_ISSUE_DATE": "",
            "INVOICER_BUILDING_NO": "",
            "PAYMENT_DUE_DATETIME": "",
            "SELLER_BUILDING_NAME": "",
            "SELLER_CONTACT_EMAIL": "",
            "SELLER_CONTACT_PHONE": "0804000500",
            "SELLER_DISTRICT_CODE": "",
            "SELLER_DISTRICT_NAME": "",
            "SELLER_PROVINCE_CODE": "",
            "SELLER_PROVINCE_NAME": "",
            "SHIPTO_CONTACT_EMAIL": "",
            "SHIPTO_CONTACT_PHONE": "",
            "ALLOWANCE_REASON_CODE": "",
            "ALLOWANCE_REASON_NAME": "",
            "BUYER_ORDER_TYPE_CODE": "",
            "INVOICEE_CONTACT_NAME": "",
            "INVOICER_CONTACT_NAME": "",
            "INVOICE_CURRENCY_CODE": "THB",
            "MONEY_CHARGE_TOTALAMT": "",
            "MONEY_ORIGINAL_AMOUNT": "",
            "SHIPFROM_CONTACT_NAME": "",
            "BUYER_ORDER_ISSUE_DATE": "",
            "INVOICEE_CONTACT_EMAIL": "",
            "INVOICEE_CONTACT_PHONE": "",
            "INVOICER_BUILDING_NAME": "",
            "INVOICER_CONTACT_EMAIL": "",
            "INVOICER_CONTACT_PHONE": "",
            "INVOICER_DISTRICT_CODE": "",
            "INVOICER_PROVINCE_CODE": "",
            "SHIPFROM_CONTACT_EMAIL": "",
            "SHIPFROM_CONTACT_PHONE": "",
            "ALLOWANCE_ACTUAL_AMOUNT": "",
            "SELLER_SUBDISTRICT_CODE": "",
            "SELLER_SUBDISTRICT_NAME": "",
            "BUYER_CONTACT_DEPARTMENT": "",
            "BUYER_DELIVERY_TYPE_CODE": "",
            "PAYEE_CONTACT_DEPARTMENT": "",
            "PAYER_CONTACT_DEPARTMENT": "",
            "INVOICER_SUBDISTRICT_CODE": "",
            "SELLER_CONTACT_DEPARTMENT": "",
            "SHIPTO_CONTACT_DEPARTMENT": "",
            "ALLOWANCE_CHARGE_INDICATOR": "",
            "INVOICEE_CONTACT_DEPARTMENT": "",
            "INVOICER_CONTACT_DEPARTMENT": "",
            "SHIPFROM_CONTACT_DEPARTMENT": "",
            "MONEY_EXCEPT_TAXBASIS_TOTALAMT": "",
            "BUYER_DOCUMENT_ID": "SVKBOL-002",
            "BUYER_DOC_ISSUE_DATE": "2024-03-26T00:00:00",
            "BUYER_DOC_TYPE_CODE": "T03",
            "MONEY_ORIGINAL_AMOUNT": 5500,
            "MONEY_LINE_TOTALAMOUNT": 166.36,
            "MONEY_DIFF_AMT": 0,
            "ALLOWANCE_SP_ACTUAL_AMOUNT": 10,
            "MONEY_ALLOWANCE_TOTALAMT": 50,
            "MONEY_TAXBASIS_TOTALAMT": 166.36,
            "MONEY_TAX_TOTALAMT": 11.64,
            "MONEY_GRAND_TOTALAMT": 178,
            "items": [
                {
                    "TRADE_LINE_ID": 1,
                    "PRODUCT_ID": "GT_1",
                    "PRODUCT_NAME": "Ghost-1",
                    "PRODUCT_CHARGEAMT": 69,
                    "PRODUCT_BILLED_QTY": 2,
                    "PRODUCT_ACTUALAMT": 30,
                    "PRODUCT_TAX_BASISAMT": 100.93,
                    "PRODUCT_TAX_CALAMT": 7.07,
                    "PRODUCT_SUM_TAXTOTALAMT": 7.07,
                    "PRODUCT_SUM_NETLINE_TOTALAMT": 108,
                    "PRODUCT_SUM_TOTALAMT": 108,
                },
                {
                    "TRADE_LINE_ID": 2,
                    "PRODUCT_ID": "GT_2",
                    "PRODUCT_NAME": "Ghost-2",
                    "PRODUCT_CHARGEAMT": 30,
                    "PRODUCT_BILLED_QTY": 3,
                    "PRODUCT_ACTUALAMT": 10,
                    "PRODUCT_TAX_BASISAMT": 74.77,
                    "PRODUCT_TAX_CALAMT": 5.23,
                    "PRODUCT_SUM_TAXTOTALAMT": 5.23,
                    "PRODUCT_SUM_NETLINE_TOTALAMT": 80,
                    "PRODUCT_SUM_TOTALAMT": 80,
                },
            ],
        }

        checker = D1A_CN_CheckSerializer(data=data)
        ok = checker.is_valid()

        pprint(checker.errors)
        self.assertEqual(ok, True)

    @tag("tax-document-checker")
    def test_rct_document_pass(self):
        data = {
            "ALLOWANCE_ACTUAL_AMOUNT": "",
            "ALLOWANCE_SP_ACTUAL_AMOUNT": "0.0",
            "BRANCH_ID": "3f329a7a-1496-490c-850e-dff7d3d34bf2",
            "BUYER_ADDRESS": "245/68 ซอยรามคำแหง 112 ถนนรามคำแหง แขวงสะพานสูง เขตสะพานสูง กรุงเทพมหานคร 10240",
            "BUYER_BRANCH": "00000",
            "BUYER_BRANCH_NAME": "",
            "BUYER_CONTACT_EMAIL": "<EMAIL>",
            "BUYER_CONTACT_NAME": "",
            "BUYER_CONTACT_PHONE": "0886604941",
            "BUYER_COUNTRY_ID": "TH",
            "BUYER_NAME": "บริษัท ดูบายบอท จำกัด (สำนักงานใหญ่)",
            "BUYER_POSTCODE": "10240",
            "BUYER_TAX_ID": "0105565020301",
            "BUYER_TYPE_TAX": "TXID",
            "DOC_CREATE_DATE": "2025-02-06T17:18:32.526382+07:00",
            "DOC_GLOB_ID": "",
            "DOC_ID": "R-250126G3DE8WES-J5",
            "DOC_TYPE": "T01",
            "DOC_ISSUE_DATE": "2025-02-06T00:00:00+07:00",
            "DOC_NAME": "ใบเสร็จรับเงิน",
            "EMAIL_FLAG": "Y",
            "MONEY_ALLOWANCE_TOTALAMT": "0.0",
            "MONEY_CHARGE_TOTALAMT": "",
            "MONEY_DIFF_AMT": "",
            "MONEY_EXCEPT_TAXBASIS_TOTALAMT": "",
            "MONEY_GRAND_TOTALAMT": "338.0",
            "MONEY_LINE_TOTALAMOUNT": "338.0",
            "MONEY_ORIGINAL_AMOUNT": "",
            "MONEY_TAXBASIS_TOTALAMT": "338.0",
            "MONEY_TAX_TOTALAMT": "0",
            "OCCURRENCE_DATETIME": "",
            "ORIGINAL_INVOICE": "",
            "PDF_BASE64": "",
            "RD_FLAG": "N",
            "REASON_DISPLAY": "",
            "REMARK_INV": "",
            "REMARK_OTHER": "N = รายการสินค้าที่ไม่มีภาษีมูลค่าเพิ่ม",
            "REMARK_OTHER_2": "",
            "REMARK_OTHER_3": "",
            "SELLER_ADDRESS": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230",
            "SELLER_ADDRESS2": "",
            "SELLER_BRANCH": "00000",
            "SELLER_BRANCH_NAME": "สำนักงานใหญ่",
            "SELLER_BUILDING_NAME": "",
            "SELLER_BUILDING_NO": "",
            "SELLER_CONTACT_DEPARTMENT": "",
            "SELLER_CONTACT_EMAIL": "",
            "SELLER_CONTACT_NAME": "",
            "SELLER_CONTACT_PHONE": "0804000500",
            "SELLER_COUNTRY_ID": "TH",
            "SELLER_DISTRICT_CODE": "",
            "SELLER_DISTRICT_NAME": "",
            "SELLER_FAX": "",
            "SELLER_GLOB_ID": "",
            "SELLER_ID": "",
            "SELLER_NAME": "บริษัท คัสเวย์ เอ็มไพร์ จำกัด",
            "SELLER_POSTCODE": "10230",
            "SELLER_PROVINCE_CODE": "",
            "SELLER_PROVINCE_NAME": "",
            "SELLER_SUBDISTRICT_CODE": "",
            "SELLER_SUBDISTRICT_NAME": "",
            "SELLER_TAX_ID": "0105559130701",
            "SELLER_TYPE_TAX": "TXID",
            "SHIPFROM_ADDRESS": "",
            "SHIPFROM_ADDRESS2": "",
            "SHIPFROM_CONTACT_DEPARTMENT": "",
            "SHIPFROM_CONTACT_EMAIL": "",
            "SHIPFROM_CONTACT_NAME": "",
            "SHIPFROM_CONTACT_PHONE": "",
            "SHIPFROM_COUNTRY_ID": "",
            "SHIPFROM_GLOB_ID": "",
            "SHIPFROM_ID": "",
            "SHIPFROM_NAME": "",
            "SHIPFROM_POSTCODE": "",
            "SHIPTO_ADDRESS": "****** กาญจนาภิเษก แขวง บางแค เขต บางแค กทม 101... *หมายเหตุ: ออเดอร์ 250126G3DE8WES-J5 ออกเอกสารพร้อมกัน 2 ฉบับ คือ RT-250126G3DE8WES-J5 เฉพาะสินค้ามี VAT และ R-250126G3DE8WES-J5 เฉพาะสินค้าไม่มี VAT ",
            "SHIPTO_ADDRESS2": "",
            "SHIPTO_CONTACT_DEPARTMENT": "",
            "SHIPTO_CONTACT_EMAIL": "",
            "SHIPTO_CONTACT_NAME": "",
            "SHIPTO_CONTACT_PHONE": "",
            "SHIPTO_COUNTRY_ID": "",
            "SHIPTO_GLOB_ID": "",
            "SHIPTO_ID": "",
            "SHIPTO_NAME": "บริษัท ดูบายบอท จำกัด",
            "SHIPTO_POSTCODE": "",
            "SO_NO": "",
            "TAX_BASIS_AMOUNT": "338.0",
            "TAX_CALCULATED_AMOUNT": "0",
            "TAX_CALCULATED_RATE": "0",
            "TAX_TYPE_CODE": "VAT",
            "TENANT_CODE": "dt2024046",
            "TENANT_ID": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
            "WH_NO": "",
            "XML_BASE64": "",
            "items": [
                {
                    "PRODUCT_ACTUALAMT": "0.0",
                    "PRODUCT_BILLED_QTY": "1",
                    "PRODUCT_CHAREGE_INDICATOR": "",
                    "PRODUCT_CHARGEAMT": "199.0",
                    "PRODUCT_ID": "H013-NesOxy3",
                    "PRODUCT_NAME": "N [Easy E-Receipt] Nespresso แคปซูลทำความสะอาด แพค 3 ชิ้น NesOXY Cleaning Capsules และ NesOXY Descaler(H013-NesOxy3)",
                    "PRODUCT_SUM_NETLINE_TOTALAMT": "199.0",
                    "PRODUCT_SUM_TAXTOTALAMT": "0.0",
                    "PRODUCT_SUM_TOTALAMT": "199.0",
                    "PRODUCT_TAX_BASISAMT": "199.0",
                    "PRODUCT_TAX_CALAMT": "0.0",
                    "PRODUCT_TAX_CALRATE": "0",
                    "PRODUCT_UNIT": "PCS",
                    "TRADE_LINE_ID": "1",
                },
                {
                    "PRODUCT_ACTUALAMT": "0.0",
                    "PRODUCT_BILLED_QTY": "1",
                    "PRODUCT_CHARGEAMT": "139.0",
                    "PRODUCT_ID": "H014-NesDescaler",
                    "PRODUCT_NAME": "N [Easy E-Receipt] Nespresso แคปซูลทำความสะอาด แพค 3 ชิ้น NesOXY Cleaning Capsules และ NesOXY Descaler(H014-NesDescaler)",
                    "PRODUCT_SUM_NETLINE_TOTALAMT": "139.0",
                    "PRODUCT_SUM_TAXTOTALAMT": "0.0",
                    "PRODUCT_SUM_TOTALAMT": "139.0",
                    "PRODUCT_TAX_BASISAMT": "139.0",
                    "PRODUCT_TAX_CALAMT": "0.0",
                    "PRODUCT_TAX_CALRATE": "0",
                    "PRODUCT_TAX_TYPE_CODE": "",
                    "PRODUCT_TYPE_CODE": "",
                    "PRODUCT_UNIT": "PCS",
                    "PRODUCT_UNITQTY": "",
                    "TRADE_LINE_ID": "2",
                },
            ],
        }

        checker = D1A_NONVAT_CheckSerializer(data=data)
        ok = checker.is_valid()

        pprint(checker.errors)
        self.assertEqual(ok, True)
