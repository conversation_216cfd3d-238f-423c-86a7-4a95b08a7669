from rest_framework import serializers

FIELD_DICT = {
    "PRODUCT_CHARGEAMT": "ราคาต่อหน่วย",
    "PRODUCT_BILLIED_QTY": "จํานวนสินค้า",
    "PRODUCT_ACTUALAMT": "มูลค่าส่วนลดหรือค่าธรรมเนียม ต่อรายการ",
    "PRODUCT_SUM_NETLINE_TOTALAMT": "มูลค่าสินค้ารวม (ไม่รวมภาษี)",
    "ALLOWANCE_SP_ACTUAL_AMOUNT": "ส่วนลดท้ายบิล",
    "MONEY_GRAND_TOTALAMT": "ยอดเงินรวม/ ยอดเงินรวม ภาษีมูลค่าเพิ่ม",
    "MONEY_ALLOWANCE_TOTALAMT": "ส่วนลดทั้งหมด",
    "MONEY_TAXBASIS_TOTALAMT": "รวมมูลค่าสินค้า/บริการ (รวมภาษีมูลค่าเพิ่ม)",
    "MONEY_TAX_TOTALAMT": "มูลค่าที่นำมาคิดภาษีมูลค่าเพิ่ม",
    "MONEY_LINE_TOTALAMOUNT": "รวมมูลค่าตามรายการ/มูลค่าที่ถูกต้อง",
    "SUM_PRODUCT_TOTALAMT": "รวมมูลค่าสินค้า/บริการ (รวมภาษีมูลค่าเพิ่ม)",
    "SUM_PRODUCT_DISCOUNT": "รวมส่วนลดของสินค้า",
    "SUM_PRODUCT_PRICE_BEFORE_DISCOUNT": "รวมมูลค่าสินค้า/บริการ ยังไม่หักส่วนลดรายสินค้า",
}


def get_field_description(fields: list[str]):
    desc = {}
    for f in fields:
        desc[f] = FIELD_DICT[f]
    return desc


def field_detail_and_desc(attrs: dict, fields: list[str]):
    from pprint import pprint

    result = {"details": {}, "field_descriptions": {}}

    for f in fields:
        result["field_descriptions"][f] = FIELD_DICT[f]
        result["details"][f] = attrs.get(f)

    return result


def positive_number(value):
    if value < 0:
        raise serializers.ValidationError("This field must greater than 0")

def net_zero(value):
    if not value == 0:
        raise serializers.ValidationError("This field must equal to 0")

def not_zero(value):
    if value == 0:
        raise serializers.ValidationError("This field must not be zero")


def eq(value1, value2, tolerance=0.05):
    diff = abs(value1 - value2)
    return diff < tolerance
    
