from rest_framework import serializers

from etax.error_prevention.utils import eq, not_zero, positive_number
from etax.models import VAT_TYPE_EXC, VAT_TYPE_INC


class NullableFloatField(serializers.FloatField):
    def to_internal_value(self, data):
        if data is None:
            return self.default
        return super().to_internal_value(data)


class OrderJsonListCheckSerializer(serializers.Serializer):
    # Tax Calculation
    # -------------------------------------
    pricepernumber = serializers.FloatField(validators=[positive_number])
    number = serializers.IntegerField(validators=[positive_number, not_zero])
    seller_discount = serializers.FloatField(
        validators=[positive_number], required=False, default=0
    )
    totalprice = serializers.FloatField(validators=[positive_number])
    eso_vatpercent = serializers.FloatField(
        validators=[positive_number], required=False, default=7, allow_null=True
    )
    vatpercent = serializers.FloatField(
        validators=[positive_number], required=False, default=7, allow_null=True
    )

    # TEMP: Uncomment this after dobysync fix shopee order issue
    # def validate(self, attrs):
    #     pricepernumber = attrs["pricepernumber"]
    #     number = attrs["number"]
    #     seller_discount = attrs["seller_discount"]
    #     totalprice = attrs["totalprice"]

    #     errors = []

    #     if not eq((pricepernumber * number) - seller_discount, totalprice, tolerance=0.05):
    #         errors.append(
    #             {
    #                 "totalprice": "(pricepernumber * number) - seller_discount != totalprice",
    #                 "details": {
    #                     "pricepernumber": pricepernumber,
    #                     "number": number,
    #                     "seller_discount": seller_discount,
    #                     "totalprice": totalprice,
    #                 }
    #             }
    #         )

    #     if errors:
    #         raise serializers.ValidationError(errors)

    #     return attrs


class OrderJsonCheckSerializer(serializers.Serializer):
    list = OrderJsonListCheckSerializer(many=True)
    shippingamount = serializers.FloatField(validators=[positive_number])
    discountamount = serializers.FloatField()
    amount = serializers.FloatField(validators=[positive_number, not_zero])
    status = serializers.CharField()

    def validate(self, attrs):
        vattype = self.context.get("vattype", VAT_TYPE_INC)
        order_items = attrs["list"]
        shippingamount = attrs["shippingamount"]
        discountamount = attrs["discountamount"]
        amount = attrs["amount"]

        errors = []

        is_all_item_vat7 = all([item["eso_vatpercent"] == 7 for item in order_items])

        if is_all_item_vat7:
            if vattype == VAT_TYPE_INC:
                total_price = sum(
                    [order_item["totalprice"] for order_item in order_items]
                )
                if round(total_price + shippingamount - discountamount, 2) != round(
                    amount, 2
                ):
                    errors.append(
                        {
                            "amount": "total_price + shippingamount - discountamount != amount",
                            "details": {
                                "total_price": total_price,
                                "shippingamount": shippingamount,
                                "discountamount": discountamount,
                                "amount": amount,
                            },
                        }
                    )
            if vattype == VAT_TYPE_EXC:
                total_price = sum(
                    [order_item["totalprice"] for order_item in order_items]
                )
                if round(
                    (total_price + shippingamount - discountamount) * 1.07, 2
                ) != round(amount, 2):
                    errors.append(
                        {
                            "amount": "(total_price + shippingamount - discountamount) * 1.07 != amount",
                            "details": {
                                "total_price": total_price,
                                "shippingamount": shippingamount,
                                "discountamount": discountamount,
                                "amount": amount,
                            },
                        }
                    )

        if errors:
            raise serializers.ValidationError(errors)

        return attrs

    def validate_status(self, value):
        if value in ["Voided", "Partial Voided"]:
            raise serializers.ValidationError(
                "Documents cannot be created if the status is Voided or Partially Voided."
            )
