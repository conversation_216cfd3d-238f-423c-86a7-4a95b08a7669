from django.test import SimpleTestCase, tag

from rest_framework import serializers
from etax.error_prevention.order_json_error_prevention import OrderJsonCheckSerializer, not_zero, positive_number


# python manage.py test --tag checker

class TestOrderJsonChecker(SimpleTestCase):
    @tag('order-json-checker')
    def test_order_json_checker_pass(self):
        data = {
            'list': [
                {
                    'sku': '123',
                    'pricepernumber': 100,
                    'number': 10,
                    'seller_discount': 1,
                    'totalprice': 999,
                },
                {
                    'sku': '124',
                    'pricepernumber': 19,
                    'number': 2,
                    'seller_discount': 10,
                    'totalprice': 28
                }
            ],
            'shippingamount': 10,
            'discountamount': 5,
            'amount': 1032,
            'status': 'Success'
        }

        checker = OrderJsonCheckSerializer(data=data)
        ok = checker.is_valid()

        self.assertEqual(ok, True)

    @tag('order-json-checker')
    def test_order_json_checker_error(self):
        data = {
            'list': [
                {
                    'sku': '123',
                    'pricepernumber': -100,
                    'number': 0,
                    'seller_discount': 1,
                    'totalprice': 999
                },
                {
                    'sku': '124',
                    'pricepernumber': 9,
                    'number': 2,
                    'seller_discount': 10,
                    'totalprice': 28
                }
            ],
            'shippingamount': -10,
            'discountamount': 0,
            'amount': 10,
            'status': 'Voided'
        }

        checker = OrderJsonCheckSerializer(data=data)
        ok = checker.is_valid()

        self.assertEqual(ok, False)
        from pprint import pprint
        pprint(checker.errors)

        self.assertTrue(True)


    @tag('order-json-checker')
    def test_positive_number(self):
        with self.assertRaises(serializers.ValidationError):
            positive_number(-10)

        result = positive_number(0)
        self.assertIsNone(result)
        
        result = positive_number(10)
        self.assertIsNone(result)

    @tag('order-json-checker')
    def test_not_zero(self):
        with self.assertRaises(serializers.ValidationError):
            not_zero(0)

        self.assertIsNone(not_zero(10))
        