from rest_framework import permissions


class CanCreateETaxDocument(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.has_perm("etax.add_taxdocument")


class CanViewETaxDocument(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.has_perm("etax.view_taxdocument")


class CanUpdateETaxDocument(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.has_perm("etax.change_taxdocument")


class CanDeleteETaxDocument(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.has_perm("etax.delete_taxdocument")
