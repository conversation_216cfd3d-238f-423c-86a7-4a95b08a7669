import base64
import json
import jwt

from typing import Any, List

from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework import serializers
from rest_framework.response import Response

from django.utils import timezone

from companies.models.models import Company
from core.responses.error_responses import ResponseError
from core.serializers.tax_serializers import BranchNoField
from picking.models import Pick<PERSON>rder
from picking.serializers.zort import ZortOrderSerializer
from utils.ignore_exception import try_index


class EasyOrderFromTokenAPIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    class Validator(serializers.Serializer):
        token = serializers.CharField()

    PATTERNS = {
        # black canyon company
        "001": {
            "company_name": "Black Canyon",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
        "002": {
            "company_name": "บริษัท เอฟ แอนด์ บี อลิอันซ์ จำกัด",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
        "003": {
            "company_name": "บริษัท ศรัญญา คอฟฟี่เฮาส์ จำกัด",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
        "004": {
            "company_name": "บริษัท ฟู้ด โซไซตี้ จำกัด",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
        "005": {
            "company_name": "บริษัท สบาย อีทเทอรี่ จำกัด",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
        "006": {
            "company_name": "บริษัท บี.บี.ซี (เชียงใหม่) จำกัด",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
        "007": {
            "company_name": "บริษัท คอฟฟี่ทาเลนท์ จำกัด",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
        "CXX": {
            "company_name": "All Company",
            "patterns": [
                {"type": float, "key": "item_amount"},
                {"type": float, "key": "service_charge"},
                {"type": str, "key": "number"},
                {"type": float, "key": "amount"},
                {"type": float, "key": "vat"},
                {"type": str, "key": "branch_number"},
                {"type": str, "key": "remark"},
                {"type": str, "key": "ref_tax_invoice_number", "default": ""},
            ],
        },
    }

    def post(self, request):
        """
        create order from easy order token
        """
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data
        token = data.get("token")

        try:
            public_decode = self.decode_public_token(token)
            list_decode = public_decode.get("data", [])
        except Exception:
            return ResponseError("UNABLE_TO_DECODE_TOKEN")

        if type(list_decode) is not list or len(list_decode) < 1:
            return ResponseError("INVALID_TOKEN_PAYLOAD")

        if not (list_decode[0] in self.PATTERNS or list_decode[0].startswith("C")):
            return ResponseError("INVALID_COMPANY_CODE")

        selected_pattern = None

        if list_decode[0].startswith("C"):
            selected_pattern = self.PATTERNS["CXX"]["patterns"]
            company = Company.objects.get(id=list_decode[0][1:])
        else:
            selected_pattern = self.PATTERNS[list_decode[0]]["patterns"]
            company = Company.objects.get(
                name=self.PATTERNS[list_decode[0]]["company_name"]
            )

        try:
            # Decode the token
            jwt.decode(
                token, company.get_setting("EASY_ETAX_TOKEN"), algorithms=["HS256"]
            )
        except jwt.InvalidTokenError:
            return ResponseError(
                "INVALID_JWT_SECRET", "Can't verify with EASY_ETAX_TOKEN"
            )

        try:
            transformed_token = self.transform_token(list_decode, selected_pattern)
        except ValueError as e:
            return ResponseError("INCORRECT_TOKEN_PAYLOAD", str(e))

        pick_order = PickOrder.objects.filter(
            company=company,
            order_number=transformed_token["number"],
        ).first()

        if not pick_order:
            pick_order = self.create_order_from_token(company, transformed_token)

        return Response(
            {
                "order_uuid": pick_order.uuid,
                "company_uuid": company.uuid,
            }
        )

    @classmethod
    def decode_public_token(cls, token):
        # Split the token into its three parts (header, payload, signature)
        header, payload, signature = token.split(".")

        # Decode the base64-encoded parts (add padding if necessary)
        decoded_payload = base64.urlsafe_b64decode(payload + "==").decode("utf-8")

        # Convert the decoded payload to JSON
        payload_json = json.loads(decoded_payload)
        return payload_json

    def transform_token(self, decode_tokens: List[Any], pattern):
        transformed_token = {}
        for i in range(len(pattern)):
            try:
                key = pattern[i]["key"]
                dtype = pattern[i]["type"]
                default_value = pattern[i].get("default", None)

                try:
                    value = decode_tokens[i + 1]
                except IndexError:
                    value = default_value

                transformed_token[key] = dtype(value)
            except Exception:
                raise ValueError(
                    f"Invalid type for {pattern[i]['key']} data {decode_tokens[i+1]}"
                )
        return transformed_token

    def create_order_from_token(self, company, transformed_token):
        order_json = {
            "number": transformed_token.get("number"),
            "remark": transformed_token.get("remark"),
            "status": "Pending",
            "saleschannel": transformed_token.get("branch_number", ""),
            "customername": "",
            "paymentstatus": "Paid",
            "discountamount": 0,
            "payments": [],
            "shippingchannel": "",
            "shippingname": "",
            "shippingaddress": "",
            "trackingno": "",
            "createdatetimeString": timezone.localtime().isoformat(),
            "isCOD": False,
            "amount": transformed_token.get("amount"),
            "orderdateString": timezone.localtime().isoformat(),
            "paymentamount": transformed_token.get("amount"),
            "extra": {
                "branch_number": transformed_token.get("branch_number", ""),
                "ref_tax_invoice_number": transformed_token.get(
                    "ref_tax_invoice_number"
                ),
            },
            "list": [
                {
                    "sku": "",
                    "name": "ค่าอาหารและเครื่องดื่ม",
                    "number": 1,
                    "totalprice": transformed_token.get("item_amount"),
                    "discountamount": 0,
                    "pricepernumber": transformed_token.get("item_amount"),
                    "seller_discount": 0,
                }
            ],
        }

        if transformed_token.get("service_charge"):
            order_json["list"].append(
                {
                    "sku": "",
                    "name": "ค่าบริการ",
                    "number": 1,
                    "totalprice": transformed_token.get("service_charge"),
                    "discountamount": 0,
                    "pricepernumber": transformed_token.get("service_charge"),
                    "seller_discount": 0,
                }
            )

        zort_order = ZortOrderSerializer(data=order_json)
        zort_order.is_valid()
        if zort_order.errors:
            raise ValueError(f"Invalid Zort Order: {zort_order.errors}")

        pick_order_created = PickOrder.create_from_zort_order(
            company=company,
            order=zort_order.validated_data,
            order_oms="easy-order",
        )
        return pick_order_created


class EasyOrderFromToken2APIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    TOKEN_FIELDS = [
        "company",
        "food_price",
        "service_charge",
        "order_number",
        "discount",
        "branch_number",
        "remark",
        "abb_number",
        "order_date",
        "special_command",
    ]

    class Validator(serializers.Serializer):
        company = serializers.CharField()
        food_price = serializers.DecimalField(max_digits=10, decimal_places=4)
        service_charge = serializers.DecimalField(max_digits=10, decimal_places=4, default=0)
        order_number = serializers.CharField()
        discount = serializers.DecimalField(max_digits=10, decimal_places=4, default=0)
        branch_number = BranchNoField()
        remark = serializers.CharField(allow_blank=True)
        abb_number = serializers.CharField(allow_blank=True)
        order_date = serializers.DateField(input_formats=["%Y%m%d"])
        special_command = serializers.CharField(allow_blank=True, allow_null=True)

        def validate_company(self, value):
            if not value.startswith("C"):
                raise serializers.ValidationError("company code must start with C")

            company_id = value[1:]
            company = Company.objects.filter(id=company_id).first()
            if not company:
                raise serializers.ValidationError(
                    f"Could not find Company with id={company_id}"
                )

            return company

    def post(self, request):
        # Extract data from token
        token = request.data.get("token")
        token_data = jwt.decode(
            jwt=token, algorithms=["HS256"], options={"verify_signature": False}
        )

        if not token_data.get("data"):
            return ResponseError("INVALID_TOKEN_PAYLOAD")

        data = {}
        for i, field in enumerate(self.TOKEN_FIELDS):
            data[field] = try_index(token_data['data'], i)

        # Validate token data
        validator = self.Validator(data=data)
        if not validator.is_valid():
            errors = []
            for key, error in validator.errors.items():
                errors.append(
                    {
                        "index": self.TOKEN_FIELDS.index(key),
                        "field": key,
                        "error": error,
                    }
                )
            return Response(errors, status=400)
        
        vdata = validator.validated_data
        company: Company = vdata["company"]

        # Verify JWT token
        jwt_key = company.get_setting("EASY_ETAX_TOKEN")
        try:
            jwt.decode(jwt=token, key=jwt_key, algorithms=["HS256"])
        except Exception:
            return ResponseError("INVALID_JWT_SECRET")

        # Create pick order if not exists
        pick_order = PickOrder.objects.filter(
            company=company,
            order_number=vdata["order_number"],
        ).first()
        if not pick_order:
            pick_order = self.create_pick_order(vdata["company"], vdata)

        # Return order_uuid and company_uuid
        # so that web can redirect to the etax request page
        return Response(
            {
                "order_uuid": pick_order.uuid,
                "company_uuid": company.uuid,
            }
        )

    def create_pick_order(self, company, order_data) -> PickOrder:
        amount = order_data["food_price"] + order_data["service_charge"] - order_data["discount"]

        order_json = {
            "number": order_data.get("order_number"),
            "remark": order_data.get("remark"),
            "saleschannel": order_data.get("branch_number", ""),
            "amount": amount,
            "paymentamount": amount,
            "orderdateString": order_data.get("order_date").isoformat(),
            "discountamount": order_data.get("discount"),
            "extra": {
                "branch_number": order_data.get("branch_number", ""),
                "ref_tax_invoice_number": order_data.get("abb_number"),
            },
            "status": "Pending",
            "customername": "",
            "paymentstatus": "Paid",
            "payments": [],
            "shippingchannel": "",
            "shippingname": "",
            "shippingaddress": "",
            "trackingno": "",
            "createdatetimeString": timezone.localtime().isoformat(),
            "isCOD": False,
            "list": [
                {
                    "sku": "",
                    "name": "ค่าอาหารและเครื่องดื่ม",
                    "number": 1,
                    "totalprice": order_data.get("food_price"),
                    "discountamount": 0,
                    "pricepernumber": order_data.get("food_price"),
                    "seller_discount": 0,
                }
            ],
        }

        if order_data.get("service_charge"):
            order_json["list"].append(
                {
                    "sku": "",
                    "name": "ค่าบริการ",
                    "number": 1,
                    "totalprice": order_data.get("service_charge"),
                    "discountamount": 0,
                    "pricepernumber": order_data.get("service_charge"),
                    "seller_discount": 0,
                }
            )

        serializer = ZortOrderSerializer(data=order_json)
        if not serializer.is_valid():
            raise Exception(f"Invalid Zort Order: {serializer.errors}")

        return PickOrder.create_from_zort_order(
            company=company,
            order=serializer.validated_data,
            order_oms="easy-order",
        )


