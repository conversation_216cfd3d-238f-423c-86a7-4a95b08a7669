from collections import OrderedDict
from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON>entication
from rest_framework.authentication import TokenAuthentication
import re
from typing import Union
from rest_framework import serializers
from rest_framework.permissions import IsAdminUser
from rest_framework.views import APIView
from rest_framework.response import Response

from companies.models.models import Company
from etax.utils.buyer import is_buyer_empty
from picking.models import PickOrder
from etax.models import TaxDocument
from etax.serializers import TaxDocumentMonitorSerializer
from picking.serializers.serializers import PickOrderSerializer
from services.etax_invoice.d1a_schema import CompanyBuyer, D1aDocument, PersonalBuyer
from services.etax_invoice.etax_service import ETaxService

from utils.ignore_exception import get_or


def prepare_new_d1a_doc(tax_doc: TaxDocument, products_map: dict):
    etax_service = ETaxService.from_company(tax_doc.company)
    pick_order: PickOrder = tax_doc.pick_order
    pick_order.order_json = etax_service._prepare_order_json(pick_order, products_map)
    buyer = ETaxService.load_buyer(tax_doc.buyer)
    split_method = etax_service._check_required_split_tiv_rct(pick_order, products_map)

    if split_method == "tiv":
        new_d1a_doc = D1aDocument.prepare_d1a_tiv(pick_order, buyer)
    elif split_method == "rct":
        new_d1a_doc = D1aDocument.prepare_doa_rct(pick_order, buyer)
    elif split_method == "tiv_rct":
        result = etax_service._split_pickorder_tiv_rct(pick_order)
        split_remark = result["split_remark"]

        if tax_doc.doc_type == TaxDocument.DOCTYPE_RECEIPT:
            pick_order_vat0 = result["pick_order_vat0"]
            new_d1a_doc = D1aDocument.prepare_doa_rct(
                pick_order_vat0, buyer, split_remark=split_remark
            )
        elif tax_doc.doc_type == TaxDocument.DOCTYPE_TAX_INVOICE:
            pick_order_vat7 = result["pick_order_vat7"]
            new_d1a_doc = D1aDocument.prepare_d1a_tiv(
                pick_order_vat7, buyer, split_remark=split_remark
            )
        else:
            return None

    return new_d1a_doc


class TaxDocumentHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = TaxDocument
        fields = "__all__"

    history_user = serializers.SerializerMethodField()
    history_date = serializers.DateTimeField()
    history_type = serializers.CharField(source="get_history_type_display")
    history_id = serializers.IntegerField()

    def get_history_user(self, obj):
        if obj and obj.history_user:
            return obj.history_user.username
        return None


class MonitorETaxDocumentAPIView(APIView):
    permission_classes = [IsAdminUser]

    def post(self, request):
        company_uuid = request.data.get("company_uuid")
        order_uuid = request.data.get("order_uuid")

        pick_order = PickOrder.objects.get(company__uuid=company_uuid, uuid=order_uuid)
        tax_documents = []

        if pick_order:
            tax_documents = TaxDocument.objects.filter(pick_order=pick_order)

            for tax_doc in tax_documents:
                if not tax_doc.status_reason:
                    _, errors = ETaxService.check_order_d1a_json_data(
                        pick_order.order_json, tax_doc.doc_info
                    )
                    tax_doc.status_reason = errors

        order_json = OrderedDict(sorted(pick_order.order_json.items()))
        pick_order_serializer = PickOrderSerializer(pick_order)

        history = tax_doc.history.all()
        his_serializer = TaxDocumentHistorySerializer(history, many=True)

        etax_service = ETaxService.from_company(pick_order.company)
        for tax_doc in tax_documents:
            tax_doc.doc_info2 = prepare_new_d1a_doc(
                tax_doc,
                products_map=None,
            ).model_dump(mode="json")
            _, tax_doc.status_reason2 = etax_service.check_order_d1a_json_data(
                tax_doc.pick_order.order_json, tax_doc.doc_info2
            )

        tax_doc_serializer = TaxDocumentMonitorSerializer(tax_documents, many=True)
        return Response(
            {
                "pick_order": pick_order_serializer.data,
                "order_json": order_json,
                "documents": tax_doc_serializer.data,
                "history": his_serializer.data,
            }
        )


class EtaxRetryAPIView(APIView):
    authentication_classes = [JWTAuthentication, TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        tax_document = serializers.PrimaryKeyRelatedField(
            queryset=TaxDocument.objects.all()
        )
        send_email = serializers.BooleanField(default=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data
        tax_document = self.retry(
            vdata["tax_document"], vdata["send_email"], request.user.username
        )
        return Response({"status": tax_document.status})

    @staticmethod
    def retry(tax_document: TaxDocument, send_email: bool, create_by: str):
        company: Company = tax_document.company
        buyer = ETaxService.load_buyer(tax_document.buyer)

        if is_buyer_empty(buyer):
            buyer = ETaxService.buyer_from_pick_order(tax_document.pick_order)

        service = ETaxService.from_company(company)
        tax_document = service.retry_tax_document(
            tax_document,
            buyer=buyer,
            create_by=create_by,
            options={
                "block_email": not send_email,
                "override_doc_id": tax_document.doc_id,
            },
        )
        return tax_document


class EtaxCancelAPIView(APIView):
    authentication_classes = [JWTAuthentication, TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        tax_document = serializers.PrimaryKeyRelatedField(
            queryset=TaxDocument.objects.all()
        )
        send_email = serializers.BooleanField(default=True)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        tax_document: TaxDocument = vdata["tax_document"]
        company: Company = tax_document.company
        service = ETaxService.from_company(company)

        options = {}
        if vdata["send_email"] is False:
            options["override"] = {
                "BUYER_CONTACT_EMAIL": "",
                "EMAIL_FLAG": "N",
            }

        options["action_retry"] = True
        service.cancel_tax_document(tax_document, options=options)
        return Response({"status": tax_document.status})


class EtaxUpdateAPIView(APIView):
    authentication_classes = [JWTAuthentication, TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        tax_document = serializers.PrimaryKeyRelatedField(
            queryset=TaxDocument.objects.all()
        )
        send_email = serializers.BooleanField(default=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        tax_document = vdata["tax_document"]
        company = tax_document.company
        service = ETaxService.from_company(company)
        buyer = ETaxService.load_buyer(tax_document.buyer)
        send_email = vdata["send_email"]

        tax_document = service.update_tax_document(
            tax_document,
            buyer=buyer,
            create_by=request.user.username,
            options={
                "block_email": not send_email,
                "override_doc_id": tax_document.doc_id,
                "action_retry": True,
            },
        )

        return Response({"status": tax_document.status})


class EtaxCancelAndRenewAPIView(APIView):
    authentication_classes = [JWTAuthentication, TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        tax_document = serializers.PrimaryKeyRelatedField(
            queryset=TaxDocument.objects.all()
        )
        override = serializers.JSONField(required=False, default=None)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        tax_document = vdata["tax_document"]
        company = tax_document.company
        service = ETaxService.from_company(company)
        buyer = ETaxService.load_buyer(tax_document.buyer)

        if re.search(r"-E\d$", tax_document.doc_id):
            new_doc_id = re.sub(
                r"-E(\d+)$",
                lambda x: f"-E{int(x.group(1)) + 1}",
                tax_document.doc_id,
            )
        else:
            new_doc_id = tax_document.doc_id + "-E1"

        tax_document = service.cancel_tax_document(
            tax_document,
            cancel_by=request.user.username,
            options={"override": get_or(vdata, "override", {})},
        )

        tax_document = service.retry_tax_document(
            tax_document,
            buyer=buyer,
            create_by=request.user.username,
            options={
                "block_email": False,
                "override": {
                    "DOC_ID": new_doc_id,
                    **get_or(vdata, "override", {}),
                },
            },
        )

        return Response({"status": tax_document.status})
