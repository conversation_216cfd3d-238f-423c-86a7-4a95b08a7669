from django.http import FileResponse
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response

from companies.models.models import Company
from etax.models import TaxDocument
from picking.models import PickOrder
from services.google.drive.drive import GoogleDriveService
from rest_framework.permissions import AllowAny

class CheckDownloadTaxDocumentAPIView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = []

    class Validator(serializers.Serializer):
        company = serializers.CharField(required=True)
        order = serializers.CharField(required=True)

        def validate(self, attrs):
            attrs = super().validate(attrs)

            attrs["company"] = get_object_or_404(
                Company,
                uuid=attrs["company"],
            )
            attrs["order"] = get_object_or_404(
                PickOrder,
                uuid=attrs["order"],
                company=attrs["company"],
            )

            return attrs

    class CheckResultSerializer(serializers.Serializer):
        uuid = serializers.Char<PERSON>ield()
        doc_id = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        company: Company = vdata["company"]
        pick_order: PickOrder = vdata["order"]

        tax_docs = TaxDocument.objects.filter(
            pick_order=pick_order,
            company=company,
            status=TaxDocument.STATUS_SUCCESS,
        )
        serializer = self.CheckResultSerializer(tax_docs, many=True)
        return Response(serializer.data)


class DownloadTaxDocumentAPIView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = []

    class Validator(serializers.Serializer):
        company = serializers.CharField(required=True)
        tax_document = serializers.CharField(required=True)

        def validate(self, attrs):
            attrs = super().validate(attrs)
            attrs["company"] = get_object_or_404(Company, uuid=attrs["company"])
            attrs["tax_document"] = get_object_or_404(
                TaxDocument, uuid=attrs["tax_document"], company=attrs["company"]
            )
            return attrs

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        tax_document: TaxDocument = vdata["tax_document"]
        file_id = tax_document.doc_url.split("/d/")[1].split("/")[0]
        drive = GoogleDriveService()
        bytes_io = drive.download_file(file_id)

        return FileResponse(
            bytes_io,
            as_attachment=True,
            filename=f"{tax_document.doc_id}.pdf",
        )
