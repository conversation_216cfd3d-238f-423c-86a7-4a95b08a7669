from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import serializers

from companies.models.models import Company
from core.responses.error_responses import ResponseError
from etax.models import TaxDocument
from etax.utils.buyer import is_buyer_empty
from picking.models import PickOrder
from services.etax_invoice.etax_service import ETaxService
from utils.ignore_exception import get_or
import re


class AutoETaxTaskHandlerAPIView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        order_number = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        pick_order = get_object_or_404(
            PickOrder,
            order_number=data["order_number"],
            company=data["company"],
        )

        tax_doc_orig = TaxDocument.objects.filter(
            company_id=pick_order.company_id,
            doc_type=TaxDocument.DOCTYPE_TAX_INVOICE,
            pick_order=pick_order,
        ).first()

        if tax_doc_orig and tax_doc_orig.status in [
            TaxDocument.STATUS_SUCCESS,
            TaxDocument.STATUS_CANCEL,
        ]:
            return ResponseError("TAX_DOCUMENT_ALREADY_EXISTS")

        etax_service = ETaxService.from_company(data["company"])
        buyer = etax_service.buyer_from_pick_order(pick_order)
        if is_buyer_empty(buyer):
            return ResponseError("BUYER_MUST_NOT_BE_EMPTY")

        if tax_doc_orig:
            tax_doc = etax_service.retry_tax_document(
                tax_doc_orig, buyer, create_by="AUTO"
            )
        else:
            tax_doc, receipt_doc = etax_service.create_tax_documents(
                pick_order, buyer, create_by="AUTO"
            )

        return Response("ok")



class AutoCancelAndRenewETaxTaskAPIView(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        tax_document = serializers.PrimaryKeyRelatedField(
            queryset=TaxDocument.objects.all()
        )
        override = serializers.JSONField(required=False, default=None)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        vdata = validator.validated_data

        tax_document = vdata["tax_document"]
        if tax_document.status != TaxDocument.STATUS_SUCCESS:
            return ResponseError("INVALID_TAX_DOCUMENT_STATUS")

        company = tax_document.company
        service = ETaxService.from_company(company)
        buyer = ETaxService.load_buyer(tax_document.buyer)

        if re.search(r"-E\d$", tax_document.doc_id):
            new_doc_id = re.sub(
                r"-E(\d+)$",
                lambda x: f"-E{int(x.group(1)) + 1}",
                tax_document.doc_id,
            )
        else:
            new_doc_id = tax_document.doc_id + "-E1"

        tax_document = service.cancel_tax_document(
            tax_document,
            cancel_by="AUTO",
            options={"override": get_or(vdata, "override", {})},
        )

        tax_document = service.retry_tax_document(
            tax_document,
            buyer=buyer,
            create_by="AUTO",
            options={
                "block_email": False,
                "override": {
                    "DOC_ID": new_doc_id,
                    **get_or(vdata, "override", {}),
                },
            },
        )

        return Response({"status": tax_document.status})
