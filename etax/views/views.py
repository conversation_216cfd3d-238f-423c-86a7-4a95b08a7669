from collections import OrderedDict
from django.db import transaction
from typing import Any, List
from django.conf import settings
import jwt
import base64
import json
from rest_framework import serializers
from django.shortcuts import get_object_or_404
import sentry_sdk
from core.responses import ResponseError
from rest_framework.permissions import IsAdminUser
from rest_framework.views import APIView, status
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from companies.models.models import Company
from core.serializers.serializers import parse_date, start_of
from etax.models import TaxDocument
from etax.permissions import CanDeleteETaxDocument, CanCreateETaxDocument
from etax.serializers import (
    GetTaxDocumentSerializer,
    CompanyBuyerSerializer,
    PersonalBuyerSerializer,
    TaxDocumentMonitorSerializer,
    TaxDocumentSerializer,
    TaxInfoListSerializer,
)
from picking.models import PickOrder
from picking.services.webhook.etax_webhook import EtaxRequestWebhookService
from services.etax_invoice.d1a_schema import PersonalBuyer, CompanyBuyer
from services.etax_invoice.etax_service import ETaxService
from services.etax_invoice.peak_repository import PeakException, PeakRepository
from sms.services.messaging import MessagingService
from datetime import datetime, timedelta
from django.utils import timezone

from users.models import User
from services.sms.sms import format_phone_number0


# Create your views here.
class D1AsiaWebhookHandlerAPI(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        print(request.headers)
        print(request.data)
        return Response({"description": "success", "httpCode": 200}, status=200)


class GetCompanyFullDataAPIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        tax_id = request.data.get("tax_id")

        try:
            result = PeakRepository.get_tax_id_info(tax_id)
        except PeakException:
            return ResponseError("TAX_ID_NOT_FOUND")

        if not result:
            return ResponseError("TAX_ID_NOT_FOUND")

        result_serializer = TaxInfoListSerializer({"results": result})

        return Response(result_serializer.data)


class GetLastestTaxDocumentAPIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):

        serializer = GetTaxDocumentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        order_uuid = data.get("order_uuid", "")
        order_number = data.get("order_number", "")
        company = get_object_or_404(Company, uuid=data.get("company_uuid"))
        today = timezone.localdate()

        if order_number:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                order_number=order_number,
            ).first()

        else:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                uuid=order_uuid,
            ).first()

        if not pick_order:
            return ResponseError("ORDER_NOT_FOUND", status=status.HTTP_404_NOT_FOUND)

        # กรณี order ที่ถูกแยกออกมา และไม่ใช่ order ของ NOCNOC จะต้องเอา order หลักมาใช้
        pick_order = pick_order.get_main_order()

        if pick_order.order_json["status"] in ["Voided", "Partial Voided"]:
            return ResponseError("ORDER_IS_VOIDED")

        is_staff = request.data.get("is_staff")
        is_update = request.data.get("is_update")
        if not is_staff:
            if today > pick_order.order_date + timedelta(
                days=company.get_setting("ETAX_ORDER_OPEN_DAYS")
            ):
                return ResponseError("ORDER_IS_NO_LONGER_ELIGIBLE_FOR_ETAX_REQUEST")

        is_order_received_require = company.get_setting("ETAX_ALLOW_ON_ORDER_RECEIVED")
        if not is_staff and is_order_received_require:
            order_oms = pick_order.order_oms.lower()
            if "zort" not in order_oms:
                if not pick_order.order_json.get("is_confirm_received", False):
                    return ResponseError("ORDER_IS_NOT_RECEIVED")

        tax_doc_ids = TaxDocument.objects.filter(
            order_number=pick_order.order_number,
            company_id=company.id,
        ).values_list("id", flat=True)
        if tax_doc_ids:
            tax_document = TaxDocument.objects.get(id=max(tax_doc_ids))
        else:
            tax_document = None

        if (
            not is_staff
            and is_update
            and tax_document
            and not ETaxService.can_edit_etax_doc(tax_document)
        ):
            return ResponseError("TAX_DOCUMENT_TOO_MANY_EDIT_ATTEMTPS")

        cutoff_check = True
        if is_staff:
            cutoff_check = not company.get_setting("ETAX_BYPASS_CUTOFF_DATE_CHECK")

        if cutoff_check:
            cutoff_limit = company.get_setting("ETAX_RETRIEVAL_DAY")
            if tax_document:
                # กรณี แก้ไข ตรวจสอบวันที่เอกสาร
                issue_date = datetime.fromisoformat(
                    tax_document.doc_info["DOC_ISSUE_DATE"]
                )
                cut_off_date = ETaxService.get_cut_off_date(issue_date, cutoff_limit)
                if today >= cut_off_date:
                    return ResponseError("EDITING_ETAX_NOT_ALLOWED_DUE_TO_OVERDUE_DATE")
            else:
                # กรณี สร้างใหม่ ตรวจสอบวันที่ order
                cut_off_date = ETaxService.get_cut_off_date(
                    pick_order.order_date, cutoff_limit
                )
                if today >= cut_off_date:
                    return ResponseError("ORDER_IS_NO_LONGER_ELIGIBLE_FOR_ETAX_REQUEST")

        order_json = pick_order.order_json
        data = {}

        # customer tax data from platform
        data["customer_name"] = order_json.get("customername", "")
        data["customer_email"] = order_json.get("customeremail", "")
        data["customer_phone"] = format_phone_number0(
            order_json.get("customerphone", "")
        )
        data["customer_id_number"] = order_json.get("customeridnumber", "")
        data["customer_branch_name"] = order_json.get("customerbranchname", "")
        data["customer_branch_no"] = order_json.get("customerbranchno", "")
        data["customer_address"] = order_json.get("customeraddress", "")
        data["post_code"] = order_json.get("customerpostcode", "")

        # confirm customer data
        data["payment_amount"] = order_json.get("paymentamount", "")
        data["order_number"] = pick_order.order_number
        data["platform_name"] = str(order_json.get("integrationName", "")).lower()
        data["tax_document_uuid"] = tax_document.uuid if tax_document else None

        return Response(
            {
                "tax_document_uuid": tax_document.uuid if tax_document else None,
                "tax_customer_info": data,
            }
        )


class TaxDocumentCreateAPIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        is_personal = request.data.get("is_personal", False)
        if is_personal:
            serializer = PersonalBuyerSerializer(data=request.data)
        else:
            serializer = CompanyBuyerSerializer(data=request.data)

        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        if data.get("tax_document_uuid"):
            return ResponseError("TAX_DOCUMENT_ALREADY_EXISTS")

        company = Company.objects.get(uuid=data.get("company_uuid"))
        if not company:
            return ResponseError("COMPANY_DOES_NOT_EXISTS")
        order_uuid = data.get("order_uuid")

        if order_uuid:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                uuid=order_uuid,
            ).first()
        else:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                order_number=data.get("order_number"),
            ).first()

        if not pick_order:
            return ResponseError("ORDER_DOES_NOT_EXISTS")

        if TaxDocument.objects.filter(
            company_id=company.id,
            pick_order_id=pick_order.id,
            doc_type=TaxDocument.DOCTYPE_TAX_INVOICE,
        ).exists():
            return ResponseError("TAX_DOCUMENT_ALREADY_EXISTS")

        # Prepare common buyer information
        buyer_info = {
            "buyer_name": data.get("buyer_name"),
            "tax_id": data.get("tax_id"),
            "address": data.get("address"),
            "email": data.get("email"),
            "phone_number": data.get("phone_number"),
            "post_code": data.get("post_code"),
            "is_consent_marketing": data.get("is_consent_marketing"),
        }

        if is_personal:
            buyer = PersonalBuyer(**buyer_info)
        else:
            # Add company-specific information for company buyers
            company_info = {
                "company_name": data.get("company_name"),
                "branch_name": data.get("branch_name"),
                "branch_code": data.get("branch_code"),
                "post_code": data.get("post_code"),
                "is_consent_marketing": data.get("is_consent_marketing"),
            }
            # Merge common and company info
            buyer_info.update(company_info)
            buyer = CompanyBuyer(**buyer_info)

        create_by = data.get("create_by", "BUYER_MANUAL")

        # Using Service
        etax_service = ETaxService.from_company(company)
        etax_document = etax_service.create_tiv(pick_order, buyer, create_by)
        if etax_document.status == TaxDocument.STATUS_FAILED:
            sentry_sdk.capture_message(
                f"Failed to create tax document: {etax_document.company.name} - {etax_document.doc_id}",
            )
            # return ResponseError("TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE")

        return Response(data={"tax_document_uuid": etax_document.uuid})


class TaxDocumentUpdateAPIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        if request.data["is_personal"]:
            serializer = PersonalBuyerSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            data = serializer.validated_data
            buyer = ETaxService.load_buyer(data, PersonalBuyer)
        else:
            serializer = CompanyBuyerSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            data = serializer.validated_data
            buyer = ETaxService.load_buyer(data, CompanyBuyer)

        company = get_object_or_404(Company, uuid=data.get("company_uuid"))
        tax_document = get_object_or_404(
            TaxDocument,
            uuid=data.get("tax_document_uuid"),
        )
        if not ETaxService.can_edit_etax_doc(tax_document) and not request.data.get(
            "is_staff_update", False
        ):
            return ResponseError("TAX_DOCUMENT_NO_LONGER_EDITABLE")

        # Get the user who is updating the tax document
        user_uuid = request.data.get("user_uuid")
        if user_uuid:
            create_by = User.objects.get(uuid=user_uuid).username
        else:
            create_by = "BUYER"

        # Update Documents
        etax_service = ETaxService.from_company(company)
        tax_docs = TaxDocument.objects.filter(
            company_id=company.id,
            pick_order_id=tax_document.pick_order_id,
            doc_type__in=[TaxDocument.DOCTYPE_TAX_INVOICE, TaxDocument.DOCTYPE_RECEIPT],
        )

        success_tax_docs = tax_docs.filter(status=TaxDocument.STATUS_SUCCESS)
        for tax_doc in success_tax_docs:
            etax_document = etax_service.update_tax_document(tax_doc, buyer, create_by)
            if etax_document.status == TaxDocument.STATUS_FAILED:
                return ResponseError("TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE")

        failed_tax_docs = tax_docs.filter(status=TaxDocument.STATUS_FAILED)
        for tax_doc in failed_tax_docs:
            tax_doc = etax_service.retry_tax_document(tax_doc, buyer, create_by)
            if tax_doc.status == TaxDocument.STATUS_FAILED:
                return ResponseError("TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE")

        return Response({"message": "SUCCESS"})


class TaxDocumentGetDetailAPIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        tax_document_uuid = request.data.get("tax_document_uuid")
        tax_document = get_object_or_404(TaxDocument, uuid=tax_document_uuid)
        serializer = TaxDocumentSerializer(tax_document)
        return Response(serializer.data)


class TaxDocumentCancelAPIView(APIView):
    permission_classes = [CanDeleteETaxDocument]

    def post(self, request):
        tax_doc_ids = tax_document = TaxDocument.objects.filter(
            company_id=request.user.company_id,
            uuid=request.data.get("tax_document_uuid"),
            doc_type__in=[TaxDocument.DOCTYPE_TAX_INVOICE, TaxDocument.DOCTYPE_RECEIPT],
        ).values_list("id", flat=True)
        tax_document = TaxDocument.objects.get(id=max(tax_doc_ids))

        company: Company = tax_document.company
        etax_service = ETaxService.from_company(company)
        tax_docs = TaxDocument.objects.filter(
            company_id=company.id,
            pick_order_id=tax_document.pick_order_id,
            doc_type__in=[TaxDocument.DOCTYPE_TAX_INVOICE, TaxDocument.DOCTYPE_RECEIPT],
            status=TaxDocument.STATUS_SUCCESS,
        )

        for tax_doc in tax_docs:
            obj = etax_service.cancel_tax_document(
                tax_doc, cancel_by=request.user.username
            )
            if obj.status == TaxDocument.STATUS_FAILED:
                return ResponseError("TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE")

        return Response({"message": "SUCCESS"})


class SendETaxLinkViaSMSAPIView(APIView):
    permission_classes = [CanCreateETaxDocument]

    class Validator(serializers.Serializer):
        order_uuid = serializers.CharField()
        phone_number = serializers.CharField()

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data

        pick_order: PickOrder = get_object_or_404(
            PickOrder,
            company_id=request.user.company_id,
            uuid=data["order_uuid"],
        )
        pick_order.order_customerphone = data["phone_number"]
        pick_order.order_json["customerphone"] = data["phone_number"]
        pick_order.order_json["shippingphone"] = data["phone_number"]
        pick_order.save(update_fields=["order_customerphone", "order_json"])

        message_service = MessagingService(pick_order.company)
        message_service.send_etax_link_via_sms(pick_order)
        return Response({"message": "SUCCESS"})


class OrderHasETaxCheckAPIView(APIView):
    permission_classes = [CanCreateETaxDocument]

    def get(self, request, order_number):
        if TaxDocument.objects.filter(
            company_id=request.user.company_id, order_number=order_number
        ).exists():
            return ResponseError("TAX_DOCUMENT_ALREADY_CREATED")

        return Response(status=status.HTTP_200_OK)


class ETaxGetCompanySetting(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def get(self, request, cid):

        company: Company = get_object_or_404(Company, uuid=cid)

        keys = [
            "ETAX_ALLOW_ON_ORDER_RECEIVED",
            "ETAX_SHOW_DOBYBOT_CONTACT",
            "ETAX_SHOW_CONSENT_CHECKBOX",
            "ETAX_WELCOME_MESSAGE",
            "ETAX_ORDER_OPEN_DAYS",
            "ETAX_LOGO",
        ]

        etax_settings = {key: company.get_setting(key) for key in keys}

        return Response(data=etax_settings, status=status.HTTP_200_OK)


class TaxDocumentCreditNoteCreateAPIView(APIView):
    permission_classes = [CanCreateETaxDocument]

    class Validator(serializers.Serializer):
        tax_document_uuid = serializers.CharField()

    def post(self, request):
        serializer = self.Validator(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        company: Company = request.user.company

        tax_document: TaxDocument = TaxDocument.objects.filter(
            uuid=data.get("tax_document_uuid"),
            doc_type__in=[TaxDocument.DOCTYPE_TAX_INVOICE, TaxDocument.DOCTYPE_RECEIPT],
        ).last()

        if not tax_document:
            return ResponseError("TAX_DOCUMENT_NOT_FOUND")

        etax_service = ETaxService.from_company(company)

        tax_docs = TaxDocument.objects.filter(
            company_id=company.id,
            pick_order_id=tax_document.pick_order_id,
            doc_type__in=[TaxDocument.DOCTYPE_TAX_INVOICE, TaxDocument.DOCTYPE_RECEIPT],
            status=TaxDocument.STATUS_SUCCESS,
        )
        for tax_doc in tax_docs:
            if tax_doc.doc_type == TaxDocument.DOCTYPE_TAX_INVOICE:
                etax_service.create_cn(tax_doc, request.user.username)
            elif tax_doc.doc_type == TaxDocument.DOCTYPE_RECEIPT:
                etax_service.create_return_rtc(tax_doc, request.user.username)

        return Response(
            {
                "message": "SUCCESS",
            }
        )


class TaxDocumentReceiptCreateAPIView(APIView):
    def post(self, request):
        is_personal = request.data.get("is_personal", False)

        if is_personal:
            serializer = PersonalBuyerSerializer(data=request.data)
        else:
            serializer = CompanyBuyerSerializer(data=request.data)

        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data

        if data.get("tax_document_uuid"):
            return ResponseError("TAX_DOCUMENT_ALREADY_EXISTS")

        company = Company.objects.get(uuid=data.get("company_uuid"))
        if not company:
            return ResponseError("COMPANY_DOES_NOT_EXISTS")
        order_uuid = data.get("order_uuid")

        if order_uuid:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                uuid=order_uuid,
            ).first()
        else:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                order_number=data.get("order_number"),
            ).first()

        if not pick_order:
            return ResponseError("ORDER_DOES_NOT_EXISTS")

        if TaxDocument.objects.filter(
            company_id=company.id,
            pick_order_id=pick_order.id,
            doc_type=TaxDocument.DOCTYPE_TAX_INVOICE,
        ).exists():
            return ResponseError("TAX_DOCUMENT_ALREADY_EXISTS")

        # Prepare common buyer information
        buyer_info = {
            "buyer_name": data.get("buyer_name"),
            "tax_id": data.get("tax_id"),
            "address": data.get("address"),
            "email": data.get("email"),
            "phone_number": data.get("phone_number"),
            # "post_code": data.get("post_code"),
            "is_consent_marketing": data.get("is_consent_marketing"),
        }

        if is_personal:
            buyer = PersonalBuyer(**buyer_info)
        else:
            # Add company-specific information for company buyers
            company_info = {
                "company_name": data.get("company_name"),
                "branch_name": data.get("branch_name"),
                "branch_code": data.get("branch_code"),
                "post_code": data.get("post_code"),
                "is_consent_marketing": data.get("is_consent_marketing"),
            }
            # Merge common and company info
            buyer_info.update(company_info)
            buyer = CompanyBuyer(**buyer_info)

        # Using Service
        etax_service = ETaxService.from_company(company)
        etax_document = etax_service.create_rct(pick_order, buyer, "MANUAL")
        if etax_document.status == TaxDocument.STATUS_FAILED:
            sentry_sdk.capture_message(
                f"Failed to create tax document: {etax_document.company.name} - {etax_document.doc_id}",
            )
            # return ResponseError("TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE")

        return Response(data={"tax_document_uuid": etax_document.uuid})


class TaxDocumentReceiptUpdateAPIView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        if request.data["is_personal"]:
            serializer = PersonalBuyerSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            data = serializer.validated_data
            buyer = PersonalBuyer(**data)
        else:
            serializer = CompanyBuyerSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            data = serializer.validated_data
            buyer = CompanyBuyer(**data)

        # setting part
        tax_document = get_object_or_404(
            TaxDocument,
            uuid=data.get("tax_document_uuid"),
        )
        if not ETaxService.can_edit_etax_doc(tax_document) and not request.data.get(
            "is_staff_update", False
        ):
            return ResponseError("TAX_DOCUMENT_NO_LONGER_EDITABLE")

        company = get_object_or_404(Company, uuid=data.get("company_uuid"))
        create_by = data.get("create_by", "BUYER_MANUAL")

        # Using Service
        etax_service = ETaxService.from_company(company)
        etax_document = etax_service.update_tax_document(
            tax_document, buyer, create_by=create_by
        )

        if etax_document.status == TaxDocument.STATUS_FAILED:
            return ResponseError("TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE")

        return Response({"message": "SUCCESS", "tax_document_uuid": etax_document.uuid})


class TaxDocumentReceiptCancelAPIView(APIView):
    permission_classes = [CanDeleteETaxDocument]

    def post(self, request):
        # tax_document = get_object_or_404(
        #     TaxDocument, uuid=request.data.get("tax_document_uuid")
        # )
        tax_document = TaxDocument.objects.filter(
            company_id=request.user.company_id,
            uuid=request.data.get("tax_document_uuid"),
            doc_type=TaxDocument.DOCTYPE_RECEIPT,
        ).last()
        # # company form
        # if tax_document.buyer.get("branch_name"):
        #     buyer = CompanyBuyer(**tax_document.buyer)
        # else:
        #     buyer = PersonalBuyer(**tax_document.buyer)

        company: Company = tax_document.company

        etax_service = ETaxService.from_company(company)
        etax_document = etax_service.cancel_tax_document(tax_document)
        if etax_document.status == TaxDocument.STATUS_FAILED:
            return ResponseError("TAX_DOCUMENT_GOT_AN_ERROR_WHILE_OPERATE")

        return Response(
            {
                "message": "SUCCESS",
                "tax_document_uuid": etax_document.uuid,
                "id": etax_document.id,
            }
        )


class OrderTaxInvoiceAPIView(APIView):
    """
    Handle buyer request for submitting etax-request
    """

    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        is_personal = request.data.get("is_personal", False)
        if is_personal:
            serializer = PersonalBuyerSerializer(data=request.data)
        else:
            serializer = CompanyBuyerSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        if data.get("tax_document_uuid"):
            return ResponseError("TAX_DOCUMENT_ALREADY_EXISTS")

        company = Company.objects.get(uuid=data.get("company_uuid"))
        if not company:
            return ResponseError("COMPANY_DOES_NOT_EXISTS")

        order_uuid = data.get("order_uuid")
        if order_uuid:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                uuid=order_uuid,
            ).first()
        else:
            pick_order: PickOrder = PickOrder.objects.filter(
                company_id=company.id,
                order_number=data.get("order_number"),
            ).first()

        if not pick_order:
            return ResponseError("ORDER_DOES_NOT_EXISTS")

        if TaxDocument.objects.filter(
            company_id=company.id,
            pick_order_id=pick_order.id,
            doc_type=TaxDocument.DOCTYPE_TAX_INVOICE,
        ).exists():
            return ResponseError("TAX_DOCUMENT_ALREADY_EXISTS")

        # Prepare common buyer information
        buyer_info = {
            "buyer_name": data.get("buyer_name"),
            "tax_id": data.get("tax_id"),
            "address": data.get("address"),
            "email": data.get("email"),
            "phone_number": data.get("phone_number"),
            "post_code": data.get("post_code"),
            "is_consent_marketing": data.get("is_consent_marketing"),
        }

        if is_personal:
            buyer = PersonalBuyer(**buyer_info)
        else:
            # Add company-specific information for company buyers
            company_info = {
                "company_name": data.get("company_name"),
                "branch_name": data.get("branch_name"),
                "branch_code": data.get("branch_code"),
                "post_code": data.get("post_code"),
                "is_consent_marketing": data.get("is_consent_marketing"),
            }
            # Merge common and company info
            buyer_info.update(company_info)
            buyer = CompanyBuyer(**buyer_info)

        # Create by
        user_uuid = request.data.get("user_uuid")
        if user_uuid:
            create_by = User.objects.get(uuid=user_uuid).username
        else:
            create_by = "BUYER"

        if company.get_setting("ETAX_REQUEST_WEBHOOK_ENABLE") and create_by == "BUYER":
            EtaxRequestWebhookService.send_webhook(pick_order=pick_order, buyer=buyer)

        mode = company.get_setting("ETAX_BUYER_REQUEST_ETAX_HANDLING_MODE")
        if mode == "manual" and create_by == "BUYER":
            # ถ้าเป็น mode manual และคนกรอกฟอร์มเป็น BUYER ให้ return ไปหา BUYER ว่าต้องรออนุมัติ
            pick_order.order_json["customername"] = buyer.buyer_name
            pick_order.order_json["customeridnumber"] = buyer.tax_id
            pick_order.order_json["customerbranchno"] = getattr(
                buyer, "branch_code", ""
            )
            pick_order.order_json["customerbranchname"] = getattr(
                buyer, "branch_name", ""
            )
            pick_order.order_json["customeraddress"] = buyer.address
            pick_order.order_json["customerpostcode"] = buyer.post_code
            pick_order.order_json["customerphone"] = buyer.phone_number
            pick_order.order_json["customeremail"] = buyer.email
            pick_order.order_json["extra"] = {
                "is_consent_marketing": buyer.is_consent_marketing
            }
            pick_order.save(update_fields=["order_json"])
            return Response("WAIT_FOR_APPROVAL")
        else:
            # ถ้าเป็น mode manual แต่คนกรอกเป็น Staff ให้สร้างเอกสารเลย
            # ถ้าเป็น mode auto ให้สร้างเอกสารเลย

            etax_service = ETaxService.from_company(company)
            receipt_document, etax_document = etax_service.create_tax_documents(
                pick_order, buyer, create_by
            )

            # Handle error case
            if etax_document is not None:
                if etax_document.status == TaxDocument.STATUS_FAILED:
                    sentry_sdk.capture_message(
                        f"Failed to create etax document: {etax_document.company.name} - {etax_document.doc_id}",
                    )

            elif receipt_document is not None:
                if receipt_document.status == TaxDocument.STATUS_FAILED:
                    sentry_sdk.capture_message(
                        f"Failed to create receipt document: {receipt_document.company.name} - {receipt_document.doc_id}",
                    )
            else:
                sentry_sdk.capture_message(
                    f"Failed to create receipt document and create etax document",
                )

            return Response("ok")


class CreditNoteCancelAPIView(APIView):
    permission_classes = [CanDeleteETaxDocument]

    class Validator(serializers.Serializer):
        tax_document_uuid = serializers.CharField()

    def post(self, request):

        serializer = self.Validator(data=request.data)
        serializer.is_valid(raise_exception=True)

        tax_document = TaxDocument.objects.filter(
            company_id=request.user.company_id,
            uuid=serializer.validated_data.get("tax_document_uuid"),
            doc_type=TaxDocument.DOCTYPE_CREDIT_NOTE,
        ).last()

        if not tax_document:
            return ResponseError("TAX_DOCUMENT_NOT_FOUND")

        company: Company = tax_document.company

        etax_service = ETaxService.from_company(company)

        etax_service.cancel_tax_document(tax_document, cancel_by=request.user.username)

        return Response({"message": "SUCCESS"})
