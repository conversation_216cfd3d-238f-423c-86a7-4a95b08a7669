from rest_framework.views import APIView
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from cloudtasks.tasks import create_cancel_order_task
from core.serializers.serializers import EndDate<PERSON>ield, StartDateField, end_of, start_of
from etax.models import TaxDocument
from picking.models import PickOrder
from services.etax_invoice.etax_service import ETaxService
from rest_framework import serializers
from django.utils import timezone
from datetime import timedelta


class AutoCancelETaxTaskSchedulerAPI(APIView):
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAdminUser]

    class Validator(serializers.Serializer):
        start_date = StartDateField(required=False)
        end_date = EndDateField(required=False)

    def post(self, request):
        data = self.Validator(data=request.data)
        data.is_valid(raise_exception=True)

        start = data.validated_data.get("start_date")
        if not start:
            yesterday = (timezone.today() - timedelta(days=1)).date()
            start = start_of(yesterday)

        end = data.validated_data.get("end_date")
        if not end:
            end = start + timedelta(days=1)

        cancel_orders = PickOrder.objects.filter(
            order_json__status__in=["Voided", "Partial Voided"],
            update_date__gte=start,
            update_date__lte=end,
        ).values_list("id", flat=True)

        not_void_docs = TaxDocument.objects.filter(
            pick_order__in=cancel_orders,
            status=TaxDocument.STATUS_SUCCESS,
        ).prefetch_related("pick_order")

        for doc in not_void_docs:
            create_cancel_order_task(
                doc.company_id,
                doc.pick_order.order_number,
            )

        return Response({"status": "success", "docs": [doc.id for doc in not_void_docs]})
