{"DOC_ID": "RT-579628371706678202", "DOC_NAME": "ใบเสร็จรับเงิน/ใบกำกับภาษี", "DOC_TYPE": "T03", "DOC_STATUS": "NEW", "DOC_ISSUE_DATE": "2025-02-05T19:00:06.660756+07:00", "DOC_CREATE_DATE": "2025-02-05T19:00:06.660782+07:00", "DOC_PURPOSE_CODE": "", "DOC_PURPOSE_DETAIL": "", "REASON_DISPLAY": "", "DOC_CONTENT": "", "DOC_GLOB_ID": "", "DOC_PDF_SIZE": "", "DOC_REMARK": "", "DOC_SUBJECT": "", "DOC_XML_FLAG": "", "EMAIL_FLAG": "Y", "RD_FLAG": "N", "DOC_PDF_FLAG": "", "PDF_BASE64": "", "XML_BASE64": "", "TENANT_CODE": "dt2024046", "TENANT_ID": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715", "BRANCH_ID": "3f329a7a-1496-490c-850e-dff7d3d34bf2", "SELLER_ID": "", "SELLER_GLOB_ID": "", "SELLER_NAME": "Cusway", "SELLER_TAX_ID": "0105559130701", "SELLER_TYPE_TAX": "TXID", "SELLER_BRANCH": "00000", "SELLER_BRANCH_NAME": "สำนักงานใหญ่", "SELLER_CONTACT_NAME": "", "SELLER_CONTACT_DEPARTMENT": "", "SELLER_CONTACT_EMAIL": "", "SELLER_CONTACT_PHONE": "0886604941", "SELLER_FAX": "", "SELLER_BUILDING_NO": "", "SELLER_BUILDING_NAME": "", "SELLER_ADDRESS": "88/229 โครงการ <PERSON>amese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230", "SELLER_ADDRESS2": "", "SELLER_PROVINCE_CODE": "", "SELLER_SUBDISTRICT_CODE": "", "SELLER_SUBDISTRICT_NAME": "", "SELLER_DISTRICT_CODE": "", "SELLER_DISTRICT_NAME": "", "SELLER_PROVINCE_NAME": "", "SELLER_POSTCODE": "10230", "SELLER_COUNTRY_ID": "TH", "BUYER_ID": "", "BUYER_GLOB_ID": "", "BUYER_NAME": "<PERSON>", "BUYER_TAX_ID": "0234567891231", "BUYER_TYPE_TAX": "NIDN", "BUYER_BRANCH": "", "BUYER_BRANCH_NAME": "", "BUYER_CONTACT_NAME": "", "BUYER_CONTACT_DEPARTMENT": "", "BUYER_CONTACT_EMAIL": "joeya<PERSON><PERSON><PERSON>@gmail.com", "BUYER_CONTACT_PHONE": "0886604941", "BUYER_FAX": "", "BUYER_BUILDING_NO": "", "BUYER_BUILDING_NAME": "", "BUYER_ADDRESS": "123 Main St, Springfield, IL, 62704 10250", "BUYER_ADDRESS2": "", "BUYER_POSTCODE": "10250", "BUYER_COUNTRY_ID": "TH", "BUYER_ORDER_ID": "", "BUYER_ORDER_TYPE_CODE": "", "BUYER_ORDER_ISSUE_DATE": "", "BUYER_DELIVERY_TYPE": "", "BUYER_DELIVERY_TYPE_CODE": "", "BUYER_BIRTHDATE": "", "BUYER_DOCUMENT_ID": "", "BUYER_DOC_TYPE_CODE": "", "BUYER_DOC_ISSUE_DATE": "", "SHIPTO_ID": "", "SHIPTO_NAME": "<PERSON>", "SHIPTO_ADDRESS": "ไทย, ปทุมธานี, คลองหลวง,La*********************************************************** ", "SHIPTO_POSTCODE": "", "SHIPTO_COUNTRY_ID": "", "SHIPTO_ADDRESS2": "", "SHIPTO_CONTACT_DEPARTMENT": "", "SHIPTO_CONTACT_NAME": "", "SHIPTO_CONTACT_EMAIL": "", "SHIPTO_CONTACT_PHONE": "", "SHIPTO_GLOB_ID": "", "SHIPFROM_ID": "", "SHIPFROM_NAME": "", "SHIPFROM_ADDRESS": "", "SHIPFROM_POSTCODE": "", "SHIPFROM_COUNTRY_ID": "", "SHIPFROM_ADDRESS2": "", "SHIPFROM_CONTACT_DEPARTMENT": "", "SHIPFROM_CONTACT_NAME": "", "SHIPFROM_CONTACT_EMAIL": "", "SHIPFROM_CONTACT_PHONE": "", "SHIPFROM_GLOB_ID": "", "OCCURRENCE_DATETIME": "", "SO_NO": "", "WH_NO": "", "DALI_BY": "", "DALI_DATE": "", "DATETIME": "", "ORIGINAL_INVOICE": "", "INVOICE_CURRENCY_CODE": "THB", "TAX_TYPE_CODE": "VAT", "TAX_CALCULATED_RATE": "7", "TAX_BASIS_AMOUNT": "216.45", "TAX_CALCULATED_AMOUNT": "15.15", "MONEY_ORIGINAL_AMOUNT": "", "MONEY_LINE_TOTALAMOUNT": "220.19", "MONEY_DIFF_AMT": "", "MONEY_CHARGE_TOTALAMT": "", "MONEY_TAXBASIS_TOTALAMT": "220.19", "MONEY_EXCEPT_TAXBASIS_TOTALAMT": "", "MONEY_TAX_TOTALAMT": "15.41", "MONEY_GRAND_TOTALAMT": "235.6", "ALLOWANCE_TYPE_CODE": "", "ALLOWANCE_ACTUAL_AMOUNT": "", "ALLOWANCE_CHARGE_INDICATOR": "", "ALLOWANCE_REASON_CODE": "", "ALLOWANCE_REASON_NAME": "", "ALLOWANCE_SP_ACTUAL_AMOUNT": "0.0", "MONEY_ALLOWANCE_TOTALAMT": "16.4", "PAYMENT_TYPE_CODE": "", "PAYMENT_DESCRIPTION": "", "PAYMENT_DUE_DATETIME": "", "PAY_TERM": "", "INVOICER_ID": "", "INVOICER_NAME": "", "INVOICER_TAX_ID": "", "INVOICER_TYPE_TAX": "", "INVOICER_BUILDING_NO": "", "INVOICER_BUILDING_NAME": "", "INVOICER_ADDRESS": "", "INVOICER_POSTCODE": "", "INVOICER_COUNTRY_ID": "", "INVOICER_ADDRESS2": "", "INVOICER_PROVINCE_CODE": "", "INVOICER_DISTRICT_CODE": "", "INVOICER_SUBDISTRICT_CODE": "", "INVOICER_CONTACT_DEPARTMENT": "", "INVOICER_CONTACT_NAME": "", "INVOICER_CONTACT_EMAIL": "", "INVOICER_CONTACT_PHONE": "", "INVOICER_GLOB_ID": "", "INVOICEE_ID": "", "INVOICEE_NAME": "", "INVOICEE_TAX_ID": "", "INVOICEE_TYPE_TAX": "", "INVOICEE_ADDRESS": "", "INVOICEE_POSTCODE": "", "INVOICEE_COUNTRY_ID": "", "INVOICEE_ADDRESS2": "", "INVOICEE_CONTACT_DEPARTMENT": "", "INVOICEE_CONTACT_NAME": "", "INVOICEE_CONTACT_EMAIL": "", "INVOICEE_CONTACT_PHONE": "", "INVOICEE_GLOB_ID": "", "PAYER_ID": "", "PAYER_NAME": "", "PAYER_TAX_ID": "", "PAYER_TYPE_TAX": "", "PAYER_ADDRESS": "", "PAYER_POSTCODE": "", "PAYER_COUNTRY_ID": "", "PAYER_ADDRESS2": "", "PAYER_CONTACT_DEPARTMENT": "", "PAYER_CONTACT_NAME": "", "PAYER_CONTACT_EMAIL": "", "PAYER_CONTACT_PHONE": "", "PAYER_GLOB_ID": "", "PAYEE_ID": "", "PAYEE_NAME": "", "PAYEE_TAX_ID": "", "PAYEE_TYPE_TAX": "", "PAYEE_ADDRESS": "", "PAYEE_POSTCODE": "", "PAYEE_COUNTRY_ID": "", "PAYEE_ADDRESS2": "", "PAYEE_CONTACT_DEPARTMENT": "", "PAYEE_CONTACT_NAME": "", "PAYEE_CONTACT_EMAIL": "", "PAYEE_CONTACT_PHONE": "", "PAYEE_GLOB_ID": "", "items": [{"TRADE_LINE_ID": "1", "PRODUCT_ID": "H042-CofBottle440g-Black", "PRODUCT_NAME": "ขวดบรรจุกาแฟ อลูมิเนียมฟู๊ดเกรด มีวาล์วคายก๊าซ CO2 By ZenBarista", "PRODUCT_BILLED_QTY": "2", "PRODUCT_CHARGEAMT": "124.0", "PRODUCT_UNIT": "PCS", "PRODUCT_UNITQTY": "", "PRODUCT_ACTUALAMT": "16.4", "PRODUCT_TAX_TYPE_CODE": "VAT", "PRODUCT_TAX_CALRATE": "7", "PRODUCT_TAX_CALAMT": "15.15", "PRODUCT_TAX_BASISAMT": "216.45", "PRODUCT_SUM_TAXTOTALAMT": "15.15", "PRODUCT_SUM_NETLINE_TOTALAMT": "231.6", "PRODUCT_SUM_TOTALAMT": "231.6", "DISCOUNT_PERCENT": "", "DISCOUNT_PER_UNIT": "", "DISCOUNT_VALUE": "", "PRODUCT_GLOB_ID": "", "PRODUCT_CODE_DISPLAY": "", "PRODUCT_DESCRIPTION": "", "PRODUCT_BATCH_ID": "", "PRODUCT_EXPIRY_DATE": "", "PRODUCT_CLASS_CODE": "", "PRODUCT_CLASS_NAME": "", "PRODUCT_COUNTRY_ID": "", "PRODUCT_SUBJECT": "", "PRODUCT_CONTENT": "", "PRODUCT_TYPE_CODE": "", "PRODUCT_CHAREGE_INDICATOR": "", "PRODUCT_ALLOW_TYPE_CODE": "", "PRODUCT_ALLOWANCE_DISPLAY": "", "PRODUCT_PER_REASON_CODE": "", "PRODUCT_PER_REASON_NAME": "", "PRODUCT_PER_TYPECODE": "", "PRODUCT_REASONCODE": "", "PRODUCT_REASON_NAME": "", "PRODUCT_OVERDUE_BALANCE": "", "PRODUCT_PER_ACTUALAMT": "", "PRODUCT_PER_CHARGE": "", "UNIT_DISPLAY": ""}], "REMARK_INV": "", "REMARK_OTHER": "", "REMARK_OTHER_2": "", "REMARK_OTHER_3": ""}