from pprint import pprint
from django.test import TestCase, tag, SimpleTestCase
from unittest import skipIf

from companies.models.models import Company

from core.tests import setup
from etax.models import TaxDocument
from picking.models import PickOrder
from services.etax_invoice.d1a_repository import D1aRepository
from services.etax_invoice.d1a_schema import D1aDocument, PersonalBuyer
from services.etax_invoice.etax_service import ETaxService
import json
import os
import random
import string
from datetime import datetime, date
from django.conf import settings
from deepdiff import DeepDiff
import calendar
import csv


personal_buyer_1 = PersonalBuyer(
    buyer_name="<PERSON>",
    tax_id="0234567891231",
    address="123 Main St, Springfield, IL, 62704",
    email="<EMAIL>",
    phone_number="0886623545",
    post_code="10250",
    is_consent_marketing=True,
)


def generate_order_number():
    # Get current timestamp
    timestamp = datetime.now().strftime("%y%m%d")  # Format: YYMMDD

    # Generate random alphanumeric string
    random_chars = "".join(random.choices(string.ascii_uppercase + string.digits, k=4))

    # Combine timestamp and random string
    order_number = f"ORD{timestamp}{random_chars}"

    return order_number


def create_tax_document(pick_order: PickOrder, buyer: dict) -> TaxDocument:
    return TaxDocument.objects.create(
        company_id=pick_order.company_id,
        pick_order=pick_order,
        order_number=pick_order.order_number,
        buyer=buyer,
    )


# Usage: python manage.py test --keepdb --tag=tax-document-regression-test


class EtaxServiceTestCase(TestCase):

    def setUp(self):

        # Setup Repository and Service
        self.repository = D1aRepository(
            api_host="https://uatv2.detax.in.th",
            api_token="YWRtaW46UGFTUzQzMjE=",
            branch_id="3f329a7a-1496-490c-850e-dff7d3d34bf2",
            tenant_code="dt2024046",
            tenant_id="1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
        )
        self.e_tax_service = ETaxService(self.repository)
        self.base_dir = settings.BASE_DIR

        # Company Setup
        result = setup.init()
        self.company: Company = result["company"]
        self.user = result["user"]
        self.company.set_setting(
            "ETAX_SELLER",
            {
                "name": "Cusway",
                "tax_id": "0105559130701",
                "type_tax": "TXID",
                "seller_branch": "00000",
                "seller_branch_name": "สำนักงานใหญ่",
                "address": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230",
                "tenant_code": "dt2024046",
                "tenant_id": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
                "branch_id": "3f329a7a-1496-490c-850e-dff7d3d34bf2",
                "phone_number": "0886604941",
                "post_code": "10230",
            },
        )
        self.company.set_setting(
            "ETAX_DOCUMENT_FOLDER_ID", "1d7ECPLD91hS0hL13vKekqI2hHZtRgvpf"
        )

        # Setup PickOrder
        # setup.create_test_pickorders(company=self.company)
        # self.pick_order = PickOrder.objects.first()
        # self.pick_order.order_number = generate_order_number()

    @tag("tax-document-regression-test")
    def test_tax_document_regression(self):

        base_dir = os.path.join(self.base_dir, "etax/tests/test_data")
        file_name = "order_json.json"
        d1a_doc_name = "doc_info.json"

        errors = []
        for dir_order_number in os.listdir(base_dir):
            file_path = os.path.join(base_dir, dir_order_number, file_name)
            d1a_doc_path = os.path.join(base_dir, dir_order_number, d1a_doc_name)
            if os.path.exists(file_path) and os.path.exists(d1a_doc_path):
                with open(file_path, "r", encoding="utf-8") as file:
                    order_json = json.load(file)

                pick_order: PickOrder = PickOrder.create_from_zort_order(
                    self.company, order_json, commit=True
                )
                d1a_doc = D1aDocument.prepare_d1a_tiv(
                    pick_order, personal_buyer_1, split_remark=""
                )

                with open(d1a_doc_path, "r", encoding="utf-8") as d1a:
                    d1a_reference_json = json.load(d1a)

                new_d1a_dict = d1a_doc.model_dump(mode="json")

                d1a_reference_json["DOC_ISSUE_DATE"] = ""
                d1a_reference_json["DOC_CREATE_DATE"] = ""
                d1a_reference_json["BUYER_CONTACT_EMAIL"] = ""
                d1a_reference_json["BUYER_CONTACT_PHONE"] = ""

                new_d1a_dict["DOC_ISSUE_DATE"] = ""
                new_d1a_dict["DOC_CREATE_DATE"] = ""
                new_d1a_dict["BUYER_CONTACT_EMAIL"] = ""
                new_d1a_dict["BUYER_CONTACT_PHONE"] = ""

                print("Testing on", dir_order_number, end="... ")
                diff = DeepDiff(d1a_reference_json, new_d1a_dict, significant_digits=5)

                if diff:
                    print("FAILED")
                    print("\n")
                    print("-" * 50)
                    print(f"Found mismatch on {dir_order_number}")
                    pprint(diff)
                    print("-" * 50)
                    print("\n")
                    errors.append(dir_order_number)
                else:
                    print("PASS")

                # self.assertEqual(
                #     d1a_reference_json,
                #     new_d1a_dict,
                #     f"Mismatch found:\n{diff} on {dir_order_number}",
                # )
        if errors:
            raise AssertionError(f"Found mismatch on {', '.join(errors)}")

    @tag("prepare-regression-test")
    def test_prepare_regression(self):
        base_dir = os.path.join(self.base_dir, "etax/tests/test_data")
        file_name = "order_json_list.json"

        with open(
            os.path.join(base_dir, file_name),
            "r",
            encoding="utf-8",
        ) as file:
            datas = json.load(file)

        for order_json in datas:
            pick_order: PickOrder = PickOrder.create_from_zort_order(
                self.company, order_json, commit=True
            )
            # Prepare D1a document
            d1a_doc = D1aDocument.prepare_d1a_tiv(
                pick_order, personal_buyer_1, split_remark=""
            )
            d1a_dict = d1a_doc.model_dump(mode="json")

            order_path = os.path.join(base_dir, pick_order.order_number)
            os.makedirs(order_path, exist_ok=True)
            doc_info_path = os.path.join(order_path, "doc_info.json")
            order_json_path = os.path.join(order_path, "order_json.json")

            # Save documents using the custom encoder
            with open(doc_info_path, "w", encoding="utf-8") as doc_file:
                json.dump(d1a_dict, doc_file, indent=4, ensure_ascii=False)

            with open(order_json_path, "w", encoding="utf-8") as order_file:
                json.dump(
                    order_json,
                    order_file,
                    indent=4,
                    ensure_ascii=False,
                )

            print(
                f"✅ Successfully written d1a_doc and order_json for order: {pick_order.order_number}"
            )


class CutOffDateTestCase(SimpleTestCase):

    @tag("test-running-date")
    @skipIf(True, "Run for mock data only)}")
    def test_running_date(self):
        # Define start and end dates
        start_year = 2025
        end_year = 2029
        csv_filename = "running_dates.csv"

        with open(csv_filename, mode="w", newline="") as file:
            writer = csv.writer(file)
            writer.writerow(["running_date", "cut_of_date"])  # Header

            for year in range(start_year, end_year + 1):
                for month in range(1, 13):  # Iterate through each month (1 to 12)
                    last_valid_day = calendar.monthrange(year, month)[
                        1
                    ]  # Get last day of the month

                    for running_day in range(1, 32):  # Testing from 1 to 31
                        if running_day > last_valid_day:
                            continue  # Skip invalid dates like 30/02

                        document_date = date(year, month, running_day)

                        # Generate cut-off date using your function
                        cut_off_date = ETaxService.get_cut_off_date(document_date)

                        # Debugging Output
                        print(
                            f"Document date: {document_date} -> Cut-off date: {cut_off_date}"
                        )

                        # Write to CSV
                        writer.writerow([document_date, cut_off_date])

                    if year == end_year and month == 1:
                        break  # Stop exactly after January 2029

        print(f"CSV file '{csv_filename}' has been created.")

    @tag("test-compare-csv")
    def test_compare_csv(self):
        csv_file = os.path.join(
            settings.BASE_DIR, "etax/tests/test_data", "running_dates.csv"
        )

        with open(csv_file, mode="r") as file:
            reader = csv.reader(file)
            next(reader)

            for row in reader:
                running_date_str, expected_cut_off_date_str = row

                running_date = datetime.strptime(running_date_str, "%Y-%m-%d").date()
                expected_cut_off_date = datetime.strptime(
                    expected_cut_off_date_str, "%Y-%m-%d"
                ).date()

                actual_cut_off_date = ETaxService.get_cut_off_date(running_date, 12)

                self.assertEqual(
                    actual_cut_off_date,
                    expected_cut_off_date,
                    f"Mismatch for {running_date}: Expected {expected_cut_off_date}, but got {actual_cut_off_date}",
                )
