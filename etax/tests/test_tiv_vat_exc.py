from django.test import TestCase, tag

from core.tests.testutils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from picking.models import PickOrder
from services.etax_invoice.d1a_schema import D1aDocument, PersonalBuyer
from services.etax_invoice.dbb_tax_schema import DobybotTaxInvoice, TaxInv
from core.tests import setup

class EtaxTivVatExcTestCase(TestCase, TestCaseHelper):
    TESTCASES = [
        {
            "input": {
                "amount": 3027.03,
                "amount_pretax": 0,
                "createdatetimeString": "2025-02-08 20:04:03",
                "createdby": None,
                "createuserid": None,
                "createusername": "",
                "customeraddress": "",
                "customerbranchname": "",
                "customerbranchno": "",
                "customercode": "",
                "customeremail": "",
                "customerid": None,
                "customeridnumber": "",
                "customername": "",
                "customerphone": "",
                "customerpostcode": "",
                "description": "",
                "discount": "",
                "discount_amount": 0,
                "discountamount": 0,
                "expireDateString": None,
                "extra": {"branch_number": "00000", "ref_tax_invoice_number": ""},
                "facebookid": "",
                "facebookname": None,
                "freeze": None,
                "id": None,
                "integrationName": "",
                "integrationShop": "",
                "isCOD": False,
                "is_confirm_received": True,
                "line": None,
                "lineid": "",
                "list": [
                    {
                        "id": None,
                        "sku": "",
                        "name": "อาหาร-เครื่องดื่ม",
                        "number": 1,
                        "status": None,
                        "skutype": None,
                        "discount": "",
                        "mfg_date": None,
                        "unittext": "",
                        "productid": None,
                        "totalprice": 2829,
                        "producttype": 0,
                        "sku_barcode": None,
                        "serialnolist": [],
                        "discountamount": 0,
                        "eso_vatpercent": 7,
                        "original_price": 0,
                        "pricepernumber": 2829,
                        "return_quantity": 0,
                        "seller_discount": 0,
                        "shipping_amount": 0,
                        "platform_discount": 0,
                        "original_shipping_amount": 0,
                        "seller_shipping_discount": 0,
                        "platform_shipping_discount": 0,
                    }
                ],
                "marketplacename": None,
                "marketplacepayment": None,
                "marketplaceshippingstatus": None,
                "number": "RC680207016",
                "orderManagementSystem": "easy-order",
                "orderdateString": "2025-02-08",
                "ordertype": 0,
                "original_shipping_amount": 0,
                "paymentamount": None,
                "paymentdatetimeString": None,
                "paymentmethod": None,
                "payments": [],
                "paymentstatus": "Pending",
                "platform_discount": 0,
                "platform_shipping_discount": 0,
                "platform_status": None,
                "platformdiscount": 0,
                "reference": "",
                "remark": "โต๊ะ53",
                "remark_status": "",
                "return_status": "NO_RETURN",
                "saleschannel": "-",
                "seller_discount": 0,
                "seller_shipping_discount": 0,
                "sellerdiscount": 0,
                "sharelink": None,
                "shipping_amount": 0,
                "shipping_sorting_no": "",
                "shippingaddress": "",
                "shippingamount": 0,
                "shippingchannel": "",
                "shippingdateString": None,
                "shippingdistrict": None,
                "shippingemail": "",
                "shippingname": "",
                "shippingphone": "",
                "shippingpostcode": None,
                "shippingprovince": None,
                "shippingsubdistrict": None,
                "shippingvat": 0,
                "status": "Pending",
                "tag": [],
                "trackingno": "",
                "updatedatetimeString": None,
                "vatamount": 0,
                "vatpercent": 7,
                "vattype": 2,
                "version": 1,
                "voucheramount": 0,
                "warehousecode": "",
            },
            "expect": {
                "DOC_ID": str,  # เลขที่เอกสาร
                "DOC_NAME": "ใบเสร็จรับเงิน/ใบกำกับภาษี",  # ชื่อเอกสาร
                "DOC_TYPE": "T03",  # รหัสประเภทเอกสาร
                "DOC_ISSUE_DATE": str,  # วันเดือนปี ที่ออกเอกสาร
                "DOC_PURPOSE_DETAIL": "",  # สาเหตุการออกเอกสาร
                "DOC_PURPOSE_CODE": "",  # รหัสสาเหตุการออกเอกสาร
                "DOC_CREATE_DATE": str,  # วันเดือนปีและเวลาที่สร้างเอกสาร
                "INVOICE_CURRENCY_CODE": "THB",  # รหัสสกุลเงินตรา
                "TAX_TYPE_CODE": "VAT",  # รหัสประเภทภาษี
                "TAX_CALCULATED_RATE": "7",  # อัตราภาษี
                "TAX_BASIS_AMOUNT": "2829.00",  # มูลค่าสินค้าทั้งหมด (ยังไม่รวมภาษี)
                "TAX_CALCULATED_AMOUNT": "198.03",  # มูลค่าภาษีทั้งหมดของสินค้าทุกรายการ
                "MONEY_LINE_TOTALAMOUNT": "2829.00",  # ยอดเงินรวมสินค้าทั้งหมด (ยังไม่รวมภาษี)
                "MONEY_TAXBASIS_TOTALAMT": "2829.00",  # ยอดเงินรวมสินค้าทั้งหมดที่นำมาคิดภาษี
                "MONEY_TAX_TOTALAMT": "198.03",  # ยอดเงินรวมภาษีมูลค่าเพิ่ม
                "MONEY_GRAND_TOTALAMT": "3027.03",  # ยอดเงินรวมสินค้าทั้งหมด (รวมภาษีแล้ว)
                "items": [
                    {
                        "TRADE_LINE_ID": "1",  # ลำดับรายการ
                        "PRODUCT_ID": "-",  # รหัสสินค้า
                        "PRODUCT_NAME": "อาหาร-เครื่องดื่ม",  # ชื่อสินค้า
                        "PRODUCT_CHARGEAMT": "2829.00",  # ราคาต่อหน่วย (ยังไม่รวมภาษี)
                        "PRODUCT_BILLED_QTY": "1",  # จำนวนสินค้า
                        "PRODUCT_UNIT": "PCS",  # ชื่อหน่วยของสินค้า
                        "PRODUCT_TAX_TYPE_CODE": "VAT",  # รหัสประเภทภาษี
                        "PRODUCT_TAX_CALRATE": "7",  # อัตราภาษี
                        "PRODUCT_TAX_BASISAMT": "2829.00",  # มูลค่าสินค้าที่นำมาคิดภาษี
                        "PRODUCT_TAX_CALAMT": "198.03",  # มูลค่าภาษีเฉพาะรายการสินค้านั้นๆ
                        "PRODUCT_SUM_TAXTOTALAMT": "198.03",  # ภาษีมูลค่าเพิ่ม
                        "PRODUCT_SUM_NETLINE_TOTALAMT": "2829.00",  # มูลค่าสินค้ารวม (ไม่รวมภาษี)
                        "PRODUCT_SUM_TOTALAMT": "3027.03",  # มูลค่าสินค้ารวม (รวมภาษีแล้ว)
                    }
                ],
            },
        },
        {
            "input": {
                "amount": 1722.7,
                "amount_pretax": 0,
                "createdatetimeString": "2025-02-07 18:44:23",
                "createdby": None,
                "createuserid": None,
                "createusername": "",
                "customeraddress": "",
                "customerbranchname": "",
                "customerbranchno": "",
                "customercode": "",
                "customeremail": "",
                "customerid": None,
                "customeridnumber": "",
                "customername": "",
                "customerphone": "",
                "customerpostcode": "",
                "description": "",
                "discount": "",
                "discount_amount": 0,
                "discountamount": 0,
                "expireDateString": None,
                "extra": {"branch_number": "00000"},
                "facebookid": "",
                "facebookname": None,
                "freeze": None,
                "id": None,
                "integrationName": "",
                "integrationShop": "",
                "isCOD": False,
                "is_confirm_received": True,
                "line": None,
                "lineid": "",
                "list": [
                    {
                        "id": None,
                        "sku": "",
                        "name": "อาหาร-เครื่องดื่ม",
                        "number": 1,
                        "status": None,
                        "skutype": None,
                        "discount": "",
                        "mfg_date": None,
                        "unittext": "",
                        "productid": None,
                        "totalprice": 1610,
                        "producttype": 0,
                        "sku_barcode": None,
                        "serialnolist": [],
                        "discountamount": 0,
                        "eso_vatpercent": 7,
                        "original_price": 0,
                        "pricepernumber": 1610,
                        "return_quantity": 0,
                        "seller_discount": 0,
                        "shipping_amount": 0,
                        "platform_discount": 0,
                        "original_shipping_amount": 0,
                        "seller_shipping_discount": 0,
                        "platform_shipping_discount": 0,
                    }
                ],
                "marketplacename": None,
                "marketplacepayment": None,
                "marketplaceshippingstatus": None,
                "number": "RC680207003",
                "orderManagementSystem": "easy-order",
                "orderdateString": "2025-02-07",
                "ordertype": 0,
                "original_shipping_amount": 0,
                "paymentamount": None,
                "paymentdatetimeString": None,
                "paymentmethod": None,
                "payments": [],
                "paymentstatus": "Pending",
                "platform_discount": 0,
                "platform_shipping_discount": 0,
                "platform_status": None,
                "platformdiscount": 0,
                "reference": "",
                "remark": "โต๊ะ41",
                "remark_status": "",
                "return_status": "NO_RETURN",
                "saleschannel": "-",
                "seller_discount": 0,
                "seller_shipping_discount": 0,
                "sellerdiscount": 0,
                "sharelink": None,
                "shipping_amount": 0,
                "shipping_sorting_no": "",
                "shippingaddress": "",
                "shippingamount": 0,
                "shippingchannel": "",
                "shippingdateString": None,
                "shippingdistrict": None,
                "shippingemail": "",
                "shippingname": "",
                "shippingphone": "",
                "shippingpostcode": None,
                "shippingprovince": None,
                "shippingsubdistrict": None,
                "shippingvat": 0,
                "status": "Pending",
                "tag": [],
                "trackingno": "",
                "updatedatetimeString": None,
                "vatamount": 0,
                "vatpercent": 7,
                "vattype": 2,
                "version": 1,
                "voucheramount": 0,
                "warehousecode": "",
            },
            "expect": {
                "DOC_ID": str,  # เลขที่เอกสาร
                "DOC_NAME": "ใบเสร็จรับเงิน/ใบกำกับภาษี",  # ชื่อเอกสาร
                "DOC_TYPE": "T03",  # รหัสประเภทเอกสาร
                "DOC_ISSUE_DATE": str,  # วันเดือนปี ที่ออกเอกสาร
                "DOC_PURPOSE_DETAIL": "",  # สาเหตุการออกเอกสาร
                "DOC_PURPOSE_CODE": "",  # รหัสสาเหตุการออกเอกสาร
                "DOC_CREATE_DATE": str,  # วันเดือนปีและเวลาที่สร้างเอกสาร
                "INVOICE_CURRENCY_CODE": "THB",  # รหัสสกุลเงินตรา
                "TAX_TYPE_CODE": "VAT",  # รหัสประเภทภาษี
                "TAX_CALCULATED_RATE": "7",  # อัตราภาษี
                "TAX_BASIS_AMOUNT": "1610.00",  # มูลค่าสินค้าทั้งหมด (ยังไม่รวมภาษี)
                "TAX_CALCULATED_AMOUNT": "112.70",  # มูลค่าภาษีทั้งหมดของสินค้าทุกรายการ
                "MONEY_LINE_TOTALAMOUNT": "1610.00",  # ยอดเงินรวมสินค้าทั้งหมด (ยังไม่รวมภาษี)
                "MONEY_TAXBASIS_TOTALAMT": "1610.00",  # ยอดเงินรวมสินค้าทั้งหมดที่นำมาคิดภาษี
                "MONEY_TAX_TOTALAMT": "112.70",  # ยอดเงินรวมภาษีมูลค่าเพิ่ม
                "MONEY_GRAND_TOTALAMT": "1722.70",  # ยอดเงินรวมสินค้าทั้งหมด (รวมภาษีแล้ว)
                "items": [
                    {
                        "TRADE_LINE_ID": "1",  # ลำดับรายการ
                        "PRODUCT_ID": "-",  # รหัสสินค้า
                        "PRODUCT_NAME": "อาหาร-เครื่องดื่ม",  # ชื่อสินค้า
                        "PRODUCT_CHARGEAMT": "1610.00",  # ราคาต่อหน่วย (ยังไม่รวมภาษี)
                        "PRODUCT_BILLED_QTY": "1",  # จำนวนสินค้า
                        "PRODUCT_UNIT": "PCS",  # ชื่อหน่วยของสินค้า
                        "PRODUCT_TAX_TYPE_CODE": "VAT",  # รหัสประเภทภาษี
                        "PRODUCT_TAX_CALRATE": "7",  # อัตราภาษี
                        "PRODUCT_TAX_BASISAMT": "1610.00",  # มูลค่าสินค้าที่นำมาคิดภาษี
                        "PRODUCT_TAX_CALAMT": "112.70",  # มูลค่าภาษีเฉพาะรายการสินค้านั้นๆ
                        "PRODUCT_SUM_TAXTOTALAMT": "112.70",  # ภาษีมูลค่าเพิ่ม
                        "PRODUCT_SUM_NETLINE_TOTALAMT": "1610.00",  # มูลค่าสินค้ารวม (ไม่รวมภาษี)
                        "PRODUCT_SUM_TOTALAMT": "1722.70",  # มูลค่าสินค้ารวม (รวมภาษีแล้ว)
                    }
                ],
            },
        },
    ]

    def setUp(self):
        result = setup.init()
        self.company = result["company"]
        self.company.set_setting(
            "ETAX_SELLER",
            {
                "name": "Cusway",
                "tax_id": "0105559130701",
                "type_tax": "TXID",
                "seller_branch": "00000",
                "seller_branch_name": "สำนักงานใหญ่",
                "address": "88/229 โครงการ Siamese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230",
                "tenant_code": "dt2024046",
                "tenant_id": "1e7bcea7-e1c6-4f6e-bc72-fa78d8129715",
                "branch_id": "3f329a7a-1496-490c-850e-dff7d3d34bf2",
                "phone_number": "0886604941",
                "post_code": "10230",
            },
        )
        self.buyer =  PersonalBuyer(
            buyer_name="John Doe",
            tax_id="0234567891231",
            address="123 Main St, Springfield, IL, 62704",
            email="<EMAIL>",
            phone_number="0886623545",
            post_code="10250",
            is_consent_marketing=True,
        )

    @tag("etax_tiv_vat_exc")
    def test_order_json_to_tiv_vat_exc(self):
        for idx, testcase in enumerate(self.TESTCASES):
            with self.subTest(f"test_order_json_to_tiv_vat_exc #{idx}"):
                input_data = testcase["input"]
                pick_order = PickOrder.create_from_zort_order(self.company, input_data, commit=False)
                output_data = D1aDocument.prepare_d1a_tiv(pick_order, buyer=self.buyer).model_dump(mode="json")
                expect_data = testcase["expect"]

                self.assertMatch(autoround(output_data), autoround(expect_data))


class EtaxTivTestCase(TestCase, TestCaseHelper):
    TESTCASES = [
        {
            "input": {
                "amount": 3027.03,
                "amount_pretax": 0,
                "createdatetimeString": "2025-02-08 20:04:03",
                "createdby": None,
                "createuserid": None,
                "createusername": "",
                "customeraddress": "",
                "customerbranchname": "",
                "customerbranchno": "",
                "customercode": "",
                "customeremail": "",
                "customerid": None,
                "customeridnumber": "",
                "customername": "",
                "customerphone": "",
                "customerpostcode": "",
                "description": "",
                "discount": "",
                "discount_amount": 0,
                "discountamount": 0,
                "expireDateString": None,
                "extra": {"branch_number": "00000", "ref_tax_invoice_number": ""},
                "facebookid": "",
                "facebookname": None,
                "freeze": None,
                "id": None,
                "integrationName": "",
                "integrationShop": "",
                "isCOD": False,
                "is_confirm_received": True,
                "line": None,
                "lineid": "",
                "list": [
                    {
                        "id": None,
                        "sku": "",
                        "name": "อาหาร-เครื่องดื่ม",
                        "number": 1,
                        "status": None,
                        "skutype": None,
                        "discount": "",
                        "mfg_date": None,
                        "unittext": "",
                        "productid": None,
                        "totalprice": 2829,
                        "producttype": 0,
                        "sku_barcode": None,
                        "serialnolist": [],
                        "discountamount": 0,
                        "eso_vatpercent": 7,
                        "original_price": 0,
                        "pricepernumber": 2829,
                        "return_quantity": 0,
                        "seller_discount": 0,
                        "shipping_amount": 0,
                        "platform_discount": 0,
                        "original_shipping_amount": 0,
                        "seller_shipping_discount": 0,
                        "platform_shipping_discount": 0,
                    }
                ],
                "marketplacename": None,
                "marketplacepayment": None,
                "marketplaceshippingstatus": None,
                "number": "RC680207016",
                "orderManagementSystem": "easy-order",
                "orderdateString": "2025-02-08",
                "ordertype": 0,
                "original_shipping_amount": 0,
                "paymentamount": None,
                "paymentdatetimeString": None,
                "paymentmethod": None,
                "payments": [],
                "paymentstatus": "Pending",
                "platform_discount": 0,
                "platform_shipping_discount": 0,
                "platform_status": None,
                "platformdiscount": 0,
                "reference": "",
                "remark": "โต๊ะ53",
                "remark_status": "",
                "return_status": "NO_RETURN",
                "saleschannel": "-",
                "seller_discount": 0,
                "seller_shipping_discount": 0,
                "sellerdiscount": 0,
                "sharelink": None,
                "shipping_amount": 0,
                "shipping_sorting_no": "",
                "shippingaddress": "",
                "shippingamount": 0,
                "shippingchannel": "",
                "shippingdateString": None,
                "shippingdistrict": None,
                "shippingemail": "",
                "shippingname": "",
                "shippingphone": "",
                "shippingpostcode": None,
                "shippingprovince": None,
                "shippingsubdistrict": None,
                "shippingvat": 0,
                "status": "Pending",
                "tag": [],
                "trackingno": "",
                "updatedatetimeString": None,
                "vatamount": 0,
                "vatpercent": 7,
                "vattype": 2,
                "version": 1,
                "voucheramount": 0,
                "warehousecode": "",
            },
            "expect": {
                "pretaxamount": 2829.00,  # มูลค่าที่นำมาคิดภาษีมูลค่าเพิ่ม (Net Amount)
                "totalvatamount": 198.03,  # มูลค่าภาษีมูลค่าเพิ่ม (VAT Amount)
                "grandtotal": 3027.03,  # ยอดเงินรวมภาษีมูลค่าเพิ่ม (Total Amount Including VAT)
                "discountamount": 0.00,  # ส่วนลดท้ายบิล (Bill Discount)
                "totaldiscount": 0.00,  # ส่วนลดทั้งหมด (Total Discount)
                "tax_inv_rows": [
                    {
                        "sku": "-",  # รหัสสินค้า
                        "name": "อาหาร-เครื่องดื่ม",  # ชื่อสินค้า
                        "number": 1,  # จำนวนสินค้า
                        "pricepernumber": 2829.00,  # ราคาต่อหน่วย (ยังไม่รวมภาษี)
                        "discount": 0,  # ส่วนลด
                        "totalprice": 2829.00,  # ราคาสินค้า (ก่อนภาษี)
                        "pretaxamount": 2829.00,  # มูลค่าก่อนภาษี
                        "vatamount": 198.03,  # จำนวนภาษี
                    }
                ],
            },
        },
        {
            "input": {
                "amount": 1722.7,
                "amount_pretax": 0,
                "createdatetimeString": "2025-02-07 18:44:23",
                "createdby": None,
                "createuserid": None,
                "createusername": "",
                "customeraddress": "",
                "customerbranchname": "",
                "customerbranchno": "",
                "customercode": "",
                "customeremail": "",
                "customerid": None,
                "customeridnumber": "",
                "customername": "",
                "customerphone": "",
                "customerpostcode": "",
                "description": "",
                "discount": "",
                "discount_amount": 0,
                "discountamount": 0,
                "expireDateString": None,
                "extra": {"branch_number": "00000"},
                "facebookid": "",
                "facebookname": None,
                "freeze": None,
                "id": None,
                "integrationName": "",
                "integrationShop": "",
                "isCOD": False,
                "is_confirm_received": True,
                "line": None,
                "lineid": "",
                "list": [
                    {
                        "id": None,
                        "sku": "",
                        "name": "อาหาร-เครื่องดื่ม",
                        "number": 1,
                        "status": None,
                        "skutype": None,
                        "discount": "",
                        "mfg_date": None,
                        "unittext": "",
                        "productid": None,
                        "totalprice": 1610,
                        "producttype": 0,
                        "sku_barcode": None,
                        "serialnolist": [],
                        "discountamount": 0,
                        "eso_vatpercent": 7,
                        "original_price": 0,
                        "pricepernumber": 1610,
                        "return_quantity": 0,
                        "seller_discount": 0,
                        "shipping_amount": 0,
                        "platform_discount": 0,
                        "original_shipping_amount": 0,
                        "seller_shipping_discount": 0,
                        "platform_shipping_discount": 0,
                    }
                ],
                "marketplacename": None,
                "marketplacepayment": None,
                "marketplaceshippingstatus": None,
                "number": "RC680207003",
                "orderManagementSystem": "easy-order",
                "orderdateString": "2025-02-07",
                "ordertype": 0,
                "original_shipping_amount": 0,
                "paymentamount": None,
                "paymentdatetimeString": None,
                "paymentmethod": None,
                "payments": [],
                "paymentstatus": "Pending",
                "platform_discount": 0,
                "platform_shipping_discount": 0,
                "platform_status": None,
                "platformdiscount": 0,
                "reference": "",
                "remark": "โต๊ะ41",
                "remark_status": "",
                "return_status": "NO_RETURN",
                "saleschannel": "-",
                "seller_discount": 0,
                "seller_shipping_discount": 0,
                "sellerdiscount": 0,
                "sharelink": None,
                "shipping_amount": 0,
                "shipping_sorting_no": "",
                "shippingaddress": "",
                "shippingamount": 0,
                "shippingchannel": "",
                "shippingdateString": None,
                "shippingdistrict": None,
                "shippingemail": "",
                "shippingname": "",
                "shippingphone": "",
                "shippingpostcode": None,
                "shippingprovince": None,
                "shippingsubdistrict": None,
                "shippingvat": 0,
                "status": "Pending",
                "tag": [],
                "trackingno": "",
                "updatedatetimeString": None,
                "vatamount": 0,
                "vatpercent": 7,
                "vattype": 2,
                "version": 1,
                "voucheramount": 0,
                "warehousecode": "",
            },
            "expect": {
                "pretaxamount": 1610.00,  # มูลค่าที่นำมาคิดภาษีมูลค่าเพิ่ม (Net Amount)
                "totalvatamount": 112.70,  # มูลค่าภาษีมูลค่าเพิ่ม (VAT Amount)
                "grandtotal": 1722.70,  # ยอดเงินรวมภาษีมูลค่าเพิ่ม (Total Amount Including VAT)
                "discountamount": 0.00,  # ส่วนลดท้ายบิล (Bill Discount)
                "totaldiscount": 0.00,  # ส่วนลดทั้งหมด (Total Discount)
                "tax_inv_rows": [
                    {
                        "sku": "-",
                        "name": "อาหาร-เครื่องดื่ม",
                        "number": 1,
                        "pricepernumber": 1610.00,
                        "discount": 0,
                        "totalprice": 1610.00,  # ราคาสินค้า (ก่อนภาษี)
                        "pretaxamount": 1610.00,  # มูลค่าก่อนภาษี
                        "vatamount": 112.70,  # จำนวนภาษี
                    }
                ],
            },
        },
    ]

    def setUp(self):
        result = setup.init()
        self.company = result["company"]

    @tag("etax_tiv_vat_exc_2")
    def test_prepare_dbb_tiv__vatexc(self):
        for idx, testcase in enumerate(self.TESTCASES):
            with self.subTest(f"test_order_json_to_dbb_tiv #{idx}"):
                input_data = testcase["input"]
                pick_order = PickOrder.create_from_zort_order(self.company, input_data, commit=False)
                output_data = DobybotTaxInvoice.prepare_dbb_tiv__vatexc(pick_order)
                expect_data = testcase["expect"]

                self.assertMatch(autoround(output_data.model_dump(mode='json')), expect=expect_data)

    @tag("etax_tiv_vat_exc_2")
    def test_prepare_dbb_tiv(self):
        for idx, testcase in enumerate(self.TESTCASES):
            with self.subTest(f"test_prepare_dbb_tiv #{idx}"):
                input_data = testcase["input"]
                pick_order = PickOrder.create_from_zort_order(self.company, input_data, commit=False)
                output_data = DobybotTaxInvoice.prepare_dbb_tiv(pick_order)
                expect_data = testcase["expect"]

                self.assertMatch(autoround(output_data.model_dump(mode='json')), expect=expect_data)


def autoround(data: dict):
    for key, value in data.items():
        if isinstance(value, str) and value.replace('.','').isdigit():
            data[key] = round(float(value), 2)
        elif isinstance(value, float):
            data[key] = round(value, 2)
        elif isinstance(value, dict):
            data[key] = autoround(value)
        elif isinstance(value, list):
            data[key] = [autoround(item) for item in value]
    return data
