import logging
import traceback

from django.conf import settings
from django.core.management.base import BaseCommand
from companies.models.models import Company
from core.serializers.serializers import start_of
from etax.management.commands._common import (
    gather_with_concurrency,
    read_excel,
    send_cancel_and_renew,
    write_excel,
)
from etax.models import TaxDocument, TaxDocumentTransaction
from etax.views.etax_debugger_views import EtaxRetryAPIView
from picking.models import PickOrder
from services.etax_invoice.d1a_schema import D1aDocument
from services.etax_invoice.etax_service import ETaxService
from datetime import date
import threading
import asyncio
import httpx


from django.db.models import Q
from django.utils import timezone
from datetime import datetime

from services.google.drive.drive import GoogleDriveService

to_be_cancel_renew = [
    "RT-250126FEDA1P5F",
    # "RT-250128NYMB4KC8",
    # "RT-250128M8SA5Q6K",
    # "RT-250128M8Q0Q2PS",
    # "RT-250129R1CVPY5B",
    # "RT-250129R1HN9RU3",
    # "RT-250129R1AJAXYY",
    # "RT-250129R1A22X20",
    # "RT-250129R1C13D65",
    # "RT-250129R1E3SXJX",
    # "RT-250129R1F0D8ND",
    # "RT-250129R1BFXHR4",
    # "RT-250129R1FYVYKK",
    # "RT-250129S3FBMNC5",
    # "RT-250129R21FQWXY",
    # "RT-250129RXJ8VBSF",
    # "RT-250129RV6GJF6U",
    # "RT-250129R2M5N82U",
    # "RT-250129R241R0PA",
    # "RT-250129RA7YMKWH",
    # "RT-250129RPQR9CN4",
    # "RT-250129RASWRF1R",
    # "RT-250129RDDX5R7T",
    # "RT-250129R98V28YW",
    # "RT-250129R1B6DEPJ",
    # "RT-250129R20DE10B",
    # "RT-250129R1CHA68Y",
    # "RT-250129R2U02GSB",
    # "RT-250129RFQNK6A0",
    # "RT-250129R247F4HA",
    # "RT-250129RJ4BD6AS",
    # "RT-250129R1CSVPW5",
    # "RT-250129RAH79S6H",
    # "RT-250129R1B88Q4U",
    # "RT-250129R1C8P5C3",
    # "RT-250129R221UE5H",
    # "RT-250129R1FGP97X",
    # "RT-250129R1T46A37",
    # "RT-250129R1EVKU95",
    # "RT-250129RTPBEDVF",
    # "RT-250129R1AR2SCH",
    # "RT-250129R1BMPK76",
    # "RT-250129RA3AWCT4",
    # "RT-250129R1S3RD64",
    # "RT-250129R1WB5X35",
    # "RT-250129REECQPM2",
    # "RT-250129R21U5PQQ",
    # "RT-250129RHUWHEGX",
    # "RT-250129R1DX3J1C",
    # "RT-250129R1AS04V2",
    # "RT-250129R1GJ1R3U",
    # "RT-250129RB7T7JRE",
    # "RT-250129R1A32CYW",
    # "RT-250129R1DJMGPG",
    # "RT-250129R1D2EXMN",
    # "RT-250129R9NGEPQJ",
    # "RT-250129R1HAU6YC",
    # "RT-250129R9YW7PA5",
    # "RT-250129R1KM9R98",
    # "RT-250129R52FXYBS",
    # "RT-250129R1B3FAP1",
    # "RT-250129R319AN9E",
    # "RT-250129RDTMGAEU",
    # "RT-250129R1M3J276",
    # "RT-250129R1F5823K",
    # "RT-250129R1ASYYDU",
    # "RT-250129R1N7R7AT",
    # "RT-250129R1N4UGTH",
    # "RT-250129R1CAK338",
    # "RT-250129R1NH9T3Q",
    # "RT-250129R9GQUSS0",
    # "RT-250129R19G0DEM",
    # "RT-250129RDVGHK0D",
    # "RT-250129R1BKR207",
    # "RT-250129R1AUUKA0",
    # "RT-250129R29QVSJ2",
    # "RT-250129R1T0BU7X",
    # "RT-250129RA3A0BHU",
    # "RT-250129R31GVWJR",
    # "RT-250129R1AQ37QD",
    # "RT-250129RUP27NQY",
    # "RT-250129R1C9KRNW",
    # "RT-250129R21HKRHY",
    # "RT-250129R1GG5RCF",
    # "RT-250129R1H26SYK",
    # "RT-250129R1E4SQX7",
    # "RT-250129RFTVKU7X",
    # "RT-250129R1A151TC",
    # "RT-250129R1W4FFHW",
    # "RT-250129RA8C1UUG",
    # "RT-250129R2HB3JX3",
    # "RT-250129R1JUE58F",
    # "RT-250129R1A08H9W",
    # "RT-250129R1CSTNNJ",
    # "RT-250129R29J4HJC",
    # "RT-250129RBD04572",
    # "RT-250129R9UVG9EB",
    # "RT-250129RQ1K110G",
    # "RT-250129R23PAN0E",
    # "RT-250129RKRYJ0FP",
    # "RT-250129R9GH4FKU",
    # "RT-250129R1FCSNHS",
    # "RT-250129R9ANA197",
    # "RT-250129R1H19FQ2",
    # "RT-250129R1E3TWJ8",
    # "RT-250130TRPWJ76V",
    # "RT-250130TJRU3CBW",
    # "RT-250130TRGP84TS",
    # "RT-250130TU6TDT8W",
    # "RT-250130TXT386XT",
    # "RT-250130UCPTWY9P",
    # "RT-250130TRSFMB8U",
    # "RT-250130UCPYMF6Q",
    # "RT-250130U5RXSX7Y",
    # "RT-250131UT0EAMQJ",
    # "RT-250131UT0QVPWS",
    # "RT-2501310XEGDVUT",
    # "RT-25013102GE7WMV",
    # "RT-25020118Y4708P",
    # "RT-25020118ANYVUG",
    # "RT-975124131986776",
    # "RT-250201182AVQKY",
    # "RT-975392771637604",
    # "RT-968675419202605",
    # "RT-2502023SAH4G39",
    # "RT-2502024YXRF3WY",
    # "RT-2502025MMEV20W",
    # "RT-968640487745319",
    # "RT-976067518072635",
    # "RT-25020370MRTE40",
    # "RT-250204A7M76MP1",
    # "RT-250204A2H5Q0YQ",
    # "RT-250204AYCWRE96",
    # "RT-969724894590466",
    # "RT-250204A15ASKJC",
    # "RT-969859463149897",
]


async def fix_etax_yvis_cancel_renew(rows):
    coros = []
    for row in rows:
        coros.append(
            send_cancel_and_renew(
                row,
                override={
                    "DOC_ISSUE_DATE": "2025-02-06T00:00:00+07:00",
                    "EMAIL_FLAG": "N",
                    "BUYER_CONTACT_EMAIL": "<EMAIL>",
                },
            )
        )

    try:
        await gather_with_concurrency(3, *coros)
    except Exception as e:
        print(repr(e))
        print("ERROR, saving progress")
        pass

    write_excel(rows, "yvis-cancel-renew-report.xlsx")


class Command(BaseCommand):
    help = "Fix incorrect etax"

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        # company = Company.objects.get(pk=470)

        breakpoint()
        tax_docs = TaxDocument.objects.filter(
            Q(company_id=470), Q(doc_id__in=to_be_cancel_renew)
        )

        rows = []
        for tax_doc in tax_docs:
            rows.append(
                {
                    "doc_pk": tax_doc.pk,
                    "doc_id": tax_doc.doc_id,
                    "company_id": tax_doc.company_id,
                    "company_name": tax_doc.company.name,
                    "fix_status": "PENDING",
                }
            )
        
        asyncio.run(fix_etax_yvis_cancel_renew(rows))
