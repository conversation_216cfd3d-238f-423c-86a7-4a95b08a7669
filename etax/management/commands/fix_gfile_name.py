import logging
import traceback

from django.conf import settings
from django.core.management.base import BaseCommand
from companies.models.models import Company
from core.serializers.serializers import start_of
from etax.management.commands._common import (
    gather_with_concurrency,
    read_excel,
    write_excel,
)
from etax.models import TaxDocument
from etax.views.etax_debugger_views import EtaxRetryAPIView
from picking.models import PickOrder
from services.etax_invoice.d1a_schema import D1aDocument
from services.etax_invoice.etax_service import ETaxService
from datetime import date
import threading
import asyncio
import httpx
import concurrent.futures
from services.google.drive.drive import GoogleDriveService
import time, random


def rename_file(gservice: GoogleDriveService, doc_url: str):
    gfile_id = doc_url.split("/")[-2]
    gfile = gservice.get(gfile_id, fields="id,name")

    if "(cancel)" in gfile["name"]:
        name, ext = gfile["name"].split(".")
        name = name.replace("(cancel)", "")
        name = name.strip()
        new_name = f"{name}.{ext}"

        
        print(f"Renamed {gfile['name']} ___to___ {new_name}")
        gservice.rename_file(gfile_id, new_name)

        return new_name
    else:
        print(f"Skipped {gfile['name']}")
        return gfile["name"]

    time.sleep(random.choice([0, 0, 0, 0, 0, 0.2, 0.5, 1]))


class Command(BaseCommand):
    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        rows = read_excel("fix_rename_gfile.xlsx")
        gservice = GoogleDriveService()

        try:
            for i, row in enumerate(rows):
                if row["status"] == "DONE":
                    print(f"{i+1}/{len(rows)} - skip DONE")
                    continue
                
                if not row["doc_url"] or type(row["doc_url"]) is not str:
                    print(f"{i+1}/{len(rows)} - skip no doc_url")
                    continue

                print(f"{i+1}/{len(rows)}", end=" - ")
                try:
                    rename_file(gservice, row["doc_url"])
                except Exception:
                    row['status'] = "ERROR"
                    continue
                row["status"] = "DONE"
        except Exception as e:
            traceback.print_exc()
        
        write_excel(rows, "fix_rename_gfile.xlsx")
