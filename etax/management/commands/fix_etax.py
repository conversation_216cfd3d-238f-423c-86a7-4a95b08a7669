import logging
import pydash as py_
import traceback

from django.conf import settings
from django.core.management.base import BaseCommand
from companies.models.models import Company
from core.serializers.serializers import start_of
from etax.management.commands._common import (
    gather_with_concurrency,
    read_excel,
    send_cancel_and_renew,
    send_retry,
    send_update,
    write_excel,
)
from etax.models import TaxDocument
from etax.views.etax_debugger_views import EtaxRetryAPIView
from picking.models import PickOrder
from services.etax_invoice.d1a_schema import D1aDocument
from services.etax_invoice.etax_service import ETaxService
from datetime import date
import threading
import asyncio
import httpx
from asgiref.sync import sync_to_async


@sync_to_async
def get_tax_doc_fix(doc_id):
    if TaxDocument.objects.filter(doc_id__startswith=doc_id + "-FIX").first():
        return True
    return False


@sync_to_async
def get_tax_doc(doc_id):
    return TaxDocument.objects.filter(doc_id=doc_id).first()


async def run_fix_etax(file: str):
    rows = read_excel(file)

    num_rows = input("Please enter number of rows to process (number/'all'): ")
    if num_rows == "all":
        num_rows = len(rows)
    else:
        num_rows = int(num_rows)

    coros = []
    for row in rows:
        if await get_tax_doc_fix(row["doc_id"]):
            print(f"- {row['doc_id']} already fixed")
            continue

        if row["fix_status"] != "PENDING":
            continue

        # if row["last_log_msg"] != "400, DOC_STATUS : NEW : DOC_ID duplicate, ":
        #     print("skip last_log != 400, DOC_STATUS : NEW : DOC_ID duplicate, ")
        #     continue

        # Fix 504 error
        # tax_doc = await get_tax_doc(row["doc_id"])
        # last_log = tax_doc.log[-1]
        # status_code = py_.get(last_log, "extra.response.status_code")
        # if status_code != 504:
        #     print(f"- {row['doc_id'], row['company_name']} is not 504")
        #     continue

        send_email = row.get("send_email")

        if row["defect_type"] == "RETRY":
            coros.append(send_retry(row, send_email))
        elif row["defect_type"] == "UPDATE":
            coros.append(send_update(row, send_email))
        elif row["defect_type"] == "CANCEL_AND_RENEW":
            coros.append(send_cancel_and_renew(row))
        else:
            print(f"Unknown defect type: {row['doc_id']} {row['defect_type']}")

        print(f"- {row['defect_type']}, {row['doc_id']} will be processed")

        if len(coros) >= num_rows:
            break

    breakpoint()

    try:
        await gather_with_concurrency(3, *coros)
    except KeyboardInterrupt:
        print("Receive exit command, saving progress")
        pass
    except Exception as e:
        print("ERROR, saving progress")
        pass

    write_excel(rows, file)


class Command(BaseCommand):
    help = "Fix incorrect etax"

    def add_arguments(self, parser):
        parser.add_argument("--file", help="File fix_etax_report", required=True)

    def handle(self, *args, **options):
        # run run_fix_etax() as async
        asyncio.run(run_fix_etax(options["file"]))


# def chunk_array(arr: list, chunk_size: int):
#     """Chunks an array into smaller arrays of a specified size.

#     Args:
#         arr: list
#         chunk_size: The desired size of each chunk.

#     Yields:
#         Smaller arrays (NumPy arrays if the input is a NumPy array, lists otherwise).
#         Yielding allows us to work with very large arrays without loading everything into memory.
#     """

#     for i in range(0, len(arr), chunk_size):
#         yield arr[i:i + chunk_size]


# class Command(BaseCommand):
#     help = "Fix incorrect etax"

#     def add_arguments(self, parser):
#         parser.add_argument("--company", help="Company ID", default="all")
#         parser.add_argument("--dryrun", help="Dryrun", action="store_true")

#     def handle(self, *args, **options):
#         if options["company"] == "all":
#             print("Fixing all companies")
#         else:
#             company = Company.objects.get(id=options["company"])
#             self.fix_etax(company, options['dryrun'])

#     def fix_etax(self, company: Company, dryrun: bool):
#         logr = self.setup_logger(company)
#         logr.info("-" * 50)
#         logr.info(f"Fixing eTax for company {company.id}")
#         logr.info("-" * 50)

#         # fetch all tax documents
#         tax_docs = TaxDocument.objects.filter(
#             company=company,
#             status__in=[TaxDocument.STATUS_SUCCESS, TaxDocument.STATUS_FAILED, TaxDocument.STATUS_ON_HOLD, TaxDocument.STATUS_NEW],
#             create_date__gte=start_of(date(2025, 1, 1))
#         ).prefetch_related("pick_order")
#         logr.info(f"Found {len(tax_docs)} tax documents")

#         # classify defects into 3 catories: grand_total, total_discount, other
#         defects = {"grand_total": [], "total_discount": [], "other": [], "no_error": []}
#         for tax_doc in tax_docs:
#             if tax_doc.status in [TaxDocument.STATUS_FAILED, TaxDocument.STATUS_ON_HOLD]:
#                 defects["other"].append(tax_doc)
#                 continue

#             if tax_doc.pick_order.order_json.get("status") in [
#                 "Voided",
#                 "Partial Voided",
#             ]:
#                 continue

#             err_type = self.classify_defect(tax_doc)
#             defects[err_type].append(tax_doc)

#         logr.info("Classified defects")
#         logr.info(f'GNDT: {len(defects["grand_total"])}')
#         logr.info(f'DISC: {len(defects["total_discount"])}')
#         logr.info(f'OTHR: {len(defects["other"])}')
#         logr.info(f'OK  : {len(defects["no_error"])}')

#         # etax_service = ETaxService.from_company(company)
#         if dryrun:
#             for tax_doc in defects["other"]:
#                 link = self.get_etax_debugger_link(tax_doc)
#                 logr.info(
#                     f"\n"
#                     f"DRYRUN {tax_doc.doc_id} ...\n"
#                     f"    status: {tax_doc.status}\n"
#                     f"    link  : {link}\n"
#                     f"    email : {tax_doc.buyer['email']}"
#                 )
#             return

#         # for other, run retry without email
#         i = 0
#         threads = []
#         for chunk in chunk_array(defects["other"], 3):

#             # Start threads
#             for tax_doc in chunk:
#                 i += 1
#                 thread = threading.Thread(target=self.retry_etax, args=(i, tax_doc, logr, False))
#                 threads.append(thread)
#                 thread.start()

#             # Wait for all threads to complete
#             for thread in threads:
#                 thread.join()

#         # for total_discount, run retry, send email

#         # for grand_total, cancel the document (send email), generate new doc_id, create new doc (send email)

#         # log the result to a file

#     def classify_defect(self, tax_doc: TaxDocument):
#         pick_order: PickOrder = tax_doc.pick_order
#         ok, errors = ETaxService.check_order_d1a_json_data(
#             pick_order.order_json, tax_doc.doc_info
#         )

#         doc_info = D1aDocument(**tax_doc.doc_info)
#         order_json = pick_order.order_json

#         # Compute D1 money amount
#         d1_sum_product_netline = sum(
#             float(x.PRODUCT_SUM_NETLINE_TOTALAMT or "0") for x in doc_info.items
#         )
#         d1_sum_product_discount = sum(
#             float(x.PRODUCT_ACTUALAMT or "0") for x in doc_info.items
#         )
#         d1_bill_discount = (
#             float(doc_info.MONEY_ALLOWANCE_TOTALAMT) - d1_sum_product_discount
#         )
#         d1_grand_total = float(doc_info.MONEY_GRAND_TOTALAMT)
#         oj_grand_total = order_json.get("amount")

#         # Compute OrderJson money amount
#         oj_shipping_amount = order_json.get("shippingamount", 0)
#         oj_sum_product_netline = (
#             sum(float(x.get("totalprice", 0)) for x in order_json.get("list", []))
#             + oj_shipping_amount
#         )
#         oj_bill_discount = order_json.get("discountamount", 0)

#         # Check defect
#         if not ok:
#             if round(oj_grand_total, 2) != round(
#                 oj_sum_product_netline - oj_bill_discount, 2
#             ):
#                 return "grand_total"
#             elif round(d1_grand_total, 2) != round(oj_grand_total, 2):
#                 return "grand_total"
#             elif round(d1_grand_total, 2) != round(
#                 d1_sum_product_netline - d1_bill_discount, 2
#             ):
#                 return "total_discount"
#             else:
#                 return "other"
#         else:
#             return "no_error"

#     def setup_logger(self, company: Company):
#         logger = logging.getLogger("my_dual_logger")
#         logger.setLevel(logging.DEBUG)  # Set the overall logger level

#         # Create a file handler
#         file_handler = logging.FileHandler(f"./.temp/fix_etax_{company.id}.log")
#         file_handler.setLevel(logging.DEBUG)  # Set the file handler level

#         # Create a console handler (logs to stderr by default)
#         console_handler = logging.StreamHandler()
#         console_handler.setLevel(logging.INFO)  # Set the console handler level

#         # Create a formatter (you can have different formatters for each handler if needed)
#         formatter = logging.Formatter("[%(asctime)s] %(levelname)s - %(message)s")
#         file_handler.setFormatter(formatter)
#         console_handler.setFormatter(formatter)

#         # Add the handlers to the logger
#         logger.addHandler(file_handler)
#         logger.addHandler(console_handler)

#         return logger

#     @classmethod
#     def get_etax_debugger_link(cls, tax_doc: TaxDocument):
#         base_url = settings.UI_HOST
#         return f"{base_url}/etax/debugger/?cid={tax_doc.company.uuid}&oid={tax_doc.pick_order.uuid}"

#     @classmethod
#     def retry_etax(cls, index: int, tax_doc: TaxDocument, logr: logging.Logger, send_email: bool = False):
#         logr.info(f"RETRYING {tax_doc.doc_id}: ({index}) ...")
#         tax_doc = EtaxRetryAPIView.retry(
#             tax_doc, send_email=send_email, create_by="SYSTEM"
#         )
#         link = cls.get_etax_debugger_link(tax_doc)
#         logr.info(
#             f"\n"
#             f"    status: {tax_doc.status}\n"
#             f"    link  : {link}\n"
#             f"    email : {tax_doc.buyer['email']}"
#         )
#         return tax_doc
