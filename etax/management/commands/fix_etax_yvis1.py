from django.core.management.base import BaseCommand

from companies.models.models import Company
from core.serializers.serializers import parse_date
from etax.management.commands._common import read_excel
from etax.models import TaxDocument
from etax.views.etax_debugger_views import load_buyer
from services.etax_invoice.d1a_repository import D1aRepository
from services.etax_invoice.d1a_schema import D1aDocument
from services.etax_invoice.etax_service import ETaxService
import concurrent.futures

done = ["250115J7XN3S3V", "RT-250129RBYUSQ88"]

class Command(BaseCommand):
    help = "ยกเลิกเอกสารของ หจก ที่ออกหลังวันที่ 17 ม.ค. 2025"

    def handle(self, *args, **kwargs):
        self.repository = D1aRepository(
            tenant_code="dt2024076",
            tenant_id="a995dc6b-3218-40fd-9bcb-9c99f1a49b4d",
            branch_id="e322a0ed-b41f-4d76-b62c-504a04c62f63",
            api_token="YWRtaW46UGFTUzQzMjE=",
            api_host="https://v2.detax.in.th",
        )
        self.service = ETaxService(repository=self.repository)

        breakpoint()
        to_be_cancelled, to_be_updated = self.get_docs()
        self.cancel_docs(to_be_cancelled)
        # self.update_docs(to_be_updated)

    def get_docs(self):
        rows = read_excel("yvis_หจก_เอกสารทั้งหมด_20250211_1104.xlsx")
        cutoff_date = parse_date("2025-01-18")
        to_be_cancelled = []
        to_be_updated = []

        for row in rows:
            doc_id = row["เลขที่เอกสาร"]
            doc_issue_date = parse_date(row["วันที่เอกสาร"])

            if doc_id in done:
                continue

            if doc_issue_date >= cutoff_date:
                to_be_cancelled.append(doc_id)
            else:
                to_be_updated.append(doc_id)

        print(len(to_be_cancelled), "documents to be cancelled")
        print(len(to_be_updated), "documents to be cancelled")
        return to_be_cancelled, to_be_updated
        # company = Company.objects.get(pk=470)

    def cancel_docs(self, doc_ids: list[str]):
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            for doc_id in doc_ids:
                tax_doc = TaxDocument.objects.get(doc_id=doc_id, company_id=470)

                d1a_doc = D1aDocument(**tax_doc.doc_info)
                d1a_doc.DOC_STATUS = "CANCEL"
                d1a_doc.EMAIL_FLAG = "Y"
                d1a_doc.BUYER_CONTACT_EMAIL = tax_doc.buyer["email"]

                # self.repository.import_document(**d1a_doc.model_dump(mode='json'))
                # executor.submit(self.service.repository.import_document, doc_info)
                self.import_document("CANCEL", d1a_doc)

    def update_docs(self, doc_ids: list[str]):
        for doc_id in doc_ids:
            tax_doc = TaxDocument.objects.get(doc_id=doc_id, company_id=470)
            buyer = load_buyer(tax_doc.buyer)
            options = {
                "override": {
                    "DOC_ID": tax_doc.doc_id,
                    "SELLER_NAME": "ห้างหุ้นส่วนจำกัด ยวิษ สโตร์",
                    "SELLER_TAX_ID": "0103563007739",
                    "SELLER_TYPE_TAX": "TXID",
                    "SELLER_BRANCH": "00000",
                    "SELLER_BRANCH_NAME": "สำนักงานใหญ่",
                    "SELLER_ADDRESS": "55 ซอยประดิพัทธ์ 17 ถนนประดิพัทธ์ แขวงพญาไท เขตพญาไท กรุงเทพมหานคร 10400",
                    "SELLER_CONTACT_PHONE": "0655151522",
                    "SELLER_POSTCODE": "10400",
                    "SELLER_COUNTRY_ID": "TH",
                    "PAYEE_NAME": "ห้างหุ้นส่วนจำกัด ยวิษ สโตร์",
                    "PAYEE_TAX_ID": "0103563007739",
                    "PAYEE_TYPE_TAX": "TXID",
                    "PAYEE_ADDRESS": "55 ซอยประดิพัทธ์ 17 ถนนประดิพัทธ์ แขวงพญาไท เขตพญาไท กรุงเทพมหานคร 10400",
                    "PAYEE_POSTCODE": "10400",
                    "PAYEE_COUNTRY_ID": "TH",
                    "EMAIL_FLAG": "N",
                    "BUYER_CONTACT_EMAIL": "",
                },
                "block_email": True,
            }
            tax_doc = self.service.retry_tax_document(
                tax_doc, buyer, create_by="SYSTEM", options=options
            )
            print(f"RETRY: {tax_doc.doc_id} - {tax_doc.status}")

    def import_document(self, action, d1a_doc):
        ok, _, _, res = self.service.repository.import_document(d1a_doc)
        print(f"{action} - ", ok, " - ", res.status_code)
