from django.core.management.base import BaseCommand

from companies.models.models import Company
from core.serializers.serializers import parse_date
from etax.management.commands._common import read_excel
from etax.models import TaxDocument
from etax.views.etax_debugger_views import load_buyer
from services.etax_invoice.d1a_repository import D1aRepository
from services.etax_invoice.d1a_schema import D1aDocument
from services.etax_invoice.etax_service import ETaxService
import concurrent.futures


done = []
done = [
    "RT-250204AYC4Y23A",
    "250118RDK19D74",
    "250129R1RV379D",
    "25012268AAT3WY",
    "25012264PA6HDH",
    "250129RAVY343Y",
    "250129R1BB5WMV",
    "250129R1B4G9AP",
    "CPS24",
    "250118RCMJN534",
    "CPB79",
    "CPC17",
    "<PERSON>C19",
    "CPC30",
    "CPC38",
    "CPB98",
    "CPB92",
    "CPC29",
    "CPC1",
    "250118T5C4E50X",
    "CPC77",
    "CPD24",
    "CPD39",
    "CPE72",
    "CPE80",
    "CPF1",
    "CPE42",
    "CPE70",
    "250119VPG28C3Y",
]
to_be_new = [
    "CPE64",
    "CPF34",
    "250118R77W1F16",
    "970560138552299",
    "250118TB3BNM8S",
    "CPF32",
    "250119VM3N0GAX",
    "CPF42",
    "25012004V3KDQT",
    "25012006K15M5E",
    "2501201J2EA381",
    "250119VXN3HC2M",
    "963850454423855",
    "CPH5",
    "250120242JTDY3",
    "CPH41",
    "CPH42",
    "2501202BEXBSP5",
    "2501213UAU4233",
    "CPI62",
    "2501201PK7AKN6",
    "CPI79",
    "2501214SH26F44",
    "CPJ15",
    "CPJ19",
    "579888634700335763",
    "CPI98",
    "CPJ14",
    "970963906254626",
    "971408723476939",
    "25012264H63DY8",
    "25012264KCQWMP",
    "971409124664353",
    "25012264PEWHKS",
    "25012264GJ3G7X",
    "25012266CNKXXF",
    "250122662UUP08",
    "250122648DQYNB",
    "971407152946712",
    "2501226504VDUC",
    "2501226563KCBG",
    "964544443128528",
    "2501226982K2TB",
    "2501226A1E4R3R",
    "25012266QQFT4Y",
    "2501226ATRTDFC",
    "25012264QJ74KM",
    "2501226CQ308AH",
    "579902838453799618",
    "2501226FASUVDS",
    "2501226FERP5BK",
    "2501226FYNETK1",
    "CPO50",
    "25012268DY561T",
    "971420924918690",
    "25012269XW37T4",
    "2501226HXFTC43",
    "2501226JNF4CQA",
    "964644601082611",
    "2501226HXC0QCF",
    "CPI88",
    "COI90",
    "2501226KU0NWJB",
    "25012268RGR44A",
    "964541829572670",
    "2501226AAXCVW3",
    "2501226PYJEPHN",
    "2501226GHW26H9",
    "250118SSE75UE0",
    "2501226VEWSGRF",
    "2501212NC2E4RP",
    "CPP53",
    "CPP64",
    "25012279DJ9J9D",
    "964564000196920",
    "2501226H6D8J0F",
    "CPQ29",
    "2501227JT367XV",
    "CPP66",
    "CPJ22",
    "2501226V6W83Q9",
    "2501238GFRW9CJ",
    "CPQ2",
    "2501227DBGHDRX",
    "CPP74",
    "CPO99",
    "CPG88",
    "2501226RY2PSDE",
    "2501239506JCDK",
    "CPS5",
    "CPS79",
    "2501239H3S3NWH",
    "CPQ37",
    "25012266KKV8P7",
    "250122652G861G",
    "2501239TKFYMWT",
    "CPT26",
    "CPT22",
    "CPT16",
    "CPS74",
    "250124A6R6R40A",
    "2501202HJQ11M9",
    "250124A9750XRT",
    "2501238VR344N3",
    "CPQ41",
    "250122646YXE8E",
    "CPS57",
    "CPU50",
    "25012267MPCU3A",
    "25012267HR2GCA",
    "25012142CDGS5Q",
    "CPS49",
    "2501239URDJK27",
    "CPU98",
    "964544433315722",
    "25012020DT8Q1E",
    "CPS36",
    "250124BX2MS3N5",
    "250124BVSSA3HW",
    "CPV38",
    "CPV34",
    "250124BAUGW826",
    "250119TQQ25QGN",
    "25012264HKE3N5",
    "CPU64",
    "CPU79",
    "250124CET76JWM",
    "2501226P34JPV1",
    "CPV64",
    "CPJ3",
    "CPV82",
    "CPW28",
    "CPW50",
    "2501227H947BEQ",
    "CPV88",
    "2501238RGX5T7T",
    "CPW38",
    "250125CQQSATK9",
    "CPW32",
    "CPX68",
    "250122651RCGW5",
    "CPX94",
    "250125CY7JJPS7",
    "CPX81",
    "250125E5MJ7UC9",
    "250125D268CX6H",
    "CPT69",
    "CPX63",
    "2501238B98T1JR",
    "250125CXM2X69T",
    "CPY48",
    "250125EAFTTNJP",
    "250125EC6QGV13",
    "CPY80",
    "CPT27",
    "965838650240650",
    "250125CQ62T6PT",
    "CPY31",
    "CPZ37",
    "CPZ20",
    "CPZ58",
    "250125EVRVE67B",
    "CPZ45",
    "CPZ90",
    "CPZ82",
    "250125F02CG7WX",
    "CAQ7",
    "CPZ60",
    "CQA39",
    "CPZ94",
    "CPZ31",
    "250125ECMF4PFB",
    "CPZ79",
    "CPZ79.",
    "CPY20",
    "CPZ25",
    "2501237R0KAYSU",
    "CPG23",
    "2501238GHK2MQA",
    "250124CJTD0H6A",
    "250119VGA2YBS3",
    "CQB44",
    "CQB55",
    "CQB84",
    "250125CUKAA5JR",
    "250126GYJFNG1C",
    "CQB34",
    "CQB85",
    "579903890957306587",
    "2501212K0SER2U",
    "CQC69",
    "CQB88",
    "CQC53",
    "973165583773309",
    "CQC29",
    "250127HP6SSB8H",
    "CQC42",
    "2501227CER91W7",
    "250125EMH1JDGY",
    "971407382066347",
    "CQD48",
    "250125CQPTS6MD",
    "2501213Q3U0G7J",
    "2501226HY7MFD2",
    "25012279J38CP3",
    "250127JTYGMQ59",
    "250126GRWT7VFA",
    "CQD85",
    "250122643JAYKP",
    "250125D2END4PD",
    "25012005UBYNG5",
    "250128M8JU7VAJ",
    "250125CQKPQCRT",
    "CQE61",
    "972488900827154",
    "971733352796846",
    "250128NGG6NPYJ",
    "250125DR8BTEU5",
    "250128P9M231A2",
    "250126H22FC669",
    "2501200CJ61TDF",
    "CQG32",
    "CQG65",
    "CQG73",
    "CQG69",
    "250128NS708TVR",
    "CQF64",
    "CQG85",
    "250127HWQ903KE",
    "CQE43",
    "973905978112764",
    "CGQ28",
    "250126G35DRTAK",
    "250125ETQG1YT1",
    "250126GSR86Q7Y",
    "25012264KT2UHE",
    "250129R1BMPC3N",
    "250129R1U3N0GM",
    "250129R2AHMBW3",
    "250129R1A329WH",
    "250124C6PMP6D0",
    "250129R4RM97S9",
    "250129R1QYF05V",
    "250129R1MC2NGF",
    "250129R1CVPY41",
    "250129R1T62TH8",
    "250129R1QSR79K",
    "250129R32076WU",
    "250129R1BW9RYY",
    "250129R21V5A53",
    "250129R2Y1SDCV",
    "250129R2F2HXS2",
    "250129R1BHSDVT",
    "250129R20TSC58",
    "250129R1CN47HP",
    "250129R1CH81J2",
    "250129R1DETJWS",
    "250129R2J0YQCY",
    "250129R2QYRGP0",
    "250129R25UXSKT",
    "250129R1BDYMBC",
    "250129R2PPP8G5",
    "250129R21W2R54",
    "250129R1ERSPVS",
    "250129R1CURB73",
    "250129R1KN7UT0",
    "250129R1EYFEDE",
    "250129R1T27850",
    "250129R1SCBPDH",
    "250129R1SRRBD9",
    "250129R1A6X7G3",
    "250129R1B98VTB",
    "250129R1JD4QU0",
    "250129R1WSG8DU",
    "250129R1VK94KX",
    "250129R1FRAAMX",
    "250129R1A33UH8",
    "250129R26WAMAT",
    "250129R23DQVKH",
    "250129R1ATXK2V",
    "250129R1S8FB7K",
    "250129R1CRVKAR",
    "250129R1EUQ7Q5",
    "250129R1C7REHS",
    "250129R1DKKPHH",
    "250129R210FPRK",
    "250129R1NK5MSM",
    "250129R20VKTXV",
    "250129R1DCVXJQ",
    "250129R19VC4ET",
    "250129R1G4NBSB",
    "250129R1SVGQAU",
    "250129R1BC2T8U",
    "250129R20H57YK",
    "250129R1EF8TMS",
    "250129R1GJXC86",
    "250129R1ATXV5B",
    "250129R1BY6VYP",
    "250129R1EBDVD2",
    "250129R1B5EY5N",
    "250129R1FU40VC",
    "250129R1BB3KDU",
    "250129R1AQ4QCX",
    "250129R1ESQCVM",
    "966648211702969",
    "250129R1F2AFEK",
    "250129R1DBYTRX",
    "250129R25EH4SK",
    "250129R1A4XYYU",
    "250129R22JYQV2",
    "250129R1B4ECMY",
    "250129R2CW0FYC",
    "250129R1GH24VE",
    "250129R29N0X9N",
    "250129R1AQ37NP",
    "250129R19UDM7N",
    "250129R1BEY6NF",
    "250129R1BA7JW9",
    "250129R1Q0XAFM",
    "250129R1CYK983",
    "250129R1C7PUC4",
    "250129R2NWYTKW",
    "250129R1AGE6JV",
    "250129R1X5VDGR",
    "250129R1AGFHGK",
    "250129R1A8U7RB",
    "250129R1XEDKMS",
    "250129R1A50HBM",
    "250129R78NT8QR",
    "250129R1NBJW44",
    "250129R19RJEXF",
    "250129R1AS15NW",
    "250129R1F5824F",
    "250129R1GMWTN9",
    "250129R1KQ4DKF",
    "250129R1A15FGU",
    "250129R1BJSTK2",
    "250129R1GAEH70",
    "250129R1B3HKF0",
    "250129R1QBGAND",
    "250129R1BPK6KC",
    "250129R1B3HKF0-FIX",
    "250129R1BD2D3C",
    "250129R1GRQDYJ",
    "250129R1BC2J3C",
    "250129R1T557UR",
    "250129R1CJ6NDK",
    "250129R1HCQTN2",
    "250129R28U9H3M",
    "250129R23S4H9C",
    "250129R1SXD3DG",
    "250129R1C309RA",
    "250129R1FGKU0G",
    "250129R2JTSU72",
    "250129R1T8XNXX",
    "250129R9DY3JUT",
    "250129R1S6JNW7",
    "250129R202VG2P",
    "250129R1FJKQ98",
    "250129R1A07U68",
    "250129R19F0TTR",
    "250129R1S3RD72",
    "250129RAFTGXW5",
    "250129R1DKMWBG",
    "250129R9Y967UQ",
    "250129RACA02K1",
    "250129R1AYQXG6",
    "250129R19Y9SB8",
    "250129R228FEDH",
    "250129R1DCYW7N",
    "250129R9WRKR2A",
    "250129R1QFANC7",
    "250129RA48EPE5",
    "250129R1CTTKK3",
    "250129RB0JGJBT",
    "250129R1BKPY3W",
    "250129RAVQE18V",
    "250129RA4YC6QB",
    "250129R1C132SX",
    "250129R1CPY7K8",
    "250129R9NN73HB",
    "250129RC03EDXJ",
    "250129R1DW6DX0",
    "250129R1BNKHYW",
    "250129R1A41JRG",
    "250129R1DSBVSU",
    "250129R9J1QPTH",
    "250129RBW502C8",
    "250129R292XA36",
    "250129R1FDREDA",
    "250129R1WF0Q2B",
    "250129R1EUPUCR",
    "250129RDCDHRBW",
    "250129R1TS64CS",
    "250129R1CFBAYB",
    "250129R2QARWPK",
    "250129RBK1Q8H1",
    "250129R9N072R9",
    "250129RDNJCCN1",
    "250129R1BD0F06",
    "250129R1A31JV7",
    "250129R1SXDHPS",
    "250129RAKJMBDP",
    "250129R1YCY7JS",
    "250129RAS258C3",
    "250129RAD5M428",
    "250129RAA4BHDN",
    "250129R1DMK523",
    "250129R1AAR73N",
    "250129R1TP8PYH",
    "250129R1FPCTG7",
    "250129R1BY6KA7",
    "250129R1ADJFT9",
    "250129R1CDGD74",
    "250129R1CXKSBQ",
    "250129R1HAU063",
    "250129RDNP6A9W",
    "250129R1BHSQK6",
    "250129R1ATW67U",
    "250129R1BC2Y26",
    "250129R1E5R173",
    "250129R1BQJJUM",
    "250129R1ASX0JX",
    "250129R1BMPUE2",
    "250129R1A32D1F",
    "250129R1R2BFGM",
    "250129R1CDEM7B",
    "250129R19GYWGS",
    "250129R1AGFHFC",
    "250129R1BE059T",
    "250129R1J6GVHA",
    "250129R1PDVJER",
    "250129R1DPEKDX",
    "250129R1BUA0EK",
    "250129R1CQYGYK",
    "250129R1C7REFN",
    "250129R1D1HJH8",
    "250129R1JNRW0F",
    "250129R1DX2D7P",
    "250129R1BW7XE9",
    "250129R1D0GYWN",
    "250129R1BW8CRD",
    "250129R1CHA6AK",
    "250129R1F49D8B",
    "250129R1EMYQUU",
    "250129R1BGVKUR",
    "250129R1B7BH7M",
    "250129R1D4CQJH",
    "250129R1D93KDT",
    "250129R1AS0P4K",
    
    "250129R1BC3X4R",
    "250129R1A8SQAD",
    "250129R1CN3W3E",
    "250129R1BPJA5Q",
    "250129R1EQVDMR",
    "250129R1B7BH7Y",
    "250129R1AGEMPS",
    "250129R1E1W58N",
    "250129R1EF97AK",
    "250129R1AWTSRK",
    "250129R1EAGMD3",
    "250129R1AQ2X3B",
    "250129R1BUB4GX",
    "250129R0WU343P",
    "250129R10VCBMJ",
    "250129R1C9NPKY",
    "250129R1A5XWB6",
    "250129R1C4UEXR",
    "250129R1BB3VAY",
    "250129R1BA6BYH",
    "250129R1B969HX",
    "250129R1CFD6HE",
    "250129R19VC4F3",
    "250129R1BVAT0K",
    "250129R1CPY7J2",
    "250129R20J4GYE",
    "250129R19H03W4",
    "250129R1HCPQ16",
    "250129R246FREA",
    "250129R1F9XH0B",
    "250129R1AEJ9K2",
    "250129R1CN4W3S",
    "250129R1AXRTKV",
    "250129R1SVJDNA",
    "250129R299HXVC",
    "250129R1CN22NW",
    "250129R1SADBV4",
    "250129R204RNWJ",
    "250129R21DUHV9",
    "250129R2JN2RER",
    "250129R1CN4M4K",
    "250129R245HQYU",
    "250129R1RBVG3K",
    "250129R1S5PBX1",
    "250129R2HY42M0",
    "250129R1DEVP96",
    "250129R25JD2KD",
    "250129R1EH51TQ",
    "250129R1DU8Y62",
    "250129R1J3K8RP",
    "250129R1CG9HPX",
    "250129R2CHFYRW",
    "250129R2D5H5PN",
    "250129R1AUWJDJ",
    "250129R2AY1B0M",
    "250129R1B79TXX",
    "250129R1BGUJ21",
    "250129R21S7NHR",
    "250129R1FGMQYQ",
    "250129R1M993GG",
    "250129R1CAK34N",
    "250129R1SPUEYR",
    "250129R1B2HAA3",
    "250129R1PCX4RX",
    "250129R1B89TQY",
    "250129R1S4Q0EJ",
    "250129R1XBM27R",
    "250129R1NQ0MD5",
    "250129R1BNKHXY",
    "250129R1A40SKC",
    "250129R27GD7J7",
    "250129R1CYJ37S",
    "250129R1BVAT03",
    "250129R1BGVKVD",
    "250129R1CH9SV1",
    "250129R1MKRXKU",
    "250129R1BTCCB6",
    "250129R1C11GW5",
    "250129R1DGSJKR",
    "250129R2156R1S",
    "250129R1B4DSBX",
    "250129R225MUUH",
    "250129R1AXRCJN",
    "250129R1J4JPVQ",
    "250129R1ETQ2TK",
    "250129R237XV1J",
    "250129R1SJ0J9S",
    "250129R1SACJWE",
    "250129R1E7KQ9Y",
    "250129R1F58F2C",
    "250129R1TQ7R7F",
    "250129R1H625S6",
    "CRL73",
    "250129R1A50NJ8",
    "250129R1AAP2E0",
    "250129R1DMH809",
    "250129R1BJSF3K",
    "250129R1CM5KCS",
    "250129R1BUAGBW",
    "250129R1C2Y26S",
    "250129R1ASXV2S",
    "250129R1T61HCC",
    "250129R1CDGD88",
    "250129R1SSPY65",
    "250129R1T53UEP",
    "250129R1TDPJHA",
    "250129R1B1JB3N",
    "250129R1DKM4J3",
    "250129R1DRE4VJ",
    "250129R1SNUUY3",
    "250129RA4ADA53",
    "250129RAB6PY4H",
    "250129R9Y97Y0N",
    "250129R9T810KQ",
    "250129RA5TYAUM",
    "250129R1AHBYWX",
    "250129R9Q104K1",
    "250129RA25Q6GJ",
    "250129R1C9NPJQ",
    "250129R94Y592Q",
    "250129R9K19FRV",
    "250129R9B8A5RC",
    "250129R1EH765C",
    "250129R1GC8X7X",
    "250129R1DHRE04",
    "250129R1B6CJ8M",
    "250129R1SADRK1",
    "250129R2PABWBG",
    "250129R1QWK4HA",
    "250129R1A9Q957",
    "250129R1CXM1RW",
    "250129R1CWQA0A",
    "250129R1D6922Y",
    "250129R1DSB81P",
    "250129R1DPEKE5",
    "250129R1BB609D",
    "250129R1MY4KE7",
    "250129R1H54H6M",
    "250129R1BDY8DY",
    "250129R214A8H3",
    "250129R1F39HTA",
    "250129R1AHC642",
    "250129R1SYCVA4",
    "250129R1MHUSJU",
    "250129R19MR6EH",
    "250129R1B96WG9",
    "250129R20QVMP5",
    "250129R1SACVCG",
    "250129R1G9F2J3",
    "250129R1T0AK0E",
    "250129R19X9Y78",
    "250129R2F0NHNX",
    "250129R2A92EJA",
    "250129R1SSN91G",
    "250129R1QVKEF8",
    "250129R1A14VE9",
    "250129R1CPYEUR",
    "250129R1TCRJ0C",
    "250129R28CY5CS",
    "250129R1F74CJ9",
    "250129R1CBHTYF",
    "250129RAQJDT2T",
    "250129R1PGSPX7",
    "250129R1DKK4ES",
    "250129R1U8CPKG",
    "250129R248DGJ5",
    "250129R1DPGXHG",
    "250129R28FSNGU",
    "250129R2P1QF6V",
    "250129R1AYQBU0",
    "250129R1D59YJ7",
    "250129R1BUA0EG",
    "250129R1B98VU0",
    "250129R1AAQA63",
    "250129REFPQDCR",
    "250129R1NP2WY2",
    "250129R1ADHCGY",
    "250129R9TXY1YH",
    "250129RDVR608A",
    "250129R1D2EXM4",
    "250129RC70NWS0",
    "250129R1CP207F",
    "250129R1CWNJ59",
    "250129R20MYTJ8",
    "250129R1T1A1DG",
    "250129R9EP1MX7",
    "250129R9MJSUAX",
    "250129R1G6K3UV",
    "250129R1RBVNMS",
    "250129R1T45XAA",
    "250129R1K17CPW",
    "250129REJANB8M",
    "250129R1ATWF07",
    "250129R1PSA896",
    "250129R9AS4BJ1",
    "250129R1S6JCKE",
    "250129R1BEY6P1",
    "250129R1CJ9BJY",
    "250129R1DRCUWM",
    "250129R1C6TF2R",
    "250129R19VEAQN",
    "250129R1C8R3AQ",
    "250129R1AAQA5X",
    "250129R19JUYCD",
    "250129R1AEG8RV",
    "250129R1EXJ854",
    "250129R1B1MHV2",
    "25012265FHV582",
    "250129R1QXHPFW",
    "250129R1BUCSNH",
    "250129R1AWRXPF",
    "250129RJA4C0MQ",
    "250129RJC0GMW4",
    "250129R1D78MF4",
    "250129R2901708",
    "250129R1AEGV87",
    "250125E05YMNHM",
    "250129RGHWS5GH",
    "250129RJHD259D",
    "250129R1XED2AP",
    "250129RGYX0GEC",
    "250129R1WEYD5F",
    "250129RHKJGFCG",
    "250129R1CUS074",
    "250129RDRV5CDE",
    "250129R1FMFAS4",
    "250129R1CAKFDS",
    "250126F61R01A9",
    "250129R9GYFFJ4",
    "250129R967737S",
    "250129R2H4D6GB",
    "250129R20M3JN0",
    "250129R1K8TPAN",
    "250129R1MTDUHD",
    "250129RMV5W9UT",
    "250129R2D6G02V",
    "250129R1EYHBQT",
    "250129R9FFQRC0",
    "250129RBW273T1",
    "250129RMT1724P",
    "250129RNJ5PHXK",
    "250129R1CTSCDF",
    "250129RC8P37VB",
    "250129R1GQQVXF",
    "250129RAA75K9P",
    "250129R1BHSQHN",
    "250129R8QH0XCF",
    "250129RNQRXC79",
    "250129RE4QQPYE",
    "250129R19KUS50",
    "250129R1B1KET9",
    "250129R1THHC70",
    "2501239T0CNXWU",
    "250129R1D3DNAH",
    "250129R1S6KBQN",
    "250129RNMSJPS9",
    "250129R1KN61V5",
    "250129R1TCR6WF",
    "250129R22TK8WS",
    "250129R1A151T3",
    "250129R1B969GP",
    "250129R1CSVPXA",
    "250129R1C11VM2",
    "250129R1R92RSK",
    "250129RTJ263SE",
    "250129R1GXEMNA",
    "250129R1FPBRHU",
    "250129RTS0BEV1",
    "250129R1DEUU61",
    "250129R1BJSF4N",
    "250129R1S5NMEY",
    "250128MA1U7HYW",
    "250124BF6W7FSB",
    "250129R1K068MB",
    "250129R23GJT7V",
    "250129RUNNSQ63",
    "250129R1AK8YQU",
    "250129R239VEWA",
    "250129RU4T0TPB",
    "250129RAGGEE40",
    "250125EYC23USK",
    "250129R19THD75",
    "250129R1DDWXKW",
    "250129R1SUJYYC",
    "250129R9REUHHG",
    "250129RVHEKTN1",
    "250129R1DB1PHY",
    "250129R1B0N5FA",
    "250129R1E6MYKU",
    "250129R1C6TF22",
    "250129R1DMJDBR",
    "250129RAGEG5H0",
    "250129R1MTBT4R",
    "250129R20H7PX0",
    "250129RACX0AYB",
    "250129R242NX5B",
    "250129R20N0WC4",
    "250129R1FQARP0",
    "250129R1HWX3B1",
    "250129R1SH47AA",
    "250129R222RE9X",
    "250129R1AS1SJP",
    "250129R9312V5N",
    "250129R9P2HF65",
    "250129R1EUPUCG",
    "250129RNRCYXXY",
    "250129RV55NRBS",
    "250129R1CUSVJ1",
    "250129R9VUWJYS",
    "CRD43",
    "250129R1NN4T4K",
    "250129RA0WKYUJ",
    "250129RA97S4MK",
    "250129R9KYT8MW",
    "250129R9GAG051",
    "250129R21W3DT9",
    "250129R99NSSWV",
    "250129RA30EBCS",
    "250129R2GQY5FA",
    "250129R1GQSS70",
    "250129R1BFWJFY",
    "250129R2B9E9UU",
    "250129R1H9VB24",
    "250129R1F56QAR",
    "CRW4",
    "250129R9MA8DUS",
    "250129RKHTQBQX",
    "250129R1BE1HG9",
    "250129R1SXETE2",
    "250129R1BE0GGW",
    "250129R1NEFAK3",
    "250129R1GD765X",
    "250129R1SKYMQ5",
    "250129RACEQCAG",
    "250129R1B3GJ1Y",
    "250129R1CJ86PB",
    "250129RS9D9RSH",
    "250129R1BD0F15",
    "250129R1ATXV4M",
    "250129RKC5H8AS",
    "250129R1F0DF1Y",
    "250129R1T548YR",
    "250129R1EYGTDD",
    "250129R21582VX",
    "250129R346PUEF",
    "250129R21JHKD9",
    "250128PBM5FEM2",
    "250129REXAYTYA",
    "250129REGS1KEJ",
    "250129R1C4UE0M",
    "250129RM7E42QC",
    "250129R1E4SH3A",
    "250129R1DMKB3P",
    "250129RCXBD6DS",
    "250129RH1P98RW",
    "250129R1MUANTH",
    "250129R9N4X5EY",
    "250129R1E8J5BN",
    "250129R21Y0GXE",
    "250129R21HMWTF",
    "250129R1J0RA8P",
    "250129R8MMFRQG",
    "250129R2JWMBVW",
    "250129R1NBK90Y",
    "250128PE16QY3W",
    "250129RUHCF8TA",
    "250129R1C5THHP",
    "250129R1CYJEEE",
    "250129R20TRXG7",
    "250129R1GSNDGH",
    "250129R22QQPP2",
    "250129R1CWNE8R",
    "250129R1CJ86R1",
    "250129R1NM3S8T",
    "250129R23PA7RH",
    "250129R22VGKVX",
    "250129R1T557VF",
    "250129RAJ9N8TJ",
    "250129R1AWR4QN",
    "250129R21BUY53",
    "250129R1D2FH6S",
    "250129R1SJ1TYN",
    "250129R298MC8R",
    "250129R1DRB5VY",
    "250129R2A66VAW",
    "250129RBJ9WQD2",
    "250129R1S7HCF3",
    "250130S85J71UU",
    "250129R1AEHG22",
    "250129R1H6YUKX",
    "250129RS5FF558",
    "250129RXMK8553",
    "250129R19JUMTH",
    "250129R1AK9W4W",
    "250129R1B4EN95",
    "250129R1CQ1GYG",
    "250129R1EPXEGM",
    "250129R2CJGMV3",
    "250129R1B7AQDF",
    "250129R1C21P69",
    "250129R95DE1CS",
    "250129RA2TN6M1",
    "250129RC9B4FUC",
    "250129R1CRX3UH",
    "250129R28B2CDS",
    "250129R1D76G78",
    "250129R1T1A621",
    "250129R24F3YUY",
    "250129R1B3G7YN",
    "250129R1CAN3VS",
    "250129RUSRDE61",
    "250129R1C9PEF5",
    "CRW13",
    "CRT96",
    "250128PJ42B3SX",
    "250129R219YYPA",
    "250129R1YRDG2W",
    "250129R1KN84CB",
    "250130T8YNTJQV",
    "250129R23T2FGX",
    "250129R1D694Q0",
    "250129R1AJAQM4",
    "250129R1UF1YWG",
    "250129R34PYK36",
    "250129R1C8P17V",
    "250129R1AN67WQ",
    "250129R9VN8TGQ",
    "250129R1DQE9SV",
    "250129R22G5N1E",
    "250129R1PRBRCD",
    "250129R1SNU5CB",
    "250129RBPYN56G",
    "250129R1B0MA0P",
    "250129R2HPHPFQ",
    "250129R1AGEMPU",
    "250128MYM1C4XK",
    "250129R1D689EN",
    "250129R1SVGM1Q",
    "250129R9KWVKVC",
    "250129R1B89AYQ",
    "250129R1BGTV83",
    "250129R1EK2YRR",
    "250129R1BMPK7C",
    "CRX27",
    "250129R1C7PUB3",
    "250129R9JMVDPK",
    "250129R1CJ8S5H",
    "250129R1BVA31B",
    "250129R1BY6AU3",
    "250129RB4J9EFQ",
    "250129R1W6CKYK",
    "250130TJ0JTHB0",
    "250130THHEB1HN",
    "CRY59",
    "250129R9RCWG92",
    "250129R1DV5N9X",
    "250129R1GF6245",
    "250129RNFSDT2F",
    "250129R1TEP8B8",
    "250129R1PFSDKP",
    "250129R23VYF1U",
    "250125DMS2U50S",
    "250129R1QUP73W",
    "250129R1SSMW9N",
    "CRZ61",
    "250129R2RGX34R",
    "250129R1CCGC3T",
    "250129R1B97785",
    "250129R1BV8JUQ",
    "250130TSBCSGEX",
    "250129RF8TPJ7D",
    "250130TS8M2VUX",
    "250129R1C3W29T",
    "CRZ80",
    "250130TSQSHTCU",
    "250129RC9RDD4F",
    "250129R1BD0YYK",
    "250129RB7ERDM9",
    "250129R1AFGYCY",
    "250129R1AHC63R",
    "25012255T64PE8",
    "250129R1CWN1KY",
    "250125EX3NW1F9",
    "250129R1D3E9JW",
    "250129R1XJ8H8D",
    "250130TY532RDH",
    "250129R1DEU5FD",
    "250129R1J6F35H",
    "250129RC50UJRW",
    "250129R1PGQGSX",
    "250129R1QH53W0",
    "250129R1AR16CQ",
    "250129R1MA6U47",
    "250130TW1TN9GF",
    "250129R1BW9CV8",
    "250130TT8536S1",
    "250130U22997TK",
    "250122666G8P16",
    "250129R1CFCDVQ",
    "250129R1PHP7SQ",
    "250129R1KP5JXM",
    "250129R1EMY22R",
    "250129R1A33EX5",
    "250129R1V265RK",
    "250129R1TT30TS",
    "250127KT48B11N",
    "250129R1S8FHP2",
    "250129R1ADKHB7",
    "250129R1BVBB86",
    "250129R1D1G7DW",
    "250129R1HGH6VP",
    "CSC99",
    "250129RAPKWG1H",
    "250129RH4V94HD",
    "CSC80",
    "250129R1CP2HQ0",
    "250129R1BFXHRW",
    "250129R1A15FFW",
    "CRV92",
    "250129R1C4WVHT",
    "250129R1AARH9C",
    "250129R1S5NME4",
    "250129R1AS0H1W",
    "250129R1GG5RCC",
    "250129R34AHQCU",
    "250129RAF99QF5",
    "250130U9EXCR25",
    "250130U9QPRH20",
    "CSD13",
    "250129R1BTDHR9",
    "250129R1CAJFTB",
    "250129RBS75CQQ",
    "250130U357Y9B9",
    "CSC43",
    "250130UCMUY390",
    "250129R1BEYUQR",
    "250125EWYTWDNJ",
    "250129R1BTCY3B",
    "250129R1A8SQBS",
    "250129RAY4QFRD",
    "250130UA9ACPKK",
    "250129RATX8PEB",
    "250129R1HKC84J",
    "250130U6TXX980",
    "250129R1EWKQEC",
    "250129RWGK7YEG",
    "250129R1BRFWKC",
    "250130TKEHC1T7",
    "CSD69",
    "250129R1E4S08T",
    "250129RA7N5KP6",
    "250129R204S1X2",
    "250130TVATF3NF",
    "250129R1T537QJ",
    "250129R1CM52DC",
    "250129R1ADHYD9",
    "250125E9S63US6",
    "CSE10",
    "CSD85",
    "250129R1E6QB24",
    "250129R20K3QVG",
    "250129R1B9811V",
    "250129RBKE46JA",
    "250129R1DPE55P",
    "250129R2HKMYCY",
    "250129RFD143W0",
    "250130TK1JFMJR",
    "250131USYX36BR",
    "250131USYRCTSA",
    "250124A6QEXW1C",
    "RT-250129R1ATXV5Q",
    "RT-250129R1KVUV1J",
    "RT-250129R19PMYA1",
    "RT-250131USYSAQPW",
    "RT-250129R1AGEAV6",
    "RT-250129R1A5XBUE",
    "RT-250129R1AHCDRT",
    "RT-250129R1AUTYRJ",
    "250129R1MD0VQ0",
    "RT-250130TU3GJ4Q7",
    "RT-250129R1CUSNY3",
    "RT-250131UT0H6Q7R",
    "RT-250129R1BMMSYA",
    "RT-250129R1AVSF3S",
    "RT-250129R1AQ4QD3",
    "250129R1WC2B7B",
    "RT-250129R9K8UYRM",
    "250129R1SH4G98",
    "RT-250129R2184MMQ",
    "RT-250129R1FU54BE",
    "RT-250129RAD6K7WU",
    "RT-250130TUA57Q8Q",
    "RT-250130TJ6U1RA6",
    "250130U6H37PX4",
    "RT-250129R1BA55XA",
    "RT-250129R1ADHCFB",
    "RT-250129R212D9U1",
    "RT-250129RDDPHEWK",
    "RT-250131W03EFDUG",
    "RT-250129RE4G5T2W",
    "RT-250129R1FU62CP",
    "RT-250129R1T63Q97",
    "RT-250129RE7AP8JH",
    "RT-250129R22F6GFH",
    "250129R1KYPJXD",
    "RT-250129R245H5VT",
    "RT-250129R1S7JRA5",
    "RT-250129RJCNFJ91",
    "RT-250129R2GYKDFW",
    "RT-250129RCA7P300",
    "RT-250129RX8J00FB",
    "RT-250130TQP6G2CM",
    "RT-250129R9JNT75D",
    "RT-250129R1TVX1CS",
    "RT-250129RHAEN9TV",
    "RT-250129RBYUSQ88",
    "RT-CPC29-FIX",
    "RT-250125CTP7HVAN",
    "RT-250126FEDA1P5F",
    "RT-250128NYMB4KC8",
    "RT-250128M8SA5Q6K",
    "RT-250128M8Q0Q2PS",
    "RT-250129R1FGP97X",
    "RT-250129R1EVKU95",
    "RT-250129R1T46A37",
    "RT-250129RTPBEDVF",
    "RT-250129R1AR2SCH",
    "RT-250129R1BMPK76",
    "RT-250129RA3AWCT4",
    "RT-250129R1S3RD64",
    "RT-250129R1WB5X35",
    "RT-250129REECQPM2",
    "RT-250129R21U5PQQ",
    "RT-250129R1CVPY5B",
    "RT-250129RHUWHEGX",
    "RT-250129R1HN9RU3",
    "RT-250129R1DX3J1C",
    "RT-250129R1AJAXYY",
    "RT-250129R1AS04V2",
    "RT-250129R1GJ1R3U",
    "RT-250129RB7T7JRE",
    "RT-250129R1A32CYW",
    "RT-250129R1DJMGPG",
    "RT-250129R1A22X20",
    "RT-250129R1C13D65",
    "RT-250129R1D2EXMN",
    "RT-250129R1E3SXJX",
    "RT-250129R1F0D8ND",
    "RT-250129R9NGEPQJ",
    "RT-250129R1BFXHR4",
    "RT-250129R1HAU6YC",
    "RT-250129R1FYVYKK",
    "RT-250129S3FBMNC5",
    "RT-250129R21FQWXY",
    "RT-250129R9YW7PA5",
    "RT-250129R1KM9R98",
    "RT-250129R1B3FAP1",
    "RT-250129R52FXYBS",
    "RT-250129RXJ8VBSF",
    "RT-250129RV6GJF6U",
    "RT-250129R2M5N82U",
    "RT-250129R319AN9E",
    "RT-250129R241R0PA",
    "RT-250129RA7YMKWH",
    "RT-250129RDTMGAEU",
    "RT-250129R1M3J276",
    "RT-250129R1F5823K",
    "RT-250129R1N7R7AT",
    "RT-250129R1ASYYDU",
    "RT-250129R1NH9T3Q",
    "RT-250129R1N4UGTH",
    "RT-250129R1CAK338",
    "RT-250129R9GQUSS0",
    "RT-250129R19G0DEM",
    "RT-250129RDVGHK0D",
    "RT-250129RPQR9CN4",
    "RT-250129R1BKR207",
    "RT-250129R1AUUKA0",
    "RT-250129R1T0BU7X",
    "RT-250129R29QVSJ2",
    "RT-250129RA3A0BHU",
    "RT-250129R31GVWJR",
    "RT-250129R1AQ37QD",
    "RT-250129RUP27NQY",
    "RT-250129RASWRF1R",
    "RT-250129R1C9KRNW",
    "RT-250129R21HKRHY",
    "RT-250129RDDX5R7T",
    "RT-250129R98V28YW",
    "RT-250129R1GG5RCF",
    "RT-250129R1B6DEPJ",
    "RT-250129R1H26SYK",
    "RT-250129R1E4SQX7",
    "RT-250129R20DE10B",
    "RT-250129R1CHA68Y",
    "RT-250129RFTVKU7X",
    "RT-250129R2U02GSB",
    "RT-250129RFQNK6A0",
    "RT-250129R247F4HA",
    "RT-250129RJ4BD6AS",
    "RT-250129R1A151TC",
    "RT-250129R1W4FFHW",
    "RT-250129RA8C1UUG",
    "RT-250129R1JUE58F",
    "RT-250129R1CSTNNJ",
    "RT-250129R2HB3JX3",
    "RT-250129R1A08H9W",
    "RT-250129R1B88Q4U",
    "RT-250129R1CSVPW5",
    "RT-250129RAH79S6H",
    "RT-250129R29J4HJC",
    "RT-250129RBD04572",
    "RT-250129R1C8P5C3",
    "RT-250129R221UE5H",
    "RT-250129R9UVG9EB",
    "RT-250129RKRYJ0FP",
    "RT-250129RQ1K110G",
    "RT-250129R23PAN0E",
    "RT-250129R1FCSNHS",
    "RT-250129R9ANA197",
    "RT-250129R9GH4FKU",
    "RT-250129R1E3TWJ8",
    "RT-250129R1H19FQ2",
    "RT-250130TRGP84TS",
    "RT-250130TU6TDT8W",
    "RT-250130TXT386XT",
    "RT-250130UCPTWY9P",
    "RT-250130TRPWJ76V",
    "RT-250130TRSFMB8U",
    "RT-250130UCPYMF6Q",
    "RT-250130TJRU3CBW",
    "RT-250130U5RXSX7Y",
    "RT-2501310XEGDVUT",
    "RT-250131UT0EAMQJ",
    "RT-25013102GE7WMV",
    "RT-250131UT0QVPWS",
    "RT-975124131986776",
    "RT-25020118Y4708P",
    "RT-25020118ANYVUG",
    "RT-250201182AVQKY",
    "RT-975392771637604",
    "RT-2502024YXRF3WY",
    "RT-968675419202605",
    "RT-2502025MMEV20W",
    "RT-2502023SAH4G39",
    "RT-968640487745319",
    "RT-976067518072635",
    "RT-25020370MRTE40",
    "RT-250204A2H5Q0YQ",
    "RT-250204A7M76MP1",
    "RT-250204AYCWRE96",
    "RT-250204A15ASKJC",
    "RT-969724894590466",
    "RT-969859463149897",
    "RT-250205DG8ENDRY",
    "RT-250205C4AMTBKM",
]


class Command(BaseCommand):
    help = "ยกเลิกเอกสารของ บจก ที่ออกก่อน 17 ม.ค. 2025"

    def handle(self, *args, **kwargs):
        company = Company.objects.get(pk=470)
        self.service = ETaxService.from_company(company)
        self.repository = self.service.repository

        print("prepare to run: ")
        print(to_be_new)
        breakpoint()
        self.new_docs(to_be_new)

    def cancel_docs(self, doc_ids: list[str]):
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            for doc_id in doc_ids:
                if doc_id in done:
                    continue

                tax_doc = TaxDocument.objects.get(doc_id=doc_id, company_id=470)

                # d1a_doc = D1aDocument(**tax_doc.doc_info)
                # d1a_doc.DOC_STATUS = "CANCEL"
                # d1a_doc.EMAIL_FLAG = "N"
                # d1a_doc.BUYER_CONTACT_EMAIL = ""

                # self.repository.import_document(**d1a_doc.model_dump(mode='json'))
                def send_request(tax_doc):
                    tax_doc = self.service.cancel_tax_document(
                        tax_doc,
                        cancel_by="SYSTEM",
                        options={
                            "override": {"EMAIL_FLAG": "N", "BUYER_CONTACT_EMAIL": ""}
                        },
                    )

                    print("Cancelled", tax_doc.doc_id, " - ", tax_doc.status)

                executor.submit(send_request, tax_doc)

    def new_docs(self, doc_ids: list[str]):
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            for doc_id in doc_ids:
                if doc_id in done:
                    continue

                def send_request(tax_doc):
                    tax_doc.doc_url = None
                    buyer = load_buyer(tax_doc.buyer)
                    options = {
                        "override": {
                            "DOC_ID": tax_doc.doc_id + "-E1",
                            "DOC_ISSUE_DATE": "2025-02-06T00:00:00+07:00",
                        }
                    }
                    tax_doc = self.service.retry_tax_document(
                        tax_doc, buyer, create_by="SYSTEM", options=options
                    )
                    print("RETRY ", tax_doc.doc_id, " - ", tax_doc.status)

                tax_doc = TaxDocument.objects.get(doc_id=doc_id, company_id=470)
                executor.submit(send_request, tax_doc)

    def get_docs(self):
        rows = read_excel("yvis_บจก_เอกสารทั้งหมด_20250211_1106.xlsx")
        new_com_start_date = parse_date("2025-02-06")
        old_com_cutoff_date = parse_date("2025-01-17")

        to_be_cancelled = []
        to_be_new = []

        for row in rows:
            doc_issue_date = parse_date(row["วันที่เอกสาร"])
            doc_id = row["เลขที่เอกสาร"]

            if doc_issue_date < new_com_start_date:
                to_be_cancelled.append(doc_id)

            if old_com_cutoff_date < doc_issue_date < new_com_start_date:
                to_be_new.append(doc_id)

        return to_be_cancelled, to_be_new
