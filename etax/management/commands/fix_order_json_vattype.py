
import json
from django.core.management.base import BaseCommand
from core.serializers.serializers import end_of, start_of
from etax.error_prevention.order_json_error_prevention import OrderJsonCheckSerializer
from etax.models import TaxDocument
from picking.models import PickOrder
from datetime import date, timedelta

class Command(BaseCommand):
    help = "Fix vattype=3 (inc) and vattype=2 (exc) in order_json"

    def handle(self, *args, **kwargs):
        # pick_order_ids = []
        # start_date = date(2025, 1, 15)
        # for i in range(20):
        #     start = start_of(start_date)
        #     end = end_of(start_date)
        #     print(f"Checking from {start} to {end}")
        #     eso_pickorders = PickOrder.objects.filter(create_date__gte=start, create_date__lte=end, order_oms='easy-order').values_list('id', flat=True)
        #     print(f"Found {len(eso_pickorders)} easy-order pickorders")
        #     print(list(eso_pickorders))
        #     pick_order_ids.extend(eso_pickorders)

        #     start_date += timedelta(days=1)

        # with open('pick-order-ids.json', 'w') as f:
        #     json.dump(list(pick_order_ids), f, indent=4, ensure_ascii=False)
    
        with open('pick-order-ids.json', 'r') as f:
            pick_order_ids = json.load(f)
        
        pick_orders = PickOrder.objects.filter(id__in=pick_order_ids)

        for po in pick_orders:
            order_json = po.order_json

            order_items = order_json["list"]
            shippingamount = order_json["shippingamount"]
            discountamount = order_json["discountamount"]
            amount = order_json["amount"]

            sum_totalprice = sum([order_item["totalprice"] for order_item in order_items])
            _amount = sum_totalprice + shippingamount - discountamount
            if (round(_amount, 2) != round(amount, 2)):
                print(f"PickOrder {po.id} has total_price + shippingamount - discountamount != amount")
                print(f"Total price: {sum_totalprice}, Shipping amount: {shippingamount}, Discount amount: {discountamount}, Amount: {amount}")
                print("-" * 80)

                if _amount * 1.07 == amount:
                    po.order_json['vattype'] = 2
                    po.save(update_fields=['order_json'])