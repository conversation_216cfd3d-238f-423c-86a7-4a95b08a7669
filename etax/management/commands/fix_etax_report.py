from django.conf import settings
from django.utils import timezone
import pydash as py_
from datetime import date, datetime
import json
from django.core.management.base import BaseCommand
from companies.models.models import Company
from core.serializers.serializers import start_of
from etax.management.commands._common import write_excel
from etax.models import TaxDocument
from etax.views.etax_debugger_views import load_buyer
from picking.models import PickOrder, Product
from services.etax_invoice.d1a_schema import D1aDocument
from services.etax_invoice.etax_service import ETaxService
from django.db.models import Q


def prepare_new_d1a_doc(tax_doc: TaxDocument, products_map: dict):
    etax_service = ETaxService.from_company(tax_doc.company)
    pick_order: PickOrder = tax_doc.pick_order
    pick_order.order_json = etax_service._prepare_order_json(pick_order, products_map)
    buyer = load_buyer(tax_doc.buyer)
    split_method = etax_service._check_required_split_tiv_rct(pick_order, products_map)

    new_d1a_doc = None
    if split_method == "tiv":
        new_d1a_doc = D1aDocument.prepare_d1a_tiv(pick_order, buyer)
    elif split_method == "rct":
        new_d1a_doc = D1aDocument.prepare_doa_rct(pick_order, buyer)
    elif split_method == "tiv_rct":
        result = etax_service._split_pickorder_tiv_rct(pick_order)
        split_remark = result["split_remark"]

        if tax_doc.doc_type == TaxDocument.DOCTYPE_RECEIPT:
            pick_order_vat0 = result["pick_order_vat0"]
            new_d1a_doc = D1aDocument.prepare_doa_rct(
                pick_order_vat0, buyer, split_remark=split_remark
            )
        elif tax_doc.doc_type == TaxDocument.DOCTYPE_TAX_INVOICE:
            pick_order_vat7 = result["pick_order_vat7"]
            new_d1a_doc = D1aDocument.prepare_d1a_tiv(
                pick_order_vat7, buyer, split_remark=split_remark
            )
        else:
            return None

    return new_d1a_doc


def get_last_log_msg(tax_doc: TaxDocument):
    if len(tax_doc.log) == 0:
        return "NO LOG"

    last_log = tax_doc.log[-1]
    status_code = py_.get(last_log, "extra.response.data.code")
    message = py_.get(last_log, "extra.response.data.message")
    if status_code == "200":
        return "200, OK"
    if status_code == "400":
        return f"400, {message}"
    if status_code == "500":
        return f"500, ERROR - {message}"
    return f"{status_code}, UNKNOWN - {message}"


def get_etax_debugger_link(tax_doc: TaxDocument):
    # base_url = settings.UI_HOST
    return f"https://cloud.dobybot.com/etax/debugger/?cid={tax_doc.company.uuid}&oid={tax_doc.pick_order.uuid}"


class Command(BaseCommand):
    help = "List all eTax records"

    def add_arguments(self, parser):
        parser.add_argument("--company", default="all")
        pass

    def handle(self, *args, **kwargs):
        start = start_of(date(2025, 2, 1))
        end = timezone.make_aware(datetime(2025, 2, 15, 12, 0, 0))
        row_count = 0
        rows = []
        summary = {
            "OK": 0,
            "RETRY": 0,
            "CANCEL_RENEW": 0,
        }

        if kwargs["company"] != "all":
            companies = Company.objects.filter(id=kwargs["company"])
        else:
            companies = Company.objects.filter(
                settings_json__ETAX_SELLER__isnull=False
            ).all()

        for company in companies:
            print("start query", company.id, company.name)
            tax_docs = TaxDocument.objects.filter(
                Q(company=company),
                Q(create_date__gte=start),
                Q(create_date__lte=end),
                Q(status=TaxDocument.STATUS_FAILED),
            ).prefetch_related("pick_order__company", "company")

            if len(tax_docs) == 0:
                continue

            products = Product.objects.filter(company_id=company.id)
            products_map = {p.sku: p for p in products}

            for tax_doc in tax_docs:
                new_d1a_doc = prepare_new_d1a_doc(tax_doc, products_map)
                if not new_d1a_doc:
                    continue
                old_d1a_doc = D1aDocument(**tax_doc.doc_info)

                new_d1a_doc.MONEY_GRAND_TOTALAMT = round(
                    float(new_d1a_doc.MONEY_GRAND_TOTALAMT), 2
                )
                new_d1a_doc.MONEY_TAX_TOTALAMT = round(
                    float(new_d1a_doc.MONEY_TAX_TOTALAMT), 2
                )
                old_d1a_doc.MONEY_GRAND_TOTALAMT = round(
                    float(old_d1a_doc.MONEY_GRAND_TOTALAMT), 2
                )
                old_d1a_doc.MONEY_TAX_TOTALAMT = round(
                    float(old_d1a_doc.MONEY_TAX_TOTALAMT), 2
                )

                ok, errors = ETaxService.check_order_d1a_json_data(
                    tax_doc.pick_order.order_json, tax_doc.doc_info
                )
                if ok:
                    defect_type = "OK"
                    fix_status = "DONE"
                else:
                    if (
                        new_d1a_doc.MONEY_GRAND_TOTALAMT
                        != old_d1a_doc.MONEY_GRAND_TOTALAMT
                        or new_d1a_doc.MONEY_TAX_TOTALAMT
                        != old_d1a_doc.MONEY_TAX_TOTALAMT
                    ):
                        defect_type = "CANCEL_RENEW"
                        fix_status = "PENDING"
                    else:
                        defect_type = "RETRY"
                        fix_status = "PENDING"

                if defect_type == "OK" and tax_doc.doc_version == 1:
                    defect_type = "RETRY"
                    fix_status = "PENDING"

                rows.append(
                    {
                        "doc_pk": tax_doc.pk,
                        "doc_id": tax_doc.doc_id,
                        "doc_version": tax_doc.doc_version,
                        "doc_status": tax_doc.status,
                        "doc_type": tax_doc.doc_type,
                        "doc_seller": old_d1a_doc.SELLER_NAME,
                        "doc_issue_date": old_d1a_doc.DOC_ISSUE_DATE.isoformat(),
                        "order_number": tax_doc.pick_order.order_number,
                        "order_date": tax_doc.pick_order.order_date,
                        "company_id": tax_doc.company.id,
                        "company_name": tax_doc.company.name,
                        "old_d1_doc_type": old_d1a_doc.DOC_TYPE,
                        "new_d1_doc_type": new_d1a_doc.DOC_TYPE,
                        "old_d1_grand_total": old_d1a_doc.MONEY_GRAND_TOTALAMT,
                        "new_d1_grand_total": new_d1a_doc.MONEY_GRAND_TOTALAMT,
                        "old_d1_vat": old_d1a_doc.MONEY_TAX_TOTALAMT,
                        "new_d1_vat": new_d1a_doc.MONEY_TAX_TOTALAMT,
                        "checker_error": (
                            "OK"
                            if ok
                            else json.dumps(errors, ensure_ascii=False, indent=4)
                        ),
                        "last_log_msg": get_last_log_msg(tax_doc),
                        "defect_type": defect_type,
                        "fix_status": fix_status,
                        "link": get_etax_debugger_link(tax_doc),
                    }
                )
                summary[defect_type] += 1
                row_count += 1
                print(row_count)

            print(summary)
            write_excel(rows, f"fix_etax_report_{kwargs['company']}.xlsx")

    # class Command(BaseCommand):
    #     help = "List defect eTax records"

    #     def add_arguments(self, parser):
    #         parser.add_argument("--company", type=int)

    #     def handle(self, *args, **kwargs):
    #         company_id = kwargs.get("company")

    #         filters = {}
    #         if company_id:
    #             filters["company_id"] = company_id

    #         tax_documents = (
    #             TaxDocument.objects
    #             .filter(
    #                 status__in=[TaxDocument.STATUS_SUCCESS],
    #                 create_date__gte="2025-01-01",
    #                 **filters
    #             )
    #             .prefetch_related("pick_order", "pick_order__company")
    #         )
    #         count_valid = 0
    #         count_defect = 0
    #         count_cancel = 0

    #         results = []
    #         defect_grand_total = []
    #         defect_total_discount = []
    #         defect_other = []

    #         for tax_document in tax_documents:
    #             pick_order = tax_document.pick_order
    #             order_json = pick_order.order_json

    #             if order_json["status"] in ["Voided", "Partial Voided"]:
    #                 count_cancel += 1
    #                 continue

    #             # if tax_document.doc_type == TaxDocument.DOCTYPE_TAX_INVOICE:
    #             #     checker = D1ATIVCheckSerializer(data=tax_document.doc_info)
    #             # elif tax_document.doc_type == TaxDocument.DOCTYPE_RECEIPT:
    #             #     checker = D1ARCTCheckSerializer(data=tax_document.doc_info)
    #             ok, errors = ETaxService.check_order_d1a_json_data(order_json, tax_document.doc_info)

    #             doc_info = D1aDocument(**tax_document.doc_info)

    #             # Compute D1 money amount
    #             d1_sum_product_netline = sum(
    #                 float(x.PRODUCT_SUM_NETLINE_TOTALAMT or "0") for x in doc_info.items
    #             )
    #             d1_sum_product_discount = sum(
    #                 float(x.PRODUCT_ACTUALAMT or "0") for x in doc_info.items
    #             )
    #             d1_bill_discount = (
    #                 float(doc_info.MONEY_ALLOWANCE_TOTALAMT) - d1_sum_product_discount
    #             )
    #             d1_grand_total = float(doc_info.MONEY_GRAND_TOTALAMT)
    #             oj_grand_total = order_json.get('amount')

    #             # Compute OrderJson money amount
    #             oj_shipping_amount = order_json.get("shippingamount", 0)
    #             oj_sum_product_netline = sum(
    #                 float(x.get("totalprice", 0)) for x in order_json.get("list", [])
    #             ) + oj_shipping_amount
    #             oj_bill_discount = order_json.get("discountamount", 0)

    #             # Check defect
    #             if not ok:
    #                 err_type = "OK"
    #                 if round(oj_grand_total, 2) != round(oj_sum_product_netline - oj_bill_discount, 2):
    #                     err_type = "ORDER_JSON_GRAND_TOTAL"
    #                     target = defect_grand_total
    #                 elif round(d1_grand_total, 2) != round(oj_grand_total, 2):
    #                     err_type = "GRAND_TOTAL"
    #                     target = defect_grand_total
    #                 elif round(d1_grand_total, 2) != round(d1_sum_product_netline - d1_bill_discount, 2):
    #                     err_type = "TOTAL_DISCOUNT"
    #                     target = defect_total_discount
    #                 else:
    #                     err_type = "OTHER"
    #                     target = defect_other

    #                 count_defect += 1
    #                 print(f"*** Defect eTax record: {tax_document.id}, {pick_order.order_number}, {tax_document.doc_type}, {err_type}")
    #                 target.append(
    #                     {
    #                         "order_uuid": pick_order.uuid,
    #                         "order_number": pick_order.order_number,
    #                         "company": pick_order.company.name,
    #                         "doc_id": tax_document.id,
    #                         "doc_type": tax_document.doc_type,
    #                         "errors": errors,
    #                         "url": tax_document.doc_url,
    #                         "err_type": err_type,
    #                         "grand_total": d1_grand_total,
    #                         "sum_product_netline": d1_sum_product_netline,
    #                         "bill_discount": d1_bill_discount,
    #                     }
    #                 )
    #             else:
    #                 count_valid += 1

    #             print(
    #                 f"Valid: {count_valid}, Defect: {count_defect}, Cancel: {count_cancel}, Total: {len(tax_documents)}"
    #             )

    #         print("--- Summary ---")
    #         print(f"Defect grand total: {len(defect_grand_total)}"
    #         )
    #         print(
    #             f"Defect total discount: {len(defect_total_discount)}"
    #         )
    #         print(
    #             f"Defect other: {len(defect_other)}"
    #         )

    #         with open("defect_etax_grand_total.json", "w") as f:
    #             json.dump(defect_grand_total, f, indent=4, ensure_ascii=False)
    #         with open("defect_etax_total_discount.json", "w") as f:
    #             json.dump(defect_total_discount, f, indent=4, ensure_ascii=False)
    #         with open("defect_etax_other.json", "w") as f:
    #             json.dump(defect_other, f, indent=4, ensure_ascii=False)

    #         #     checker = OrderJsonCheckSerializer(data=order_json)
    #         #     if not checker.is_valid():
    #         #         count_defect += 1
    #         #         print(
    #         #             f"Defect eTax record: {tax_document.id}, {pick_order.order_number}"
    #         #         )
    #         #         print(checker.errors)
    #         #         print("-" * 80)
    #         #         results.append(
    #         #             {
    #         #                 "order_number": pick_order.order_number,
    #         #                 "company": pick_order.company.name,
    #         #                 "doc_id": tax_document.id,
    #         #                 "doc_type": tax_document.doc_type,
    #         #                 "errors": checker.errors,
    #         #             }
    #         #         )
    #         #     else:
    #         #         count_valid += 1
    #         #         print(f"Valid eTax record: {tax_document.id}")

    #         #     print(
    #         #         f"Valid: {count_valid}, Defect: {count_defect}, Total: {len(tax_documents)}"
    #         #     )
