import logging
import traceback

from django.conf import settings
from django.core.management.base import BaseCommand
from companies.models.models import Company
from core.serializers.serializers import start_of
from etax.management.commands._common import (
    gather_with_concurrency,
    read_excel,
    write_excel,
)
from etax.models import TaxDocument, TaxDocumentTransaction
from etax.views.etax_debugger_views import EtaxRetryAPIView
from picking.models import PickOrder
from services.etax_invoice.d1a_schema import D1aDocument
from services.etax_invoice.etax_service import ETaxService
from datetime import date
import threading
import asyncio
import httpx


from django.db.models import Q
from django.utils import timezone
from datetime import datetime

from services.google.drive.drive import GoogleDriveService

to_be_rename = [
    "250115JM3S6HXM",
    # "250115J78JFBVU",
    # "250115J7XN3S3V",
    # "COX29",
    # "cj5",
    # "250116KAT0ARD7",
    # "COZ13",
    # "COZ32 Pop up",
    # "250117QT5169KB",
    # "250117PXBEXR6E",
    # "250117PHF0YVPM",
    # "COZ12",
    # "CPA64",
    # "COZ75",
    # "CPA78",
    # "CPB5",
    # "CPA27",
    # "CPA79",
    # "CPB2",
    # "CPB6",
    # "CPA73",
    # "250117QBGVS7YQ",
    # "250118R56W1JR5",
    # "579883966276143474",
    # "250117Q4H7GD2D",
    # "250117PTNH6KQK",
    # "CPC38",
    # "250118R77W1F16",
    # "250118SSE75UE0",
    # "250118RDK19D74",
    # "CPB79",
    # "250118RCMJN534",
    # "CPC17",
    # "CPC19",
    # "CPC30",
    # "CPB92",
    # "CPB98",
    # "CPC1",
    # "250118T5C4E50X",
    # "CPC77",
    # "CPD24",
    # "CPD39",
    # "250118TB3BNM8S",
    # "CPE72",
    # "CPF42",
    # "250119VM3N0GAX",
    # "CPE70",
    # "CPE80",
    # "CPF1",
    # "CPE42",
    # "CPF34",
    # "250119VPG28C3Y",
    # "CPE64",
    # "970560138552299",
    # "CPF32",
    # "250119VXN3HC2M",
    # "250119TQQ25QGN",
    # "250119VGA2YBS3",
    # "963850454423855",
    # "CPH5",
    # "2501201PK7AKN6",
    # "2501212K0SER2U",
    # "25012005UBYNG5",
    # "25012004V3KDQT",
    # "2501202BEXBSP5",
    # "25012020DT8Q1E",
    # "25012006K15M5E",
    # "2501201J2EA381",
    # "250120242JTDY3",
    # "CPH42",
    # "CPH41",
    # "2501202HJQ11M9",
    # "2501200CJ61TDF",
    # "CPI62",
    # "2501212NC2E4RP",
    # "CPJ22",
    # "2501213Q3U0G7J",
    # "2501213UAU4233",
    # "CPJ19",
    # "CPI79",
    # "970963906254626",
    # "CPJ15",
    # "2501214SH26F44",
    # "CPI98",
    # "CPJ14",
    # "CPI88",
    # "COI90",
    # "25012142CDGS5Q",
    # "2501226563KCBG",
    # "2501226ATRTDFC",
    # "25012264QJ74KM",
    # "2501226HXC0QCF",
    # "2501226AAXCVW3",
    # "2501226GHW26H9",
    # "2501226VEWSGRF",
    # "CPP53",
    # "25012267MPCU3A",
    # "25012267HR2GCA",
    # "2501226P34JPV1",
    # "579903890957306587",
    # "2501226HY7MFD2",
    # "25012255T64PE8",
    # "250122666G8P16",
    # "2501226A1E4R3R",
    # "25012264H63DY8",
    # "25012264KCQWMP",
    # "971408723476939",
    # "25012264GJ3G7X",
    # "25012264PEWHKS",
    # "971409124664353",
    # "250122662UUP08",
    # "250122648DQYNB",
    # "25012266CNKXXF",
    # "971407152946712",
    # "2501226504VDUC",
    # "2501226982K2TB",
    # "964544443128528",
    # "25012266QQFT4Y",
    # "25012268AAT3WY",
    # "2501226CQ308AH",
    # "579902838453799618",
    # "2501226FYNETK1",
    # "2501226FERP5BK",
    # "CPO50",
    # "2501226FASUVDS",
    # "25012269XW37T4",
    # "25012268DY561T",
    # "2501226HXFTC43",
    # "971420924918690",
    # "2501226JNF4CQA",
    # "25012268RGR44A",
    # "964644601082611",
    # "2501226PYJEPHN",
    # "2501226KU0NWJB",
    # "964541829572670",
    # "CPP64",
    # "964564000196920",
    # "25012279DJ9J9D",
    # "CPQ29",
    # "2501226H6D8J0F",
    # "2501226V6W83Q9",
    # "2501227JT367XV",
    # "CPQ2",
    # "2501227DBGHDRX",
    # "CPP66",
    # "CPO99",
    # "CPP74",
    # "2501226RY2PSDE",
    # "25012266KKV8P7",
    # "CPQ37",
    # "250122652G861G",
    # "CPQ41",
    # "250122646YXE8E",
    # "964544433315722",
    # "25012264PA6HDH",
    # "25012264HKE3N5",
    # "2501227H947BEQ",
    # "250122651RCGW5",
    # "2501227CER91W7",
    # "971407382066347",
    # "25012279J38CP3",
    # "250122643JAYKP",
    # "25012264KT2UHE",
    # "25012265FHV582",
]


class Command(BaseCommand):
    help = "Fix incorrect etax"

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        company = Company.objects.get(pk=470)
        service = ETaxService.from_company(company)
        company_folder_id = company.get_setting("ETAX_DOCUMENT_FOLDER_ID")

        tax_docs = TaxDocument.objects.filter(
            Q(company_id=470), Q(doc_id__in=to_be_rename)
        )

        results = []
        for tdoc in tax_docs:
            d1a_doc = D1aDocument(**tdoc.doc_info)
            d1a_doc.DOC_STATUS = "UPDATE"

            # Seller
            d1a_doc.SELLER_NAME = "ห้างหุ้นส่วนจำกัด ยวิษ สโตร์"
            d1a_doc.SELLER_TAX_ID = "0103563007739"
            d1a_doc.SELLER_TYPE_TAX = "TXID"
            d1a_doc.SELLER_BRANCH = "00000"
            d1a_doc.SELLER_BRANCH_NAME = "สำนักงานใหญ่"
            d1a_doc.SELLER_ADDRESS = (
                "55 ซอยประดิพัทธ์ 17 ถนนประดิพัทธ์ แขวงพญาไท เขตพญาไท กรุงเทพมหานคร 10400"
            )
            d1a_doc.SELLER_CONTACT_PHONE = "0655151522"
            d1a_doc.SELLER_POSTCODE = "10400"
            d1a_doc.SELLER_COUNTRY_ID = "TH"

            # PAYEE
            d1a_doc.PAYEE_NAME = d1a_doc.SELLER_NAME
            d1a_doc.PAYEE_TAX_ID = d1a_doc.SELLER_TAX_ID
            d1a_doc.PAYEE_TYPE_TAX = d1a_doc.SELLER_TYPE_TAX
            d1a_doc.PAYEE_ADDRESS = d1a_doc.SELLER_ADDRESS
            d1a_doc.PAYEE_POSTCODE = d1a_doc.SELLER_POSTCODE
            d1a_doc.PAYEE_COUNTRY_ID = "TH"

            # Block Email
            d1a_doc.EMAIL_FLAG = "N"
            d1a_doc.BUYER_CONTACT_EMAIL = ""

            tdoc.doc_info = d1a_doc.model_dump(mode="json")

            ok, file_pdf, doc_xml, response = service.repository.import_document(
                d1a_doc
            )
            tdoc.add_request_log(response, create_by="SYSTEM")
            tdoc.add_transaction(TaxDocumentTransaction.ACTION_RETRY)

            if not ok:
                tdoc.status = TaxDocument.STATUS_FAILED
                tdoc.save()
                print(f"RENAME Failed: {tdoc.doc_id}")
                results.append(
                    {
                        "doc_id": tdoc.doc_id,
                        "status": "FAILED",
                    }
                )
                continue

            doc_id = tdoc.doc_info["DOC_ID"]
            gdrive_service = GoogleDriveService()
            if tdoc and tdoc.doc_url:
                gdrive_service.rename_file(
                    fileId=tdoc.doc_url.split("/d/")[1].split("/")[0],
                    new_name=f"{doc_id} (cancel).pdf",
                )

            folder_id, _ = gdrive_service.get_or_create_folder(
                name=datetime.now().strftime("%Y-%m"),
                parent_id=company_folder_id,
            )
            file_id = gdrive_service.upload_file(
                name=f"{doc_id}.pdf",
                mime_type="application/pdf",
                file=file_pdf,
                parent_id=folder_id,
            )

            # Update tax_invoice data
            tdoc.doc_url = f"https://drive.google.com/file/d/{file_id}/view"
            tdoc.status = TaxDocument.STATUS_SUCCESS
            tdoc.doc_xml = doc_xml
            tdoc.edit_count += 1
            tdoc.save()
            print(f"RENAME Success: {tdoc.doc_id}")
            results.append(
                {
                    "doc_id": tdoc.doc_id,
                    "status": "OK",
                }
            )

        write_excel(results, "yvis_rename_result.xlsx")
