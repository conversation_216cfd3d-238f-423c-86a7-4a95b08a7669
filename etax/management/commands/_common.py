import pandas as pd
import asyncio
from openpyxl import Workbook
from io import BytesIO
import httpx

REPORT_FOLDER = ".temp/fix_etax_report"

SERVER = "PROD"

if SERVER == "LOCALHOST":
    BASE_URL = "http://localhost:8000"
    AUTHTOKEN = "412e2522aeb6c8bda225492e0479e0d61f629ff3"
if SERVER == "UAT":
    BASE_URL = "https://api-uat.dobybot.com"
    AUTHTOKEN = "c392ca23d9414291e33c4816cd297ca888023c56"
elif SERVER == "PROD":
    BASE_URL = "https://dobybot-task-service-yuo4mnnlaa-as.a.run.app"
    AUTHTOKEN = "412e2522aeb6c8bda225492e0479e0d61f629ff3"


def write_excel(rows, filename):
    buffer = BytesIO()
    writer = pd.ExcelWriter(buffer, engine="openpyxl")
    df = pd.DataFrame(rows)
    df.to_excel(writer, sheet_name="Sheet1", index=False)

    workbook: Workbook = writer.book
    ws = workbook.active
    ws.auto_filter.ref = ws.dimensions
    workbook.save(buffer)

    buffer.seek(0)
    outfile = f"{REPORT_FOLDER}/{filename}"

    with open(outfile, "wb") as f:
        f.write(buffer.read())

    return outfile


def read_excel(filename):
    filename = f"{REPORT_FOLDER}/{filename}"
    df = pd.read_excel(filename)
    return df.to_dict(orient="records")


async def gather_with_concurrency(n, *coros):
    semaphore = asyncio.Semaphore(n)

    async def sem_coro(coro):
        async with semaphore:
            return await coro

    return await asyncio.gather(*(sem_coro(c) for c in coros))


async def send_request(row, path, json):
    """
    row = {
        "doc_pk": 1,
        "doc_id": "1234",
        "company_id": 1,
        "company_name": "ABC",
        "fix_status": "PENDING"
    }
    """
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{BASE_URL}{path}",
                headers={"Authorization": f"Token {AUTHTOKEN}"},
                json=json,
                timeout=60,
            )

            if response.status_code == 200:
                doc_status = response.json()["status"]
                if doc_status in ["success", "cancel"]:
                    row["fix_status"] = "DONE"
                else:
                    row["fix_status"] = f"FAILED - doc_status={doc_status}"
            else:
                row["fix_status"] = f"FAILED - {response.status_code} - {response.text}"
        except Exception as e:
            row["fix_status"] = "ERROR - Exception " + repr(e)

    print(
        f"Sent {path} for {json=}, company={row['company_id'], row['company_name']}  ... {row['fix_status']}"
    )


async def send_retry(row: dict, send_email: bool):
    await send_request(
        row,
        "/api/etax/debugger/retry/",
        {"tax_document": row["doc_pk"], "send_email": send_email},
    )


async def send_cancel_and_renew(row: dict, override: dict=None):
    payload = {"tax_document": row["doc_pk"]}
    if override:
        payload["override"] = override

    await send_request(
        row, "/api/etax/debugger/cancel-and-renew/", payload
    )


async def send_cancel(row: dict):
    await send_request(
        row, "/api/etax/debugger/cancel/", {"tax_document": row["doc_pk"]}
    )


async def send_update(row: dict, send_email: bool):
    await send_request(
        row, "/api/etax/debugger/update/", {"tax_document": row["doc_pk"], "send_email": send_email}
    )
