from django.db import models
from datetime import datetime
import requests
import nanoid
from django.utils import timezone

from companies.models.models import Company
from core.logging import masked_dict, parse_body
from simple_history.models import HistoricalRecords

from utils.datetime_fmt import to_iso_format

VAT_TYPE_NON = 1
VAT_TYPE_EXC = 2
VAT_TYPE_INC = 3


class TaxDocument(models.Model):

    uuid = models.CharField(
        unique=True, db_index=True, default=nanoid.generate, max_length=21
    )
    company = models.ForeignKey(
        "companies.Company",  # Replace with your actual Company model
        on_delete=models.CASCADE,
    )
    pick_order = models.ForeignKey(
        "picking.PickOrder",  # Replace with your actual Order model
        on_delete=models.CASCADE,
        null=True,
    )

    edit_count = models.IntegerField(default=0, null=True, blank=True)

    credit_count = models.FloatField(default=0, null=True, blank=True)

    # Basic Fields
    order_number = models.Char<PERSON>ield(max_length=100)
    create_date = models.DateTimeField(auto_now_add=True)

    # Buyer Information
    buyer = models.JSONField()

    # D1A Related Fields
    doc_info = models.JSONField(null=True, blank=True)
    doc_url = models.URLField(max_length=500, null=True, blank=True)
    doc_xml = models.TextField(blank=True, default="")
    doc_id = models.CharField(max_length=100, null=True, blank=True)
    doc_version = models.IntegerField(default=1)

    DOCTYPE_TAX_INVOICE = "tax_invoice"
    DOCTYPE_CREDIT_NOTE = "credit_note"
    DOCTYPE_DEBIT_NOTE = "debit_note"
    DOCTYPE_INVOICE = "invoice"
    DOCTYPE_RECEIPT = "receipt"
    DOCTYPE_RETURN_RECEIPT = "return_receipt"

    DOCTYPE_CHOICES = [
        (DOCTYPE_TAX_INVOICE, "Tax Invoice"),
        (DOCTYPE_CREDIT_NOTE, "Credit Note"),
        (DOCTYPE_DEBIT_NOTE, "Debit Note"),
        (DOCTYPE_RECEIPT, "Receipt"),
        (DOCTYPE_INVOICE, "Invoice"),
        (DOCTYPE_RETURN_RECEIPT, "Return Receipt"),
    ]

    doc_type = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        default=DOCTYPE_TAX_INVOICE,
        choices=DOCTYPE_CHOICES,
    )

    # Status
    STATUS_NEW = "new"
    STATUS_SUCCESS = "success"
    STATUS_FAILED = "failed"
    STATUS_CANCEL = "cancel"
    STATUS_ON_HOLD = "on_hold"
    STATUS_INVALID = "invalid"
    STATUS_CHOICES = [
        (STATUS_NEW, "New"),
        (STATUS_SUCCESS, "Success"),
        (STATUS_FAILED, "Failed"),
        (STATUS_CANCEL, "Cancel"),
        (STATUS_ON_HOLD, "On Hold"),
        (STATUS_INVALID, "Invalid"),
    ]

    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default=STATUS_NEW)
    status_reason = models.JSONField(null=True)

    # Log Information

    # [
    #     {
    #         timestamp: datetiem
    #         severity: info,warning,error
    #         message: str
    #         extra: any
    #     }
    # ]
    log = models.JSONField(
        default=list,
        help_text="Stores log entries with timestamp, severity, message, and extra data",
    )
    is_consent_marketing = models.BooleanField(default=False)
    history = HistoricalRecords(excluded_fields=["log"])

    # class Meta:
    #     unique_together = ("doc_id", "company")

    def add_log_entry(self, severity, message, extra=None, create_by: str = ""):
        """
        Add a new log entry to the log JSON field
        """

        if self.log is None:
            self.log = []

        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "severity": severity,
            "message": message,
            "create_by": create_by,
            "extra": extra,
        }

        self.log.append(log_entry)
        self.save(update_fields=["log"])

    def add_transaction(self, action):

        credit_amount = self.get_credit_amount_by_action(action)

        transaction = TaxDocumentTransaction()
        transaction.tax_document = self
        transaction.action = action
        transaction.company = self.company
        transaction.credit_count = credit_amount
        transaction.save()

    def add_request_log(self, res: requests.Response, create_by: str = ""):

        severity = "error"

        if res.status_code >= 400:
            data = res.text

        if res.status_code == 200:
            data = res.json()

            if data["code"] == "200":
                severity = "info"
                try:
                    details = data["message"]["data"]["data"]
                    if details.get("signed64pdf", False):
                        details["signed64pdf"] = (
                            f"base64: {len(details['signed64pdf'])} bytes"
                        )
                    if details.get("signed64xml", False):
                        details["signed64xml"] = (
                            f"base64: {len(details['signed64xml'])} bytes"
                        )
                except Exception:
                    pass

        req = res.request

        self.add_log_entry(
            severity=severity,
            message=f"[{res.status_code}] {req.method} {req.url}",
            extra={
                "request": {
                    "url": req.url,
                    "method": req.method,
                    "headers": masked_dict(req.headers),
                    "data": parse_body(req.body),
                },
                "response": {"status_code": res.status_code, "data": data},
            },
            create_by=create_by,
        )

    def get_credit_amount_by_action(self, action) -> float:
        company: Company = self.company

        credit = 0
        if action == TaxDocumentTransaction.ACTION_CREATE:
            credit = company.get_setting("ETAX_CREATE_ACTION_CREDIT_AMOUNT")
        elif action == TaxDocumentTransaction.ACTION_UPDATE:
            credit = company.get_setting("ETAX_UPDATE_ACTION_CREDIT_AMOUNT")
        elif action == TaxDocumentTransaction.ACTION_CANCEL:
            credit = company.get_setting("ETAX_CANCEL_ACTION_CREDIT_AMOUNT")
        elif action == TaxDocumentTransaction.ACTION_CREDIT_NOTE:
            credit = company.get_setting("ETAX_CREDIT_NOTE_ACTION_CREDIT_AMOUNT")
        elif action == TaxDocumentTransaction.ACTION_RETRY:
            credit = 0
        elif action == TaxDocumentTransaction.ACTION_RETRY_UPDATE:
            credit = 0
        elif action == TaxDocumentTransaction.ACTION_RETRY_CANCEL:
            credit = 0

        return credit

    def is_document_after_cutoff(self):
        company: Company = self.company
        cutoff_limit = company.get_setting("ETAX_RETRIEVAL_DAY")

        issue_date = to_iso_format(self.doc_info["DOC_ISSUE_DATE"])

        from services.etax_invoice.etax_service import ETaxService

        cut_off_date = ETaxService.get_cut_off_date(issue_date, cutoff_limit)
        today = timezone.now()

        return cut_off_date < today.date()

    @staticmethod
    def get_doc_type_T0X(doc_type):
        if doc_type == "T01":
            return TaxDocument.DOCTYPE_RECEIPT
        elif doc_type == "T02":
            return TaxDocument.DOCTYPE_INVOICE
        elif doc_type == "T03":
            return TaxDocument.DOCTYPE_TAX_INVOICE
        elif doc_type == "388":
            return TaxDocument.DOCTYPE_TAX_INVOICE
        elif doc_type == "380":
            return TaxDocument.DOCTYPE_INVOICE
        else:
            return None


class TaxDocumentTransaction(models.Model):
    tax_document = models.ForeignKey(TaxDocument, on_delete=models.PROTECT)
    company = models.ForeignKey("companies.Company", on_delete=models.CASCADE)

    ACTION_CREATE = "create"
    ACTION_UPDATE = "update"
    ACTION_CANCEL = "cancel"
    ACTION_CREDIT_NOTE = "credit_note"
    ACTION_RETRY = "retry"
    ACTION_RETRY_UPDATE = "retry_update"
    ACTION_RETRY_CANCEL = "retry_cancel"
    ACTION_CHOICES = [
        (ACTION_CREATE, "Create"),
        (ACTION_UPDATE, "Update"),
        (ACTION_CANCEL, "Cancel"),
        (ACTION_CREDIT_NOTE, "Credit Note"),
        (ACTION_RETRY, "*Retry"),
        (ACTION_RETRY_UPDATE, "*Update"),
        (ACTION_RETRY_CANCEL, "*Cancel"),
    ]

    action = models.CharField(
        max_length=20, choices=ACTION_CHOICES, default=ACTION_CREATE
    )
    credit_count = models.FloatField(default=0, null=True, blank=True)
    create_date = models.DateTimeField(auto_now_add=True)
