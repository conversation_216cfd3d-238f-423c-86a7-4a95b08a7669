# Generated by Django 3.2.19 on 2024-12-02 03:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0069_alter_settingvalue_key'),
        ('etax', '0009_taxdocument_is_consent_marketing'),
    ]

    operations = [
        migrations.CreateModel(
            name='TaxDocumentTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'Create'), ('edit', 'Edit'), ('cancel', 'Cancel'), ('credit_note', 'Credit Note')], default='create', max_length=20)),
                ('credit_count', models.FloatField(blank=True, default=0, null=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.company')),
                ('tax_document', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='etax.taxdocument')),
            ],
        ),
    ]
