# Generated by Django 3.2.19 on 2025-03-24 07:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('picking', '0069_productserialno'),
        ('etax', '0019_alter_taxdocumenttransaction_action'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicaltaxdocument',
            name='doc_type',
            field=models.CharField(blank=True, choices=[('tax_invoice', 'Tax Invoice'), ('credit_note', 'Credit Note'), ('debit_note', 'Debit Note'), ('receipt', 'Receipt'), ('invoice', 'Invoice'), ('return_receipt', 'Return Receipt')], default='tax_invoice', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='taxdocument',
            name='doc_type',
            field=models.CharField(blank=True, choices=[('tax_invoice', 'Tax Invoice'), ('credit_note', 'Credit Note'), ('debit_note', 'Debit Note'), ('receipt', 'Receipt'), ('invoice', 'Invoice'), ('return_receipt', 'Return Receipt')], default='tax_invoice', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='taxdocument',
            name='pick_order',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='picking.pickorder'),
        ),
    ]
