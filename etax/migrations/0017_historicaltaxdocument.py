# Generated by Django 3.2.19 on 2025-02-07 06:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import nanoid.generate
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        ('picking', '0068_merge_20250128_0223'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('companies', '0076_alter_settingvalue_key'),
        ('etax', '0016_auto_20250203_1548'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalTaxDocument',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('uuid', models.CharField(db_index=True, default=nanoid.generate, max_length=21)),
                ('edit_count', models.IntegerField(blank=True, default=0, null=True)),
                ('credit_count', models.FloatField(blank=True, default=0, null=True)),
                ('order_number', models.CharField(max_length=100)),
                ('create_date', models.DateTimeField(blank=True, editable=False)),
                ('buyer', models.JSONField()),
                ('doc_info', models.JSONField(blank=True, null=True)),
                ('doc_url', models.URLField(blank=True, max_length=500, null=True)),
                ('doc_xml', models.TextField(blank=True, default='')),
                ('doc_id', models.CharField(blank=True, max_length=100, null=True)),
                ('doc_version', models.IntegerField(default=1)),
                ('doc_type', models.CharField(blank=True, choices=[('tax_invoice', 'Tax Invoice'), ('credit_note', 'Credit Note'), ('receipt', 'Receipt')], default='tax_invoice', max_length=100, null=True)),
                ('status', models.CharField(choices=[('new', 'New'), ('success', 'Success'), ('failed', 'Failed'), ('cancel', 'Cancel'), ('on_hold', 'On Hold'), ('invalid', 'Invalid')], default='new', max_length=10)),
                ('status_reason', models.JSONField(null=True)),
                ('log', models.JSONField(default=list, help_text='Stores log entries with timestamp, severity, message, and extra data')),
                ('is_consent_marketing', models.BooleanField(default=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('company', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.company')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('pick_order', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='picking.pickorder')),
            ],
            options={
                'verbose_name': 'historical tax document',
                'verbose_name_plural': 'historical tax documents',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
