# Generated by Django 3.2.19 on 2025-02-22 04:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('etax', '0018_remove_historicaltaxdocument_log'),
    ]

    operations = [
        migrations.AlterField(
            model_name='taxdocumenttransaction',
            name='action',
            field=models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('cancel', 'Cancel'), ('credit_note', 'Credit Note'), ('retry', '*Retry'), ('retry_update', '*Update'), ('retry_cancel', '*Cancel')], default='create', max_length=20),
        ),
    ]
