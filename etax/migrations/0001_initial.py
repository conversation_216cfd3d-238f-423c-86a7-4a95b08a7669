# Generated by Django 3.2.19 on 2024-11-07 02:27

from django.db import migrations, models
import django.db.models.deletion
import nanoid.generate


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("picking", "0055_alter_pickordertrackingno_shipping_provider"),
        ("companies", "0064_alter_settingvalue_key"),
    ]

    operations = [
        migrations.CreateModel(
            name="TaxInvoiceRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "uuid",
                    models.CharField(
                        db_index=True,
                        default=nanoid.generate,
                        max_length=21,
                        unique=True,
                    ),
                ),
                ("order_number", models.Char<PERSON>ield(max_length=100)),
                ("create_date", models.DateTimeField(auto_now_add=True)),
                ("buyer", models.JSONField()),
                ("doc_info", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ("doc_url", models.URL<PERSON>ield(blank=True, max_length=500, null=True)),
                ("doc_id", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("new", "New"),
                            ("success", "Success"),
                            ("failed", "Failed"),
                        ],
                        default="new",
                        max_length=10,
                    ),
                ),
                (
                    "log",
                    models.JSONField(
                        default=list,
                        help_text="Stores log entries with timestamp, severity, message, and extra data",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="companies.company",
                    ),
                ),
                (
                    "pick_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="picking.pickorder",
                    ),
                ),
            ],
        ),
    ]
