# Generated by Django 3.2.19 on 2025-02-03 08:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('etax', '0015_alter_taxdocument_doc_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='taxdocument',
            name='doc_version',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='taxdocument',
            name='doc_xml',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AddField(
            model_name='taxdocument',
            name='status_reason',
            field=models.JSONField(null=True),
        ),
        migrations.AlterField(
            model_name='taxdocument',
            name='status',
            field=models.CharField(choices=[('new', 'New'), ('success', 'Success'), ('failed', 'Failed'), ('cancel', 'Cancel'), ('on_hold', 'On Hold'), ('invalid', 'Invalid')], default='new', max_length=10),
        ),
    ]
