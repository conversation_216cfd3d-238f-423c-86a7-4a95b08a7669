# Generated by Django 3.2.19 on 2024-11-09 20:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('etax', '0002_rename_taxinvoicerequest_taxdocument'),
    ]

    operations = [
        migrations.AddField(
            model_name='taxdocument',
            name='doc_type',
            field=models.CharField(blank=True, choices=[('tax_invoice', 'Tax Invoice'), ('credit_note', 'Credit Note')], default='tax_invoice', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='taxdocument',
            name='status',
            field=models.CharField(choices=[('new', 'New'), ('success', 'Success'), ('failed', 'Failed'), ('cancel', 'Cancel')], default='new', max_length=10),
        ),
    ]
