from django.contrib import admin
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.utils.html import format_html
from django.urls import path
from django.http import HttpResponseRedirect
from django.contrib import messages

from companies.models.models import Company
from etax.models import TaxDocument, TaxDocumentTransaction
from services.etax_invoice.d1a_schema import CompanyBuyer, PersonalBuyer
from services.etax_invoice.etax_service import ETaxService
from django.urls import reverse


@admin.register(TaxDocument)
class TaxDocumentAdmin(admin.ModelAdmin):
    list_display = [
        "doc_id",
        "company",
        "buyer_email",
        "buyer_phone",
        "doc_type",
        "create_date",
        "status_with_color",
        "etax_debugger",
    ]
    list_filter = ["status", "doc_type", "company", "create_date"]
    search_fields = ["order_number", "doc_id", "uuid"]
    readonly_fields = ["uuid", "create_date", "log", "edit_count", "pick_order", "status_reason"]

    def status_with_color(self, obj):
        colors = {
            "new": "#3498db",  # Blue
            "success": "#2ecc71",  # Green
            "failed": "#e74c3c",  # Red
            "on_hold": "#fc9803",  # Red
            "cancel": "#95a5a6",  # Grey
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.status, "black"),
            obj.get_status_display(),
        )

    def buyer_email(self, obj):
        if isinstance(obj.buyer, dict):
            return obj.buyer.get("email", "N/A")
        return "N/A"

    def buyer_phone(self, obj):
        if isinstance(obj.buyer, dict):
            return obj.buyer.get("phone_number", "N/A")
        return "N/A"

    status_with_color.short_description = "Status"

    def retry_button(self, obj: TaxDocument):
        if obj.status in [obj.STATUS_FAILED, obj.STATUS_NEW]:
            return format_html(
                '<a class="button" href="{}" style="background-color: #007bff; color: white; '
                'padding: 5px 10px; text-decoration: none; border-radius: 4px;">Retry Upload</a>',
                reverse("admin:retry-upload", args=[obj.pk]),
            )
        return ""
    
    def etax_debugger(self, obj: TaxDocument):
        return format_html(
            '<a class="button" href="{}" style="background-color: #007bff; color: white; '
            'padding: 5px 10px; text-decoration: none; border-radius: 4px;">ETax Debugger</a>',
            f'{settings.UI_HOST}/etax/debugger?cid={obj.company.uuid}&oid={obj.pick_order.uuid}',
        )

    retry_button.short_description = "Actions"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "retry-upload/<int:pk>/",
                self.admin_site.admin_view(self.retry_upload),
                name="retry-upload",
            ),
        ]
        return custom_urls + urls

    def retry_upload(self, request, pk):
        failed_document = get_object_or_404(TaxDocument, pk=pk)
        try:
            company: Company = failed_document.company
            is_company_data = failed_document.buyer.get("branch_code", None)
            buyer_data = failed_document.buyer
            if is_company_data:
                company_info = {
                    "buyer_name": buyer_data.get("buyer_name"),
                    "tax_id": buyer_data.get("tax_id"),
                    "address": buyer_data.get("address"),
                    "email": buyer_data.get("email"),
                    "phone_number": buyer_data.get("phone_number"),
                    "branch_name": buyer_data.get("branch_name"),
                    "branch_code": buyer_data.get("branch_code"),
                    "is_consent_marketing": buyer_data.get("is_consent_marketing"),
                    "post_code": buyer_data.get("post_code"),
                }
                buyer = CompanyBuyer(**company_info)
            else:
                buyer_info = {
                    "buyer_name": buyer_data.get("buyer_name"),
                    "tax_id": buyer_data.get("tax_id"),
                    "address": buyer_data.get("address"),
                    "email": buyer_data.get("email"),
                    "phone_number": buyer_data.get("phone_number"),
                    "is_consent_marketing": buyer_data.get("is_consent_marketing"),
                    "post_code": buyer_data.get("post_code"),
                }
                buyer = PersonalBuyer(**buyer_info)

            etax_service = ETaxService.from_company(company)
            etax_service.retry_tax_document(
                tax_document=failed_document, buyer=buyer, create_by="RETRY_SYSTEM"
            )
            # etax_service.update_tax_document(failed_document, buyer)
            messages.success(request, "Retry successful!")
        except Exception as e:
            messages.error(request, f"Retry failed: {str(e)}")

        # Redirect back to the TaxDocument change list
        return HttpResponseRedirect(
            f"/admin/etax/taxdocument/{failed_document.id}/change/"
        )


@admin.register(TaxDocumentTransaction)
class TaxDocumentTransactionAdmin(admin.ModelAdmin):
    list_display = (
        "create_date",
        "company",
        "action",
        "credit_count",
        "get_tax_document",
    )
    list_filter = ("action", "create_date", "company")
    search_fields = (
        "tax_document__order_number",
        "company__name",
        "tax_document__doc_id",
    )
    readonly_fields = ("create_date",)
    date_hierarchy = "create_date"

    def get_tax_document(self, obj):
        url = reverse("admin:etax_taxdocument_change", args=[obj.tax_document.id])
        return format_html('<a href="{}">{}</a>', url, obj.tax_document.order_number)

    get_tax_document.short_description = "Tax Document"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("tax_document", "company")
