annotated-types==0.5.0
anyio==4.8.0
appdirs==1.4.4
appnope==0.1.2
argcomplete==1.12.3
argon2-cffi==21.1.0
asgiref==3.5.2
astroid==2.8.2
attrs==21.2.0
autopep8==1.6.0
backcall==0.2.0
bleach==4.1.0
Brotli==1.0.9
cachetools==4.2.4
cairocffi==1.3.0
CairoSVG==2.5.2
certifi==2021.10.8
cffi==1.15.0
chardet==4.0.0
charset-normalizer==2.0.6
coreapi==2.3.3
coreschema==0.0.4
coverage==6.2
cssselect2==0.4.1
dacite==1.8.0
debugpy==1.5.1
decorator==5.1.0
deepdiff==8.2.0
defusedxml==0.7.1
Django==3.2.19
django-axes==6.0.1
django-cleanup==5.2.0
django-cors-headers==3.10.0
django-csp==3.7
django-environ==0.8.1
django-ipware==5.0.0
django-pg-bulk-update==3.6.0
django-query-debugger==0.0.4
django-simple-history==3.3.0
django-storages==1.12.3
django-user-agents==0.4.0
djangorestframework==3.13.1
djangorestframework-simplejwt==4.8.0
drf-spectacular==0.28.0
drf-spectacular-sidecar==2025.2.1
drf-yasg==1.21.4
dynamic-rest==2.1.2
entrypoints==0.3
et-xmlfile==1.1.0
exceptiongroup==1.2.2
fonttools==4.27.1
future==0.18.2
google-api-core==2.7.1
google-api-python-client==2.48.0
google-auth==2.6.2
google-auth-httplib2==0.1.0
google-auth-oauthlib==0.5.1
google-cloud-appengine-logging==1.1.0
google-cloud-audit-log==0.2.0
google-cloud-core==2.1.0
google-cloud-logging==2.7.2
google-cloud-secret-manager==2.7.2
google-cloud-storage==1.42.3
google-cloud-tasks==2.7.1
google-cloud-bigquery==3.11.4
google-crc32c==1.3.0
google-resumable-media==2.1.0
googleapis-common-protos==1.53.0
grpc-google-iam-v1==0.12.3
grpcio==1.41.1
grpcio-status==1.41.1
gunicorn==20.1.0
h11==0.14.0
hashids==1.3.1
html5lib==1.1
httpcore==1.0.7
httplib2==0.20.4
httpx==0.28.1
idna==3.2
inflection==0.5.1
ipykernel==6.6.0
ipython==7.30.0
ipython-genutils==0.2.0
ipywidgets==7.6.5
isort==5.9.3
itypes==1.2.0
jedi==0.18.1
Jinja2==3.0.3
jsonschema==4.2.1
jupyter==1.0.0
jupyter-client==7.1.0
jupyter-console==6.4.0
jupyter-core==4.9.1
jupyterlab-pygments==0.1.2
jupyterlab-widgets==1.0.2
lazy-object-proxy==1.6.0
libcst==0.3.21
lxml==4.9.2
mailjet-rest==1.3.4
MarkupSafe==2.0.1
matplotlib-inline==0.1.3
mccabe==0.6.1
mistune==0.8.4
mypy-extensions==0.4.3
nanoid==2.0.0
nbclient==0.5.9
nbconvert==6.3.0
nbformat==5.1.3
nest-asyncio==1.5.1
notebook==6.4.6
numpy==1.21.4
oauthlib==3.2.0
openpyxl==3.0.9
orderly-set==5.3.0
packaging==21.2
paho-mqtt==1.6.1
pandas==1.3.4
pandocfilters==1.5.0
parso==0.8.3
pdf2image==1.16.0
pexpect==4.8.0
pickleshare==0.7.5
pillow==8.4.0
platformdirs==2.4.0
PrintNodeApi==0.2.0
prometheus-client==0.12.0
prompt-toolkit==3.0.23
proto-plus==1.19.7
protobuf==3.19.1
psycopg2==2.9.10
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycodestyle==2.8.0
pycparser==2.20
pydantic==2.2.1
pydantic_core==2.6.1
pydash==5.1.1
pydyf==0.1.1
Pygments==2.10.0
PyJWT==2.1.0
pylint==2.11.1
pylint-django==2.4.4
pylint-plugin-utils==0.6
pyparsing==2.4.7
PyPDF2==1.26.0
pyphen==0.11.0
pyrsistent==0.18.0
pyserial==3.5
python-barcode==0.13.1
python-dateutil==2.8.2
python-dotenv==0.19.1
python-escpos==2.2.0
python-poppler==0.4.1
pytz==2021.3
pyusb==1.2.1
PyYAML==6.0
pyzmq==22.3.0
qrcode==7.3.1
qtconsole==5.2.1
QtPy==1.11.2
requests==2.32.3
requests-oauthlib==1.3.1
rsa==4.7.2
ruamel.yaml==0.17.21
ruamel.yaml.clib==0.2.7
Send2Trash==1.8.0
sentry-sdk==2.25.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.3.2.post1
sqlparse==0.4.2
tblib==1.7.0
terminado==0.12.1
testpath==0.5.0
tinycss2==1.1.0
tinyhtml5==2.0.0
toml==0.10.2
tornado==6.1
traitlets==5.1.1
typing-inspect==0.7.1
typing_extensions==4.8.0
ua-parser==0.10.0
uritemplate==4.1.1
urllib3==2.3.0
user-agents==2.2.0
wcwidth==0.2.5
weasyprint==53.3
webencodings==0.5.1
widgetsnbextension==3.5.2
wrapt==1.12.1
xlrd==2.0.1
zopfli==0.1.8
