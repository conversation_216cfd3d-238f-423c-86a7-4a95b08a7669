from companies.models import Company
from picking.models import PickOrder
from django.utils import timezone
from datetime import date, timedelta


def mask(value: str, mask_char='x'):
    if not value:
        return value
    return value[:2] + ((len(value) - 2) * mask_char)


class DataMaskingService:

    order_json_masked_fields = [
        'customername',
        'customeraddress',
        'customerphone',
        'customeremail',
        'shippingname',
        'shippingaddress',
        'shippingphone',
        'shippingemail',
    ]

    @classmethod
    def mask_pick_order_data(cls, company: Company, target_date: date = None):
        today = timezone.localdate()
        if not target_date:
            target_date = today - timedelta(days=90)

        pick_orders = PickOrder.objects.filter(company=company, create_date__date=target_date)

        updatelist = []
        for pick_order in pick_orders:
            pick_order.order_customer = mask(pick_order.order_customer)
            pick_order.order_customerphone = mask(pick_order.order_customerphone)

            for field in cls.order_json_masked_fields:
                if pick_order.order_json.get(field):
                    pick_order.order_json[field] = mask(pick_order.order_json[field])
            updatelist.append(pick_order)

        PickOrder.objects.bulk_update(
            updatelist,
            fields=['order_customer', 'order_customerphone', 'order_json'],
            batch_size=200
        )

        return len(updatelist)
