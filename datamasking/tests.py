from datetime import timedelta
from django.test import TestCase
from django.test.utils import tag
from core.tests.testutils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TestClient
from core.tests import setup
from datamasking.services import DataMaskingService
from picking.models import PickOrder
from django.utils import timezone


# python manage.py test --tag=data-masking
class DataMaskingServiceTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        self.user, self.company, self.wallet = setup.init().values()
        self.client = TestClient()
        self.client.login()

    @tag("data-masking")
    def test_masking_pickorder_data(self):
        setup.create_test_pickorders(company=self.company)
        pick_orders = PickOrder.objects.all()
        for p in pick_orders:
            p.create_date = timezone.now() - timedelta(days=90)
            p.save()

        DataMaskingService.mask_pick_order_data(company=self.company)
        pick_orders = PickOrder.objects.all()

        p0 = pick_orders[0]
        self.assertEqual(p0.order_customer, "**xxxxxxxxxxxxxxx")
        self.assertEqual(p0.order_customerphone, "66xxxxxxxxx")
        self.assertEqual(p0.order_json["customername"], "**xxxxxxxxxxxxxxxxxxxxxxxx")
        self.assertEqual(p0.order_json["shippingname"], "**xxxxxxxxxxxxxxx")
        self.assertEqual(p0.order_json["customerphone"], "66xxxxxxxxx")
        self.assertEqual(p0.order_json["shippingemail"], "t.xxxxxxxxxxxxxxxxxxxx")
