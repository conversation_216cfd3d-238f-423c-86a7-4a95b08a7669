from rest_framework.views import APIView
from rest_framework import serializers

from companies.models.models import Company
from rest_framework.permissions import IsAdminUser
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response

from datamasking.services import DataMaskingService


class PickOrderDataMaskingAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        date = serializers.DateField(allow_null=True)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        company = validator.validated_data['company']
        target_date = validator.validated_data['date']

        DataMaskingService.mask_pick_order_data(company, target_date)

        return Response(status=200)
