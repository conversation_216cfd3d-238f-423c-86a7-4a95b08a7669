from typing import List
from uuid import uuid4
from fixcases.models import FixCase
from services import sms
from services.sms.sms import format_phone_number, send_sms_with_ants
from services.sms.jera_sms import send_sms_with_jera
from services.sms import shorten_url
from logger.models import SmsLog
from django.conf import settings
from sentry_sdk import capture_exception, capture_message

from sms.models import SmsBlock, SmsCampaign


class SmsException(Exception):
    pass


class SmsService:
    def __init__(self, company) -> None:
        self.company = company

    def send_sms_request(
        self, sender: str, messages: List[dict], campaign: SmsCampaign = None, **kwargs
    ):
        """
        Send request to sms service provider

        Parameters
        ----------
        messages: List[dict]
            [{ 'to': str, 'text': str, 'remark': str? }]
        shorten_url: bool
            Detect and shorten url in the message (ants service)
        campaign: SmsCampaign
            default to None

        Returns
        -------
        (bool, dict | str)
            (is_success, response_data)

            For example:
            (True, {
                'bulkId': 'ef54db21-3bff-4877-859c-a3266d669af5', // optional, may not existed
                'details': [{
                    'messageId': 'bf1a5fa6-dbbc-4215-bc29-03a17097f0d3',
                    'mobile': '668XXXXXXX',
                    'to': '668XXXXXXX',
                    'from': 'Cusway-SMS',
                    'credit': '1.00',
                    'shorturl': 'N',
                    'status': {'code': '101', 'name': 'PENDING', 'description': 'Pending -Message has been accepted by the system'}
                }]
            })
        """
        raise NotImplementedError()

    def get_notify_url(self):
        raise NotImplementedError("You must override SmsService.get_notify_url")

    def send_sms(self, messages, shorten_url=False, campaign=None, type=None, **kwargs):
        """
        Parameters
        ----------
        messages: List[dict]
            [{ 'to': str, 'text': str, 'remark': str? }]
        shorten_url: bool
            Detect and shorten url in the message (ants service)
        campaign: SmsCampaign
            default to None
        type:
            'fixcase' | 'paid' | 'ready_to_ship'
            default to None
        Raises
        ------
        SmsException

        Return
        ------
        List[Smslog]
        """
        results = []

        try:
            sender = self.company.get_setting("SMS_SENDER")
            if not sender:
                raise SmsException(f"Invalid sender {sender}")

            if settings.OVERRIDE_PHONE_NUMBER:
                messages = [
                    {**m, "to": settings.OVERRIDE_PHONE_NUMBER} for m in messages
                ]

            phone_numbers = []
            for message in messages:
                message["to"] = format_phone_number(message["to"])
                phone_numbers.append(message["to"])

            blocked_numbers = SmsBlock.objects.filter(
                mobile__in=phone_numbers
            ).values_list("mobile", flat=True)
            for message in messages:
                if message["to"] in blocked_numbers:
                    message["to"] = "BLOCKED-" + message["to"]

            is_success, result = self.send_sms_request(
                sender=sender,
                messages=messages,
                shorten_url=shorten_url,
                notifyUrl=self.get_notify_url(),
                notifyContentType="application/json",
                **kwargs,
            )

            if is_success:
                bulk_id = result.get("bulkId", "unspecified")
                for message, detail in zip(messages, result["details"]):
                    smslog = SmsLog.create_from_ants_response(
                        self.company,
                        bulk_id=bulk_id,
                        detail=detail,
                        text=message["text"],
                        remark=message.get("remark", ""),
                        campaign=campaign,
                        commit=False,
                        sms_type=type,
                    )
                    results.append(smslog)
                results = SmsLog.objects.bulk_create(results)
            else:
                if "wrong phone number format:" in result.get("message", ""):
                    bulk_id = str(uuid4())
                    for msg in messages:
                        if "BLOCKED" in msg["to"]:
                            status = {
                                "code": "209",
                                "name": "UNDELIVERED",
                                "description": "PDPA - Receipient requested to block",
                            }
                        else:
                            status = {
                                "code": "402",
                                "name": "REJECTED",
                                "description": "Invalid number - phone number formatted incorrectly",
                            }
                        sms_log = SmsLog.objects.create(
                            company=self.company,
                            sender=sender,
                            bulk_id=bulk_id,
                            message_id=str(uuid4()),
                            to=msg["to"],
                            text=msg["text"],
                            remark=msg.get("remark", ""),
                            credit=0,
                            status=status,
                        )
                        results.append(sms_log)
                else:
                    capture_message(f"Failed to send sms {result}")

        except Exception as e:
            capture_exception(e)

        return results

    def send_sms_ready_to_ship(self, pick_order):
        """
        Send sms with SMS_READY_TO_SHIP_MESSAGE by using data from `pick_order`

        Parameters
        ----------
        pick_order : PickOrder
        """
        message = self.get_sms_ready_to_ship_message(self.company, pick_order)
        if not message:
            return None

        sms_logs = self.send_sms(
            messages=[
                {
                    "to": pick_order.order_customerphone,
                    "text": message,
                    "remark": "AUTO-RTS",
                }
            ],
            shorten_url=True,
            type=SmsLog.READY_TO_SHIP_SMS,
        )

        return sms_logs[0] if sms_logs else None

    def send_sms_multi_package(self, pick_order):
        message = self.company.get_setting("SMS_MULTI_PACKAGE_MESSAGE")

        if not message:
            return None

        message = message.format(
            company=self.company.get_setting("COMPANY_NAME"),
            customer=pick_order.order_customer,
            order_number=pick_order.order_number,
            tracking_no=pick_order.order_trackingno,
            total_packages=pick_order.total_packages,
        )
        sms_logs = self.send_sms(
            messages=[
                {
                    "to": pick_order.order_customerphone,
                    "text": message,
                    "remark": "AUTO-RTS-MULTI-PACKAGE",
                }
            ],
            shorten_url=True,
            type=SmsLog.READY_TO_SHIP_SMS,
        )

        return sms_logs[0] if sms_logs else None

    def send_sms_open_fixcase(self, fixcase: FixCase) -> SmsLog:
        """
        Parameters
        ----------
        fixcase : FixCase

        Returns
        -------
        SmsLog
        """
        message = self.company.get_setting("SMS_FIXCASE_OPEN_MESSAGE")

        if not message:
            return None

        customer_phone = (
            fixcase.customer_phone or fixcase.pick_order.order_customerphone
        )
        customer_name = fixcase.customer_name or fixcase.pick_order.order_customer

        message = message.format(
            company=self.company.get_setting("COMPANY_NAME"),
            customer=customer_name,
            order_number=fixcase.pick_order.order_number,
        )

        sms_logs = self.send_sms(
            messages=[
                {"to": customer_phone, "text": message, "remark": "AUTO-FIXCASE-OPEN"}
            ],
            shorten_url=True,
            type=SmsLog.READY_TO_SHIP_SMS,
        )

        return sms_logs[0] if sms_logs else None

    def send_sms_close_fixcase(self, fixcase: FixCase):
        message = self.company.get_setting("SMS_FIXCASE_CLOSE_MESSGE")

        if not message:
            return None

        customer_phone = (
            fixcase.customer_phone or fixcase.pick_order.order_customerphone
        )
        customer_name = fixcase.customer_name or fixcase.pick_order.order_customer

        message = message.format(
            company=self.company.get_setting("COMPANY_NAME"),
            customer=customer_name,
            order_number=fixcase.pick_order.order_number,
            fixcase_remark=fixcase.tracking_code,
            tracking_code=fixcase.tracking_code,
        )

        sms_logs = self.send_sms(
            messages=[
                {"to": customer_phone, "text": message, "remark": "AUTO-FIXCASE-CLOSE"}
            ],
            shorten_url=True,
            type=SmsLog.FIXCASE_SMS,
        )

        return sms_logs[0] if sms_logs else None

    @staticmethod
    def get_sms_ready_to_ship_message(
        company, pick_order, shorten=False, shorten_https=False, no_ads=False
    ):
        message = company.get_setting("SMS_READY_TO_SHIP_MESSAGE")

        if not message:
            return None

        receipt_link = pick_order.signed_receipt_url
        if no_ads:
            receipt_link = f"{receipt_link}&noads=1"
        if shorten:
            receipt_link = shorten_url(receipt_link) or receipt_link
        if shorten_https:
            receipt_link = f"https://{receipt_link}"

        # add one blank space line after receipt link
        receipt_link = f"{receipt_link}\n"
        message = message.format(
            customer=pick_order.order_customer,
            order_number=pick_order.order_number,
            tracking_no=pick_order.order_trackingno,
            receipt_link=receipt_link,
            shipping_channel=pick_order.order_shippingchannel,
        )

        return message

    @staticmethod
    def deduct_or_hold_credit(company, sms_logs: List[SmsLog]):
        wallet = company.wallet
        if company.get_setting("API_SMS_REFUND_UNDELIVERED"):
            # Will refund if sms is undelivered
            # Hold credit now
            wallet.sms_credit_onhold += sum([x.credit for x in sms_logs])
        else:
            # No refund, deduct credit now
            wallet.sms_balance -= sum([x.credit for x in sms_logs])
        wallet.save()


class AntsSmsService(SmsService):
    def send_sms_request(self, *args, **kwargs):
        return send_sms_with_ants(*args, **kwargs)

    def get_notify_url(self):
        return settings.ANTS_NOTIFY_URL


class JeraSmsService(SmsService):
    def send_sms_request(self, *args, **kwargs):
        return send_sms_with_jera(*args, **kwargs)

    def get_notify_url(self):
        return settings.JERA_SMS_NOTIFY_URL
