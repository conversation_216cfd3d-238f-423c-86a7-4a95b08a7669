from rest_framework.views import APIView
from rest_framework.response import Response
import base64
import os
from core.tests.mocks.response import MockedResponse as MResponse


def file_to_base64(file_path):
    """
    Converts a PDF file to its base64 representation.
    """
    with open(file_path, "rb") as pdf_file:
        file_bytes = pdf_file.read()
        base64_string = base64.b64encode(file_bytes).decode("utf-8")
        return base64_string


class ImportDocumentAPI(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        return Response(
            {
                "code": "200",
                "message": {
                    "data": {
                        "data": {
                            "data": {},
                            "status": "success",
                            "dmsIdPDF": "ZGV0YXh2Mi1kdDIwMjQxMTUtMjAyNS0wMi0xNi1wZGYtVDAzLVJULVJDVDA3MzA2Njg3LTIwMjUwMjE2MDkyODU2LnppcA==",
                            "dmsIdXML": "ZGV0YXh2Mi1kdDIwMjQxMTUtMjAyNS0wMi0xNi14bWwtVDAzLVJULVJDVDA3MzA2Njg3LTIwMjUwMjE2MDkyODU2LnppcA==",
                            "description": "",
                            "signed64pdf": file_to_base64(
                                ".testdata/etax/RT-RCT07306687.pdf"
                            ),
                            "signed64xml": file_to_base64(
                                ".testdata/etax/RT-RCT07306687.xml"
                            ),
                        },
                        "status": 200,
                        "massage": "",
                        "respMsg": "",
                        "respCode": "PASS",
                    },
                    "status": 200,
                },
            }
        )
