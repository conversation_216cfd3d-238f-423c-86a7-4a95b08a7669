from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response


# Create your views here.
class FlashCreateOrderAPI(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        return Response(
            {
                "code": 1,
                "message": "success",
                "tid": "662f5d35da44b14b3b81caa5",
                "data": {
                    "pno": "TH20045JTS6S2B0",
                    "mchId": "CZ5533",
                    "subMchId": "10492839",
                    "outTradeNo": "7608877",
                    "sortCode": "19C-19026-03",
                    "lineCode": "08",
                    "sortingLineCode": "E08",
                    "dstStoreName": "PTY_SP-พัทยา",
                    "earlyFlightEnabled": False,
                    "packEnabled": False,
                    "upcountryCharge": True,
                    "notice": None,
                    "srcPostalCode": "24130",
                    "dstPostalCode": "20150",
                },
            }
        )
