from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response


# Create your views here.
class BTNCPreRecordWebhookMockAPI(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        return Response(
            {
                "order": {
                    "freeze": True,
                    "amount": 1950.2,
                    "paymentamount": 1950.2,
                    "discount": 0,
                    "vatamount": 0,
                    "voucheramount": 0,
                    "shippingamount": 0,
                    "amount_pretax": 0,
                    "number": "845693235246549",
                    "list": [
                        {
                            "sku": "PAHO WH6 X7 Z12",
                            "sku_barcode": "8852519046398",
                            "name": "",
                            "number": 1,
                            "unittext": "",
                            "totalprice": "1950.20000",
                            "pricepernumber": 1950.2,
                        }
                    ],
                    "receipt_invoice": {
                        "ref_number": "8349/22377",
                        "name": "ปรั**** BTNC",
                        "address": "11**/**-75 สุข******* แขวงพ****** เข******** กรุงเทพมหานคร 10250",
                        "footer_html": """
                            <h1>Test Footer</h1><div style='text-align: center'><img style='width: 60px; height: 60px;' src='https://uat.btnc.me/btncservice/btncservice.png'></div>
                        """
                    },
                    "remark": "ทดสอบใบเสร็จรับเงิน",
                },
                "dbb_action": "PARTIAL_UPDATE",
            }
        )
