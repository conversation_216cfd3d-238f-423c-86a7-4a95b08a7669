from unittest import skipIf
from django.test import SimpleTestCase
from django.test import tag

from .tasks import create_vrich_checkout_task, create_wallet_daily_deduct_task


class VrichCheckTaskTestCase(SimpleTestCase):
    @skipIf(True, "This will send request to vrich production server")
    @tag("mailjet")
    def test_create_vrich_checkout_task(self):
        create_vrich_checkout_task(1, "test456456")


class WalletDailyDeductTaskTestCase(SimpleTestCase):
    @skipIf(True, "This will send request to production server")
    @tag('wallet-task')
    def test_create_wallet_daily_deduct_task(self):
        create_wallet_daily_deduct_task(1)
