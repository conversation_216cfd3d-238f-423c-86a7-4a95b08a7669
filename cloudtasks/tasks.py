# from google.protobuf import timestamp_pb2
import json

from google.cloud import tasks_v2
from django.conf import settings
from typing import TYPE_CHECKING, Optional
from datetime import date, datetime

import sentry_sdk
from core.logging import logger

if TYPE_CHECKING:
    from companies.models import Company
    from picking.models import <PERSON><PERSON><PERSON><PERSON>
    from picking.models import Pick<PERSON>rderTrackingNo
    from etax.models import TaxDocument


PROJECT = "dobybot"
LOCATION = "asia-southeast1"
BASE_URL = settings.CLOUD_TASK_HOST
AUTH_TOKEN = settings.CLOUD_TASK_AUTH_TOKEN

DOBYBOT_CONNECT_HOST = settings.DOBYBOT_CONNECT_HOST
DOBYBOT_CONNECT_APIKEY = settings.DOBYBOT_CONNECT_APIKEY
DOBYSYNC_HOST = settings.DOBYSYNC_HOST


def create_task(queue, url, payload, headers=None):
    client = tasks_v2.CloudTasksClient()

    # Construct the fully qualified queue name.
    parent = client.queue_path(PROJECT, LOCATION, queue)

    if not headers:
        headers = {
            "Content-type": "application/json",
            "Authorization": f"Token {AUTH_TOKEN}",
        }

    # Construct the request body.
    task = {
        "http_request": {
            "http_method": tasks_v2.HttpMethod.POST,
            "url": url,
            "headers": headers,
            "body": json.dumps(payload).encode(),
        }
    }

    # Use the client to build and send the task.
    response = client.create_task(request={"parent": parent, "task": task})
    return response.name


def create_sync_orders_task(company_id: int):
    client = tasks_v2.CloudTasksClient()

    # Construct the fully qualified queue name.
    queue = "sync-orders"
    parent = client.queue_path(PROJECT, LOCATION, queue)

    # Construct the request body.
    url = f"{BASE_URL}/api/picking/tasks/handler/sync-orders/"
    payload = {"company": company_id}
    task = {
        "http_request": {
            "http_method": tasks_v2.HttpMethod.POST,
            "url": url,
            "headers": {
                "Content-type": "application/json",
                "Authorization": f"Token {AUTH_TOKEN}",
            },
            "body": json.dumps(payload).encode(),
        }
    }

    # Use the client to build and send the task.
    response = client.create_task(request={"parent": parent, "task": task})
    return response.name


def create_vrich_checkout_task(company_id: int, order_number: str):
    client = tasks_v2.CloudTasksClient()

    # Construct the fully qualified queue name.
    queue = "vrich-checkout"
    parent = client.queue_path(PROJECT, LOCATION, queue)

    # Construct the request body.
    url = f"{BASE_URL}/api/picking/vrich/checkout/"
    payload = {"company": company_id, "order_number": order_number}
    task = {
        "http_request": {
            "http_method": tasks_v2.HttpMethod.POST,
            "url": url,
            "headers": {
                "Content-type": "application/json",
                "Authorization": f"Token {AUTH_TOKEN}",
            },
            "body": json.dumps(payload).encode(),
        }
    }

    # Use the client to build and send the task.
    response = client.create_task(request={"parent": parent, "task": task})
    return response.name


def create_import_order_task(import_type: str, import_request_id: int):
    return create_task(
        queue="orders-import",
        url=f"{BASE_URL}/api/importdata/orders/import/{import_type}/",
        payload={"import_request_id": import_request_id},
    )


def create_wallet_daily_deduct_task(company_id: int):
    queue = "wallet-daily-deduct"
    url = f"{BASE_URL}/api/wallets/daily-deduct-tasks/handler/"
    payload = {"company": company_id}
    return create_task(queue, url, payload)


def create_google_drive_daily_delete_video_folder_task(company_id: int):
    queue = "google-drive-daily-delete-video-folder"
    url = f"{BASE_URL}/api/drivemanager/delete-video-folder/handler/"
    payload = {"company": company_id}
    return create_task(queue, url, payload)


def create_company_print_pick_order_task(company_id: int):
    queue = "print-pick-order"
    url = f"{BASE_URL}/api/picking/tasks/handler/print/pick-order/"
    payload = {"company": company_id}
    return create_task(queue, url, payload)


def create_single_print_pick_order_task(
    company_id: int, order_number: str, idempotency: bool = True
):
    queue = "print-pick-order"
    url = f"{BASE_URL}/api/picking/tasks/handler/print/pick-order/"
    payload = {
        "company": company_id,
        "order_number": order_number,
        "idempotency": idempotency,
    }
    return create_task(queue, url, payload)


def create_webhook_task(webhook_type: str, payload: dict):
    return create_task(
        queue="webhook",
        url=f"{BASE_URL}/api/picking/tasks/handler/webhook/{webhook_type}/",
        payload=payload,
    )


def create_webhook_auth_task(company_id: int):
    return create_task(
        queue="webhook-auth",
        url=f"{BASE_URL}/api/companies/tasks/handler/webhook-auth/",
        payload={"company": company_id},
    )


def create_dobysync_task(company: "Company", queue, path, payload, headers=None):
    client = tasks_v2.CloudTasksClient()
    dobysync = company.get_dobybot_connect_service()

    # Construct the fully qualified queue name.
    parent = client.queue_path(PROJECT, LOCATION, queue)

    if not headers:
        headers = {
            "Content-type": "application/json",
            "Authorization": f"Token {dobysync.client_apikey}",
        }

    # Construct the request body.
    task = {
        "http_request": {
            "http_method": tasks_v2.HttpMethod.POST,
            "url": DOBYSYNC_HOST + path,
            "headers": headers,
            "body": json.dumps(payload).encode(),
        }
    }

    # Use the client to build and send the task.
    response = client.create_task(request={"parent": parent, "task": task})

    try:
        logger("dobysync").log_struct(
            {
                "request": {
                    "method": tasks_v2.HttpMethod.POST,
                    "url": DOBYSYNC_HOST + path,
                    "headers": headers,
                    "body": json.dumps(payload),
                },
            }
        )
    except Exception as e:
        sentry_sdk.capture_exception(e)

    return response.name


def create_dobybot_connect_task(queue, path, payload, headers=None):
    client = tasks_v2.CloudTasksClient()

    # Construct the fully qualified queue name.
    parent = client.queue_path(PROJECT, LOCATION, queue)

    if not headers:
        headers = {
            "Content-type": "application/json",
            "Authorization": f"Bearer {DOBYBOT_CONNECT_APIKEY}",
        }

    # Construct the request body.
    task = {
        "http_request": {
            "http_method": tasks_v2.HttpMethod.POST,
            "url": DOBYBOT_CONNECT_HOST + path,
            "headers": headers,
            "body": json.dumps(payload).encode(),
        }
    }

    # Use the client to build and send the task.
    response = client.create_task(request={"parent": parent, "task": task})
    return response.name


def create_zort_ready_to_ship_task(
    pick_order: "PickOrder",
):
    company = pick_order.company
    config = company.get_setting("ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG")

    other_shipment = config["shipment"].get("other", "none")
    shipment = config["shipment"].get(pick_order.order_marketplace, other_shipment)
    if shipment == "none":
        return None

    company: Company = pick_order.company
    version = company.get_setting("DOBYBOT_CONNECT_VERSION")

    logger("zort-ready-to-ship").log_struct(
        {
            "company": company.uuid,
            "type": "enqueue",
            "order_number": pick_order.order_number,
            "config": config,
            "version": version,
        }
    )

    if version == 1:
        return create_dobybot_connect_task(
            queue="dobybot-connect-tasks",
            path="/zortout/ready-to-ship",
            payload={
                "company_id": company.uuid,
                "order_number": pick_order.order_number,
                "shipment": shipment,
                "params": {"warehousecode": config["warehousecode"]},
            },
        )

    if version == 2:
        return create_dobysync_task(
            company,
            queue="dbbc2-tasks",
            path="/api/v1/marketplaces/orders/ready-to-ship/",
            payload={
                "marketplace": "ZORT",
                "order_number": pick_order.order_number,
                "shipment": shipment,
                "params": {"warehousecode": config["warehousecode"]},
            },
        )


def create_zort_update_order_status_task(
    pick_order: "PickOrder",
):
    company = pick_order.company
    config = company.get_setting("ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG")

    company: Company = pick_order.company
    version = company.get_setting("DOBYBOT_CONNECT_VERSION")

    logger("zort-update-order-status").log_struct(
        {
            "company": company.uuid,
            "type": "enqueue",
            "order_number": pick_order.order_number,
            "config": config,
            "version": version,
        }
    )

    if version == 1:
        return create_dobybot_connect_task(
            queue="dobybot-connect-tasks",
            path="/zortout/update-order-status",
            payload={
                "company_id": company.uuid,
                "order_number": pick_order.order_number,
                "status": config["status"],
                "params": {"wareshousecode": config["warehousecode"]},
            },
        )

    if version == 2:
        return create_dobysync_task(
            company,
            queue="dbbc2-tasks",
            path="/api/v1/marketplaces/zort/update-order-status/",
            payload={
                "order_number": pick_order.order_number,
                "status": config["status"],
                "params": {"warehousecode": config["warehousecode"]},
            },
        )


def create_lazada_ready_to_ship_task(pick_order: "PickOrder"):
    company: Company = pick_order.company
    version = company.get_setting("DOBYBOT_CONNECT_VERSION")
    if version == 1:
        return create_dobybot_connect_task(
            queue="dobybot-connect-tasks",
            path="/lazada/order/ready-to-ship",
            payload={
                "company_id": company.uuid,
                "shop_id": pick_order.order_marketplaceshop,
                "order_number": pick_order.order_number,
                "tracking_number": pick_order.order_trackingno,
            },
        )
    if version == 2:
        return create_dobysync_task(
            company,
            queue="dbbc2-tasks",
            path="/api/v1/marketplaces/orders/ready-to-ship/",
            payload={
                "shop_id": pick_order.order_marketplaceshop,
                "order_number": pick_order.order_number,
                "tracking_number": pick_order.order_trackingno,
                "marketplace": pick_order.order_marketplace.upper(),
                "params": {},
            },
        )


def create_nocnoc_ready_to_ship_task(pick_order: "PickOrder"):
    company: Company = pick_order.company

    version = company.get_setting("DOBYBOT_CONNECT_VERSION")
    if version == 1:
        return

    if version == 2:
        return create_dobysync_task(
            company,
            queue="dbbc2-tasks",
            path="/api/v1/marketplaces/orders/ready-to-ship/",
            payload={
                "marketplace": pick_order.order_marketplace.upper(),
                "shop_id": pick_order.order_marketplaceshop,
                "order_number": pick_order.order_number,
                "tracking_number": pick_order.order_trackingno,
            },
        )


def create_generate_tracking_number_task(pick_order: "PickOrder"):
    return create_task(
        queue="generate-tracking-number",
        url=f"{BASE_URL}/api/shipping/tasks/generate-tracking-number/handler/",
        payload={"company_id": pick_order.company_id, "pick_order_id": pick_order.id},
    )


def create_daily_packing_stat_calculation_task(company: "Company"):
    return create_task(
        queue="daily-packing-stat-calculation",
        url=f"{BASE_URL}/api/stats/tasks/handler/daily-packing-stat-calculation/",
        payload={"company": company.id},
    )


def create_kerry_update_shipping_status_task(po_tracking: "PickOrderTrackingNo"):
    return create_task(
        queue="kerry-update-shipping-status",
        url=f"{BASE_URL}/api/shipping/kerry/update-shipping/handler/",
        payload={
            "company_id": po_tracking.company_id,
            "po_tracking_id": po_tracking.id,
        },
    )


def create_cancel_order_task(company_id: int, order_number: str):
    return create_task(
        queue="dobybot-order-updated",
        url=f"{BASE_URL}/api/picking/tasks/handler/cancel-order/",
        payload={"company": company_id, "order_number": order_number},
    )


def create_predict_order_level_task(pick_order: "PickOrder"):
    return create_task(
        queue="predict-order-level",
        url=f"{BASE_URL}/api/picking/tasks/handler/predict-order-level/",
        payload={"company_id": pick_order.company_id, "pick_order_id": pick_order.id},
    )


def create_auto_etax_task(pick_order: "PickOrder"):
    return create_task(
        queue="auto-etax",
        url=f"{BASE_URL}/api/etax/auto-etax/tasks/handler/",
        payload={
            "company": pick_order.company_id,
            "order_number": pick_order.order_number,
        },
    )


def create_etax_cancel_and_renew_task(tax_document: "TaxDocument"):
    return create_task(
        queue="auto-etax-cancel-and-renew",
        url=f"{BASE_URL}/api/etax/cancel-and-renew/tasks/handler/",
        payload={
            "tax_document": tax_document.id,
        },
    )


def create_retry_pending_sms_task(
    company: "Company", timestamp_from: date, timestamp_to: date
):
    return create_task(
        queue="retry-pending-sms",
        url=f"{BASE_URL}/api/sms/tasks/handler/retry-pending-sms/",
        payload={
            "company": company.id,
            "timestamp_from": timestamp_from.isoformat(),
            "timestamp_to": timestamp_to.isoformat(),
        },
    )


def create_save_report_export_log_task(
    report_name: str,
    filter_dictionary: dict,
    export_by: int,
    timestamp: datetime,
    export_file_path: str,
    company: int,
):
    return create_task(
        queue="dobybot-oneoff-tasks",
        url=f"{BASE_URL}/api/report-v2/tasks/handler/save-report-export-log/",
        payload={
            "report_name": report_name,
            "filter_dictionary": filter_dictionary,
            "timestamp": timestamp.isoformat(),
            "export_by": export_by,
            "export_file_path": export_file_path,
            "company": company,
        },
    )


def create_ready_to_ship_messaging_task(
    company_id: int,
    pick_order_id: int,
) -> Optional[dict]:
    """
    Create a Cloud Task to handle post-record messaging

    Args:
        company_id: ID of the company
        pick_order_id: ID of the pick order

    Returns:
        Task creation response or None if disabled
    """
    return create_task(
        queue="post-record-message",
        url=f"{BASE_URL}/api/picking/tasks/handler/ready-to-ship-message/",
        payload={
            "company_id": company_id,
            "pick_order_id": pick_order_id,
        },
    )
