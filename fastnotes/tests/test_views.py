import json
from django.test import TestCase
from django.test.utils import tag

from core.tests.testutils import Test<PERSON>aseHelper, TestClient
from core.tests import setup

from companies.models import Company
from fastnotes.models import FastNote


class FastNoteTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        setup.init()
        self.company = Company.objects.get(name='Cusway')
        self.client = TestClient()
        self.client.login()

        FastNote.objects.create(
            company=self.company,
            notes=['NOTE1', 'NOTE2', 'NOTE3']
        )

    @tag('fastnotes')
    def test_module_is_defined(self):
        self.assertEqual(1, 1)

    @tag('fastnotes')
    def test_get_fastnote(self):
        res = self.client.get(f'/api/fastnotes/fastnote/{self.company.id}/')
        self.assertMatch(
            res.json(),
            {
                'fastnote': {
                    'company_id': int,
                    'notes': ['NOTE1', 'NOTE2', 'NOTE3']
                }
            }
        )

    @tag('fastnotes')
    def test_update_fastnote(self):
        res = self.client.put(
            f'/api/fastnotes/fastnote/{self.company.id}/',
            data=json.dumps({'notes': ['NOTE4', 'NOTE1', 'NOTE2', 'NOTE3']}),
            content_type='application/json'
        )
        self.assertMatch(
            res.json(),
            {'fastnote': {'company_id': int, 'notes': ['NOTE4', 'NOTE1', 'NOTE2', 'NOTE3']}}
        )

        fs = FastNote.objects.get(company_id=self.company.id)
        self.assertEqual(
            fs.notes,
            ['NOTE4', 'NOTE1', 'NOTE2', 'NOTE3']
        )
