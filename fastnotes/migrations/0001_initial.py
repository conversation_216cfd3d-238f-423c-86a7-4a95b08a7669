# Generated by Django 2.2.24 on 2021-12-04 16:25

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0003_auto_20211204_0244'),
    ]

    operations = [
        migrations.CreateModel(
            name='FastNote',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', django.contrib.postgres.fields.jsonb.JSONField()),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
            ],
        ),
    ]
