from dynamic_rest.viewsets import DynamicModelViewSet

from fastnotes.models import FastNote
from fastnotes.permissions import CanUseFastNotePage
from fastnotes.serializers import FastNoteSerializer


class FastNoteViewSet(DynamicModelViewSet):
    permission_classes = [CanUseFastNotePage]
    serializer_class = FastNoteSerializer

    def get_queryset(self, queryset=None):
        company = self.request.user.company
        return FastNote.objects.filter(company=company)
