from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>

# Create your models here.


def get_default_notes():
    return [
        " SCB - XXXXXXXXX ชื่อบัญชี ACCNAME ค่ะ โอนแล้วแจ้งชื่อที่อยู่เบอร์โทรได้เลยนะคะ",
        "ตอนนี้สินค้าหมดค่ะ รอของเข้าอีกไม่นาน คุณลูกค้าสามารถให้เราแจ้งเตือนของเข้าได้นะคะ รบกวนขอเบอร์โทรที่ส่งเอสเอมเอสได้ค่ะ เดี๋ยวพอของเข้าระบบแจ้งออโต้พร้อมลิ้งให้กลับมาซื้อในนี้ค่ะ",
        "เรียน คุณลูกค้าทราบ ทางร้านได้จัดส่งสินค้าที่คุณลูกค้าออเดอร์เข้ามาแล้ว แต่ขออนุญาตจัดส่ง .............................ตามไปที่หลังนะคะ เนื่องจากสินค้าเข้าล่าช้ากว่ากำหนดค่ะ จะจัดส่งให้คุณลูกค้าทันทีที่สินค้าเข้าค่ะ ขอบพระคณค่า _/\\_",
        "กรุณาส่งมาที่ บริษัท .... จำกัด ที่อยู่ XX/XXX ถนน ... เขต ... แขวง ... กทม. 10XXX โทร XXXXXXXXXX",
        "วิธีแก้ไขรีวิว คอมเม้นท์ค่า เปิด Application Lazada \n1. คลิกที่รูปคน ด้านล่างขวามือ (บัญชี) \n2. ดูรายการสั่งซื้อทั้งหมด -เลือกสินค้าที่เราต้องการรีวิว -คลิกไปที่ตัวหนังสือ \n3. จะขึ้นหน้าสินค้านั้นๆ (รายละเอียดคำสั่งซื้อ) \n4. เลือกกรอบข้อความแดงๆ เขียนว่า เขียนรีวิว \n5. คลิกเข้าไปค่ะ \n6. จะขึ้นข้อความที่เรารีวิวไว้ กดเข้าไปที่ข้อความนั้นๆ จะมีเคอเซอร์สำหรับให้แก้ไขได้ค่ะ",
        "การปรับ เพิ่มหรือลดคะแนนให้กับทางร้านค้า โดยแจ้งให้ทางผู้ซื้อดำเนินการดังนี้ \n1. เลือก การซื้อของฉัน ( My Purchases ) > รายการที่สำเร็จ ( Completed ) \n2. สินค้าที่ต้องการปรับ เพิ่ม-ลด คะแนน ( Order Detail ) \n3. เลื่อนลงมาด้านล่าง แตะที่รูปสินค้า ( Product ) > เลื่อนลงมาล่างสุด \" ดูความคิดเห็น \" ( Be the first to leave a comment ) \n4. ค้นหาความคิดเห็นที่ต้องการแก้ไข ( Comment ) > แตะจุดไข่ปลา 3 จุดด้านขวามือ กดปรับคะแนน ( Change Rating Rate Product ) จากนั้นให้กดปรับ, เพิ่มหรือลดคะแนนได้ตามต้องการ และกดบันทึกอีกครั้ง *หลังจากที่มีการให้คะแนนในครั้งแรกแล้ว ภายใน 30 วัน คุณยังสามารถกดปรับ เพิ่ม - ลด คะแนนได้ตามต้องการ และกดบันทึกอีกครั้ง (สามารถทำได้เพียงครั้งเดียวเท่านั้น)"
    ]


class FastNote(models.Model):
    company = models.OneToOneField(
        'companies.Company', primary_key=True, on_delete=models.CASCADE)
    notes = models.JSONField(default=get_default_notes)

    def __str__(self) -> str:
        return f"{self.company}'s notes"
