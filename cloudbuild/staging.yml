# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# [START cloudrun_django_cloudmigrate]
steps:

  # Build and push images
  - id: "build & push image"
    name: 'gcr.io/kaniko-project/executor:latest'
    args:
    - --destination=gcr.io/${PROJECT_ID}/${_SERVICE_NAME}
    - --cache=true
    - --cache-ttl=48h

  # Run python manage.py makemigrations
  - id: "apply migrations"
    name: "gcr.io/google-appengine/exec-wrapper"
    args:
      [
        "-i", "gcr.io/$PROJECT_ID/${_SERVICE_NAME}",
        "-s", "${PROJECT_ID}:${_REGION}:${_CLOUD_SQL_INSTANCE_NAME}",
        "-e", "SETTINGS_NAME=${_SECRET_SETTINGS_NAME}",
        "--", "python", "manage.py", "migrate",
      ]

  # Deploy app service to Cloud Run
  - id: "deploy cloudrun app service" 
    name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', '${_SERVICE_NAME}', 
      '--platform', 'managed',
      '--region', '${_REGION}',
      '--image', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}', 
      '--add-cloudsql-instances', 'dobybot:asia-southeast1:main-1',
      '--allow-unauthenticated'
    ]
  
substitutions:
  _CLOUD_SQL_INSTANCE_NAME: staging-1
  _REGION: asia-southeast1
  _SERVICE_NAME: staging-dobybot-app-service
  _SECRET_SETTINGS_NAME: staging_settings

# images:
#   - "gcr.io/${PROJECT_ID}/${_SERVICE_NAME}"
# [END cloudrun_django_cloudmigrate]

