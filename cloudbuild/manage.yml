steps:
  # Run python manage.py makemigrations
  - id: "Run manage.py command"
    name: "gcr.io/google-appengine/exec-wrapper"
    args:
      [
        "-i", "gcr.io/$PROJECT_ID/${_SERVICE_NAME}",
        "-s", "${PROJECT_ID}:${_REGION}:${_CLOUD_SQL_INSTANCE_NAME}",
        "-e", "SETTINGS_NAME=${_SECRET_SETTINGS_NAME}",
        "--", "python", "manage.py", "${_COMMAND}",
      ]

options:
  machineType: 'E2_HIGHCPU_8'

timeout: 3600s

substitutions:
  _CLOUD_SQL_INSTANCE_NAME: main-1
  _REGION: asia-southeast1
  _SERVICE_NAME: dobybot-app-service
  _SECRET_SETTINGS_NAME: dobybot_settings