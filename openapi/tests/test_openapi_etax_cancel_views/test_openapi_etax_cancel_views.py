from unittest.mock import patch
from django.test import TestCase, tag, LiveServerTestCase
from core.serializers.serializers import parse_date
from core.tests import setup
from core.tests.mocks.response import MockedResponse
from core.tests.testutils import TestCaseHelper, TestClient
from core.tests.mocks.google_drive import MockGoogleDriveService


# TODO.0 - Rename EtaxCancelAPI into something more descriptive
class EtaxCancelAPITestCase(LiveServerTestCase, TestCaseHelper):
    def setUp(self):
        # Setup company
        result = setup.init()
        self.company = result["company"]
        self.user = result["user"]
        self.wallet = result["wallet"]

        # Setup ETax
        setup.init_etax(self.company, self.live_server_url)

        # Setup Test Client
        self.client = TestClient()

        # TODO.1: - Choose one of the following methods to login
        self.client.login_with_authtoken()
        # self.client.login()

        patcher1 = patch(
            "services.etax_invoice.etax_service.GoogleDriveService",
            return_value=MockGoogleDriveService(),
        )
        patcher1.start()
        self.addCleanup(patcher1.stop)

        patcher2 = patch("openapi.views.base.log_openapi_req_res")
        patcher2.start()
        self.addCleanup(patcher2.stop)

    @tag("EtaxCancelAPI")
    def test_cancel_ok(self, *args):
        pick_order = setup.create_test_pickorder_from_file()
        _, tiv = setup.create_tax_document(pick_order)
        self.assertEqual(tiv.doc_id, "RT-929755684018356")
        self.assertEqual(tiv.status, "success")

        response = self.client.delete(
            "/openapi/v1/etax/document/RT-929755684018356/cancel/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            {"code": "OK", "data": None, "message": "Document cancelled"},
        )

        tiv.refresh_from_db()
        self.assertEqual(tiv.status, "cancel")

    @tag("EtaxCancelAPI")
    def test_cancel_error_notfound(self):
        pick_order = setup.create_test_pickorder_from_file()
        _, tiv = setup.create_tax_document(pick_order)
        self.assertEqual(tiv.status, "success")

        response = self.client.delete("/openapi/v1/etax/document/RT-NOTFOUND/cancel/")
        self.assertEqual(response.status_code, 404)
        self.assertEqual(
            response.json(),
            {
                "code": "NOT_FOUND",
                "message": "Could not find object with the specified doc_id",
                "data": None,
            },
        )

    @tag("EtaxCancelAPI")
    def test_cancel_error_rd_submitted(self):
        pick_order = setup.create_test_pickorder_from_file()
        _, tiv = setup.create_tax_document(pick_order)
        tiv.doc_info["DOC_ISSUE_DATE"] = "2025-01-01"
        tiv.save()

        self.assertEqual(tiv.doc_id, "RT-929755684018356")
        self.assertEqual(tiv.status, "success")

        response = self.client.delete(
            "/openapi/v1/etax/document/RT-929755684018356/cancel/"
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json(),
            {
                "code": "RD_SUBMITTED",
                "message": "Could not cancel, the document has been submitted to Revenue Department",
                "data": None,
            },
        )

    @tag("EtaxCancelAPI")
    def test_cancel_error_credit_note(self):
        pick_order = setup.create_test_pickorder_from_file()
        _, tiv = setup.create_tax_document(pick_order)

        # Create credit note #1

        res1 = self.client.post(
            "/openapi/v1/etax/document/credit-note/0/",
            data={"ref_doc_id": "RT-929755684018356"},
            format="json",
        )
        self.assertEqual(res1.status_code, 200)

        # Try to cancel
        response = self.client.delete(
            "/openapi/v1/etax/document/RT-929755684018356/cancel/"
        )
        self.assertEqual(response.status_code, 200)
        self.assert_response(response)

    @tag("EtaxCancelAPI")
    def test_throttling(self):
        pick_order = setup.create_test_pickorder_from_file()
        _, tiv = setup.create_tax_document(pick_order)
        self.assertEqual(tiv.doc_id, "RT-929755684018356")
        self.assertEqual(tiv.status, "success")

        for i in range(100):
            response = self.client.delete(
                "/openapi/v1/etax/document/RT-929755684018356/cancel/"
            )
            if i == 0:
                self.assertEqual(response.status_code, 200)
            elif 1 <= i < 60:
                self.assertEqual(response.status_code, 400)
            elif 60 <= i:
                self.assertEqual(response.status_code, 429)

    @tag("EtaxCancelAPI")
    def test_cancel_with_slash_in_docid(self):
        pick_order = setup.create_test_pickorder_from_file()
        _, tiv = setup.create_tax_document(pick_order)
        # Modify the doc_id to include a slash
        tiv.doc_id = "RT/929755684018356"
        tiv.save()
        
        # Use -slash- in the URL instead of actual /
        response = self.client.delete(
            "/openapi/v1/etax/document/RT-slash-929755684018356/cancel/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            {"code": "OK", "data": None, "message": "Document cancelled"},
        )

        tiv.refresh_from_db()
        self.assertEqual(tiv.status, "cancel")
