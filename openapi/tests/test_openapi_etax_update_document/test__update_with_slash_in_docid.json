{"request": {"seller": {"branch_code": "00001"}}, "response": {"status_code": 200, "data": {"buyer": {"buyer_name": "บริษัท ทดสอบ จำกัด", "tax_id": "1234567890123", "branch_code": "", "branch_name": "", "address": "123/45 ถนนExample แขวง/ตำบลTest เขต/อำเภอExample จังหวัดTest 10110", "post_code": "10110", "email": "<EMAIL>", "phone_number": "+6**********", "tax_type": "NIDN", "country_id": "TH", "is_consent_marketing": false}, "seller": {"seller_name": "Cusway", "tax_id": "0105559130701", "branch_code": "00001", "branch_name": "สาขา 1", "address": "ที่อยู่ xxxx สาขา 1", "post_code": "10230", "phone_number": "+66-*********", "tax_type": "TXID", "country_id": "TH"}, "order": {"list": [{"sku": "P001", "name": "ปากกา", "number": 100, "pricepernumber": "10.0000", "totalprice": "900.0000", "discount": "100.0000", "vatamount": "58.8785", "pretaxamount": "841.1215"}, {"sku": "N002", "name": "สมุด", "number": 50, "pricepernumber": "200.0000", "totalprice": "9500.0000", "discount": "500.0000", "vatamount": "621.4953", "pretaxamount": "8878.5047"}], "number": "SO-20231027-001", "totaldiscount": "700.0000", "pretaxamount": "9626.1700", "totalvatamount": "673.8300", "grandtotal": "10300.0000", "vattype": "3"}, "document": {"doc_id": "TIV/20231027/001", "doc_url": "https://drive.google.com/file/d/file_id_1234/view", "doc_name": "ใบเสร็จรับเงิน/ใบกำกับภาษี", "doc_type": "T03", "doc_create_date": "<type:str-datetime>", "doc_issue_date": "<type:str-datetime>", "doc_email_flag": "Y", "doc_purpose_code": "", "doc_purpose_detail": "", "ref_doc_id": "", "ref_doc_issue_date": null, "ref_doc_type": "", "status": "success", "status_reason": null}}}, "buyer_after": {"email": "<EMAIL>", "tax_id": "1234567890123", "address": "123/45 ถนนExample แขวง/ตำบลTest เขต/อำเภอExample จังหวัดTest 10110", "post_code": "10110", "buyer_name": "บริษัท ทดสอบ จำกัด", "phone_number": "*********", "is_consent_marketing": false}, "doc_info_after": {"BUYER_NAME": "บริษัท ทดสอบ จำกัด", "BUYER_TAX_ID": "1234567890123", "BUYER_ADDRESS": "123/45 ถนนExample แขวง/ตำบลTest เขต/อำเภอExample จังหวัดTest 10110", "BUYER_POSTCODE": "10110", "BUYER_TYPE_TAX": "NIDN", "BUYER_COUNTRY_ID": "TH", "BUYER_BRANCH": "", "BUYER_BRANCH_NAME": "", "BUYER_CONTACT_EMAIL": "<EMAIL>", "BUYER_CONTACT_PHONE": "+66-21234567", "SELLER_NAME": "Cusway", "SELLER_BRANCH": "00001", "SELLER_ADDRESS": "ที่อยู่ xxxx สาขา 1", "SELLER_CONTACT_PHONE": "+66-*********"}}