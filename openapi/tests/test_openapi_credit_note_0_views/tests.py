from unittest.mock import patch
from django.test import LiveServerTestCase, TestCase, tag
from core.tests import setup
from core.tests.mocks.google_drive import MockGoogleDriveService
from core.tests.testutils import TestCaseHelper, TestClient
from etax.models import TaxDocument


class OpenAPICreditNoteZeroTestCase(LiveServerTestCase, TestCaseHelper):
    def setUp(self):
        # Setup company
        result = setup.init()
        self.company = result["company"]
        self.user = result["user"]
        self.wallet = result["wallet"]
        setup.init_etax(self.company, self.live_server_url)

        # Setup Test Client
        self.client = TestClient()

        # TODO.1: - Choose one of the following methods to login
        self.client.login_with_authtoken()
        # self.client.login()

        # TODO.2: - Mock & Patch
        patcher1 = patch(
            "services.etax_invoice.etax_service.GoogleDriveService",
            return_value=MockGoogleDriveService(),
        )
        patcher1.start()
        self.addCleanup(patcher1.stop)
        patcher2 = patch("openapi.views.base.log_openapi_req_res")
        patcher2.start()
        self.addCleanup(patcher2.stop)

    @tag("OpenAPICreditNoteZero")
    def test_create_credit_note_success(self):
        pick_order = setup.create_test_pickorder_from_file(
            filename=".testdata/pick_order/zort_lazada_929755684018356.json"
        )
        setup.create_tax_document(pick_order)

        data = self.load_testdata()
        response = self.client.post(
            "/openapi/v1/etax/document/credit-note/0/",
            data=data,
            format="json",
        )
        self.assert_response(response)
    
    @tag("OpenAPICreditNoteZero")
    def test_create_credit_note_validation_error(self):
        data = self.load_testdata()
        response = self.client.post(
            "/openapi/v1/etax/document/credit-note/0/",
            data=data,
            format="json",
        )
        self.assert_response(response)

    @tag("OpenAPICreditNoteZero")
    def test_create_credit_note_fail_already_existed(self):
        pick_order = setup.create_test_pickorder_from_file(
            filename=".testdata/pick_order/zort_lazada_929755684018356.json"
        )
        setup.create_tax_document(pick_order)

        # Create credit note #1
        data = self.load_testdata()
        res1 = self.client.post(
            "/openapi/v1/etax/document/credit-note/0/",
            data=data,
            format="json",
        )
        self.assertEqual(res1.status_code, 200)

        # Create credit note #2
        res2 = self.client.post(
            "/openapi/v1/etax/document/credit-note/0/",
            data=data,
            format="json",
        )
        self.assert_response(res2)

    @tag("OpenAPICreditNoteZero")
    @tag("OpenAPICreditNoteZero_2")
    def test_create_credit_note_fail_status_not_success(self):
        pick_order = setup.create_test_pickorder_from_file(
            filename=".testdata/pick_order/zort_lazada_929755684018356.json"
        )
        _, tiv = setup.create_tax_document(pick_order)
        tiv.status = TaxDocument.STATUS_CANCEL
        tiv.save()

        # Create credit note #1
        data = self.load_testdata()
        res1 = self.client.post(
            "/openapi/v1/etax/document/credit-note/0/",
            data=data,
            format="json",
        )
        self.assert_response(res1)