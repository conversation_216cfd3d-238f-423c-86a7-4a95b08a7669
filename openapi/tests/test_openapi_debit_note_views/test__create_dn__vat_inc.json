{"request": {"ref_doc_id": "TIV-20231027-001", "doc_issue_date": "2023-11-27", "order": {"discountamount": 100.0, "amount": 10400.0, "list": [{"sku": "P001", "name": "ปากกา", "number": 100.0, "pricepernumber": 11.0, "totalprice": 1000.0, "discount": 100.0}]}, "doc_id": "DN-20231027-001", "doc_purpose_code": "DBNG02", "doc_purpose_detail": "คํานวณราคาสินค้า ผิดพลาดต่ำกว่าที่เป็นจริง"}, "response": {"status_code": 200, "data": {"buyer": {"buyer_name": "บริษัท ทดสอบ จำกัด", "tax_id": "1234567890123", "branch_code": "", "branch_name": "", "address": "123/45 ถนนExample แขวง/ตำบลTest เขต/อำเภอExample จังหวัดTest 10110", "post_code": "10110", "email": "<EMAIL>", "phone_number": "+6***********", "tax_type": "NIDN", "country_id": "TH", "is_consent_marketing": false}, "seller": {"seller_name": "Cusway", "tax_id": "0105559130701", "branch_code": "00000", "branch_name": "สำนักงานใหญ่", "address": "88/229 โครงการ <PERSON>amese Blossom, ถ. รามอินทรา แขวงคันนายาว เขตคันนายาว กรุงเทพมหานคร 10230", "post_code": "10230", "phone_number": "0886604941", "tax_type": "TXID", "country_id": "TH"}, "order": {"list": [{"sku": "P001", "name": "ปากกา", "number": 100, "pricepernumber": "11.0000", "totalprice": "1000.0000", "discount": "100.0000", "vatamount": "65.4206", "pretaxamount": "934.5794"}], "number": "SO-20231027-001", "totaldiscount": "200.0000", "pretaxamount": "-93.4600", "totalvatamount": "-6.5400", "grandtotal": "-100.0000", "vattype": "3"}, "document": {"doc_id": "DN-20231027-001", "doc_url": "https://drive.google.com/file/d/file_id_1234/view", "doc_name": "ใบเพิ่มหนี้", "doc_type": "80", "doc_create_date": "<type:str-datetime>", "doc_issue_date": "2023-11-27T00:00:00+07:00", "doc_email_flag": "Y", "doc_purpose_code": "DBNG02", "doc_purpose_detail": "คํานวณราคาสินค้า ผิดพลาดต่ำกว่าที่เป็นจริง", "ref_doc_id": "TIV-20231027-001", "ref_doc_issue_date": "<type:str-datetime>", "ref_doc_type": "T03", "status": "success", "status_reason": null}}}}