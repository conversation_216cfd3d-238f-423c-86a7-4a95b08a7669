from unittest.mock import patch
from django.test import LiveServerTestCase, TestCase, tag
from core.tests import setup
from core.tests.mocks.google_drive import MockGoogleDriveService
from core.tests.testutils import TestCaseHelper, TestClient
from etax.models import TaxDocument
from picking.models import PickOrder

from django.conf import settings
import json


class ETaxCreateDocumentAPIViewTestCase(LiveServerTestCase, TestCaseHelper):
    EXPECT_OVERRIDE = {}

    def setUp(self):
        # Setup company
        result = setup.init()
        self.company = result["company"]
        self.user = result["user"]
        self.wallet = result["wallet"]

        setup.init_etax(self.company, self.live_server_url)

        # Setup Test Client
        self.client = TestClient()

        # 1: - Choose one of the following methods to login
        self.client.login_with_authtoken()
        # self.client.login()

        # 2: - Mo<PERSON> & <PERSON>
        patcher1 = patch(
            "services.etax_invoice.etax_service.GoogleDriveService",
            return_value=MockGoogleDriveService(),
        )
        patcher1.start()
        self.addCleanup(patcher1.stop)
        patcher2 = patch("openapi.views.base.log_openapi_req_res")
        patcher2.start()
        self.addCleanup(patcher2.stop)

    def setup_create_t03(self, filename):
        with open(settings.BASE_DIR / "openapi/tests/test_openapi_etax_create_document" / filename) as f:
            testdata = json.load(f)
            self.client.post(
                "/openapi/v1/etax/document/", data=testdata["request"], format="json"
            )

    @tag("OpenApiEtaxCreditNote", "CN001")
    def test__create_cn__vat_inc(self):
        self.setup_create_t03("test__doc_T03__vat_inc__vat7.json")
        ref_doc = TaxDocument.objects.get(doc_id="TIV-20231027-001")
        print(ref_doc)

        payload = self.load_testdata()["request"]
        response = self.client.post(
            "/openapi/v1/etax/document/credit-note/",
            data=payload,
            format="json",
        )
        self.assert_response(response, override=self.EXPECT_OVERRIDE)

    @tag("OpenApiEtaxCreditNote", "CN002")
    def test__create_cn__vat_exc(self):
        self.setup_create_t03("test__doc_T03__vat_exc__vat7.json")
        ref_doc = TaxDocument.objects.get(doc_id="TIV-20231027-001")
        print(ref_doc)

        payload = self.load_testdata()["request"]
        response = self.client.post(
            "/openapi/v1/etax/document/credit-note/",
            data=payload,
            format="json",
        )
        self.assert_response(response, override=self.EXPECT_OVERRIDE)
