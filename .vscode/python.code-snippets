{
	// Place your dobybot workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope 
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is 
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are: 
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. 
	// Placeholders with the same ids are connected.
	// Example:
	// "Print to console": {
	// 	"scope": "javascript,typescript",
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
	"Template for django SimpleTestCase": {
		"prefix": "file_test_simple",
		"body": [
			"from django.test import SimpleTestCase, tag",
			"",
			"class ${1:TestClassName}TestCase(SimpleTestCase):",
			"    def setUp(self):",
			"        pass",
			"",
			"    @tag('${2:method_name}')",
			"    def test_${2:method_name}(self):",
			"        print('Test ${2:method_name} is running')",
		]
	},
	"Template for django TestCase": {
		"prefix": "file_test",
		"body": [
		]
	},
}