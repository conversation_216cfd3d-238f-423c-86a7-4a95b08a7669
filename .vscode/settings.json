{
  "python.linting.pylintArgs": [
    "--rcfile=.devcontainer/.pylintrc",
    "--django-settings-module=core.settings"
  ],
  "pylint.path": [
    "./venv/bin/pylint"
  ],
  // "editor.formatOnSave": true,
  "python.formatting.provider": "none",
  "python.formatting.autopep8Args": [
    "--max-line-length",
    "100",
    "--experimental"
  ],
  "files.exclude": {
    "**/__pycache__": true,
    "venv/": true
  },
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter"
  },
  "autopep8.args": [
    "--max-line-length",
    "100"
  ],
  "python.defaultInterpreterPath": "python",
  "terminal.integrated.env.osx": {
    "MallocNanoZone": "1"
  },
  "autoDocstring.docstringFormat": "numpy",
  "postman.settings.dotenv-detection-notification-visibility": false
}