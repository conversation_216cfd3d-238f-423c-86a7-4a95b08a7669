{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "coverage",
            "type": "shell",
            "command": "alias coverage=/Users/<USER>/Projects/dobybot.com/dobybot/venv/bin/coverage && coverage run --rcfile='.coveragerc' manage.py test && coverage html && open htmlcov/index.html"
        }
    ]
}