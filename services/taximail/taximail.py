import json
import requests
import typing

if typing.TYPE_CHECKING:
    from companies.models.models import Company
    from picking.models import PickOrder

from django.conf import settings

token = settings.TAXIMAIL_APITOKEN

class TaximailTemplates:
    READY_TO_SHIP = {
        "key": "202246800bfc51af14",
        "name": "template2"
    }


def send_taximail(
    to_name: str,
    to_email: str,
    from_name: str,
    template: dict,
    content: dict,
):
    url = "https://api.taximail.com/v2/transactional"

    # content = {
    #     "Firstname": "กฤช ฐานะโสภณ",
    #     "CF_ordernumber": "12321312",
    #     "CF_orderchannel": "Zen Barista-LAZADA",
    #     "CF_short_link": "https://www.dobybot.com",
    #     "CF_other_detail": "",
    # }

    payload = {
        "transactional_group_name": "Default",
        "to_name": to_name,
        "to_email": to_email,
        "from_name": from_name,
        "from_email": "<EMAIL>",
        "reply_name": "noreply",
        "reply_email": "<EMAIL>",
        # "template_key": "202246800bfc51af14",
        # "template_name": "template2",
        "template_key": template["key"],
        "template_name": template["name"],
        "content_html": json.dumps(content),
    }
    headers = {
        "Authorization": f"Basic {token}",
        # "Cookie": "AWSALB=RAv8KwZ8Gb1InXJu4t73KfHDAGt09/IBom3y8TFQF1wFARf5YGo5VnQqwJAZ929qB4yB/B7LO2M7+0tZs7WAK88AKjXyysbEYc+/yAM5F54/CPSXtooyzlsa8AgD; AWSALBCORS=RAv8KwZ8Gb1InXJu4t73KfHDAGt09/IBom3y8TFQF1wFARf5YGo5VnQqwJAZ929qB4yB/B7LO2M7+0tZs7WAK88AKjXyysbEYc+/yAM5F54/CPSXtooyzlsa8AgD",
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    return response


def send_taximail_ready_to_ship(pick_order: "PickOrder"):
    company: Company = pick_order.company
    from_name = company.get_setting("EMAIL_SENDER")
    to_email = pick_order.order_json.get("shippingemail") or pick_order.order_json.get(
        "customeremail"
    )

    return send_taximail(
        to_email=to_email,
        to_name=pick_order.order_customer,
        from_name=from_name,
        template=TaximailTemplates.READY_TO_SHIP,
        content={
            "Firstname": pick_order.order_customer,
            "CF_ordernumber": pick_order.order_number,
            "CF_orderchannel": pick_order.order_saleschannel,
            "CF_short_link": pick_order.receipt_url,
            "CF_other_detail": "",
        },
    )
