from unittest import mock
from django.test import TestCase, tag
from core.tests import setup
from core.tests.testutils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from picking.models import PickOrder

from services.taximail.taximail import send_taximail, send_taximail_ready_to_ship


class TaxiMailTestCase(TestCase, TestCaseHelper):
    def setUp(self):
        # Setup company and user
        result = setup.init()
        self.company = result["company"]
        self.user = result["user"]

        # Create a test pick order
        setup.create_test_pickorders(company=self.company)
        self.pick_order = PickOrder.objects.first()

        # Ensure order_json has the required fields
        self.pick_order.order_json = {
            "shippingemail": "<EMAIL>",
            "customeremail": "<EMAIL>",
        }
        self.pick_order.save()

    @mock.patch("services.taximail.taximail.send_taximail")
    @tag("taximail")
    def test_send_taximail_ready_to_ship_with_shipping_email(self, mock_send_taximail):
        """Test send_taximail_ready_to_ship with shipping email available"""
        # Setup mock return value
        mock_send_taximail.return_value = mock.MagicMock()

        # Call the function
        send_taximail_ready_to_ship(self.pick_order)

        # Assert send_taximail was called with correct parameters
        mock_send_taximail.assert_called_once()
        call_args = mock_send_taximail.call_args[1]  # Get keyword arguments

        # Check email is the shipping email
        self.assertEqual(call_args["to_email"], "<EMAIL>")
        self.assertEqual(call_args["to_name"], self.pick_order.order_customer)
        self.assertEqual(call_args["from_name"], "OrderNotice")

        # Check template
        self.assertEqual(call_args["template"]["key"], "202246800bfc51af14")
        self.assertEqual(call_args["template"]["name"], "template2")

        # Check content
        self.assertEqual(
            call_args["content"]["Firstname"], self.pick_order.order_customer
        )
        self.assertEqual(
            call_args["content"]["CF_ordernumber"], self.pick_order.order_number
        )
        self.assertEqual(
            call_args["content"]["CF_orderchannel"], self.pick_order.order_saleschannel
        )
        self.assertEqual(
            call_args["content"]["CF_short_link"], self.pick_order.receipt_url
        )
        self.assertEqual(call_args["content"]["CF_other_detail"], "")

    @tag("taximail")
    def test_send_taximail_ready_to_ship_with_shipping_email_2(self):
        """Test send_taximail_ready_to_ship with shipping email available"""
        send_taximail_ready_to_ship(self.pick_order)

    @mock.patch("services.taximail.taximail.send_taximail")
    @tag("taximail")
    def test_send_taximail_ready_to_ship_with_customer_email(self, mock_send_taximail):
        """Test send_taximail_ready_to_ship with only customer email available"""
        # Update pick order to have only customer email
        self.pick_order.order_json = {
            "shippingemail": None,
            "customeremail": "<EMAIL>",
        }
        self.pick_order.save()

        # Setup mock return value
        mock_send_taximail.return_value = mock.MagicMock()

        # Call the function
        send_taximail_ready_to_ship(self.pick_order)

        # Assert send_taximail was called with correct parameters
        mock_send_taximail.assert_called_once()
        call_args = mock_send_taximail.call_args[1]  # Get keyword arguments

        # Check email is the customer email
        self.assertEqual(call_args["to_email"], "<EMAIL>")

    @mock.patch("services.taximail.taximail.send_taximail")
    @tag("taximail")
    def test_send_taximail_ready_to_ship_with_empty_email(self, mock_send_taximail):
        """Test send_taximail_ready_to_ship with no email available"""
        # Update pick order to have no email
        self.pick_order.order_json = {
            "shippingemail": None,
            "customeremail": None,
        }
        self.pick_order.save()

        # Setup mock return value
        mock_send_taximail.return_value = mock.MagicMock()

        # Call the function
        send_taximail_ready_to_ship(self.pick_order)

        # Assert send_taximail was called with correct parameters
        mock_send_taximail.assert_called_once()
        call_args = mock_send_taximail.call_args[1]  # Get keyword arguments

        # Check email is None
        self.assertEqual(call_args["to_email"], None)
