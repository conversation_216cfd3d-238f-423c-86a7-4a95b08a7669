from rest_framework import serializers

from importdata.serializers import DateTimeField, DecimalField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI
import decimal

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'บิลเลขที่': 'number',
    'หมายเลข SKU': 'list__sku',
    'หมายเลขซีเรียลสินค้า': 'list__sku_barcode',
    'ชื่อสินค้า': 'list__name',
    'ราคา': 'list__pricepernumber',
    'ขายแล้ว': 'list__number',
    'ส่วนลด': 'list__discount',
    'ยอดชำระ': 'list__totalprice',
    'จำนวนเงินที่ต้องได้รับ': 'paymentamount',
    'Account': 'shippingname',
    'ค่าส่ง': 'shippingamount',
    'โลจิสติก': 'shippingchannel',
    'ที่อยู่สำหรับการจัดส่ง': 'shippingaddress',
    'เบอร์โทร': 'shippingphone',
    'เลขที่พัสดุ': 'trackingno',
    'หมายเหตุสลิปการชำระเงิน': 'remark',
    'ช่องทางดูดออเดอร์': 'saleschannel'
}
TYPE = '037-jambolive-2'

def remove_decimal(number_str):
    try:
        return str(int(float(number_str)))
    except ValueError:
        return number_str

class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        
        try:
            value = decimal.Decimal(data)
            data = value.quantize(decimal.Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)
        except decimal.DecimalException:
            self.fail('invalid')

        return super().to_internal_value(data)

class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField()
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)

    # Optional

    remark = serializers.CharField(required=False, allow_blank=True)
    saleschannel = serializers.CharField(required=False, allow_blank=True)
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    trackingno = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__sku_barcode = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__pricepernumber = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__number = BlankableDecimalField( max_digits=12, decimal_places=2, default=1, required=False)
    list__discount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        for item in  order["list"]:
            item["sku"] = remove_decimal(item["sku"])
            item["sku_barcode"] = remove_decimal(item["sku_barcode"]) 

        return order

