
from importdata.serializers import DateT<PERSON><PERSON>ield, DecimalField
from email.policy import default
from rest_framework import serializers
from .generics import OrderUploadGeneric<PERSON>I, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'เลขที่ออเดอร์มาร์เก็ตเพลส': 'invoice_number',
    'เลขที่เอกสาร': 'number',
    'ยอดสุทธิ': 'paymentamount',
    'ค่า COD': 'amount',
    'วันที่-เวลา': 'orderdateString',
    'ชื่อลูกค้า': 'customername',
    'เลขแทร็ก': 'trackingno',
    'ส่วนลด': 'discountamount',
    'ช่องทางการขาย': 'saleschannel',

    'ชื่อผู้รับ': 'shippingname',
    'เบอร์โทร': 'shippingphone',
    'ที่อยู่ผู้รับ': 'shippingaddress',
    'E-Mail': 'shippingemail',
    'วิธีการส่ง': 'shippingchannel',
    'ค่าส่งสินค้า': 'shippingamount',
    'รหัสสินค้า': 'product_code',
    'ออฟชั่น': 'option',
    'ชื่อสินค้า': 'list__name',
    'จำนวน.1': 'list__number',
    'ราคา/หน่วย': 'list__pricepernumber',
    'ราคารวม': 'list__totalprice',
    '-> รายละเอียดสินค้า': 'remark',
}
TYPE = '021-commerzy-2'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class CommerzyOrderUploadSerializer(serializers.Serializer):
    # Required
    shippingphone = serializers.CharField(max_length=400)
    saleschannel = serializers.CharField(max_length=400)

    number = serializers.CharField(max_length=100, allow_blank=True, default='')
    invoice_number = serializers.CharField(max_length=100, allow_blank=True, default='')
    shippingname = serializers.CharField(max_length=400, allow_blank=True, default='No name')
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    orderdateString = DateTimeField(input_formats=["%Y-%m-%d %H:%M:%S", "%Y/%m/%d %I:%M:%S %p"])
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    product_code = serializers.CharField(required=False, allow_blank=True, default='')
    option = serializers.CharField(required=False, allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField( max_digits=12, decimal_places=2, default=1, required=False)
    list__pricepernumber = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class CommerzyOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'เลขแทร็ก': str, 'เบอร์โทร': str}
    copy_upper_row_data = ['number', 'shippingname', 'orderdateString',
                           'invoice_number', 'saleschannel', 'shippingphone', 'paymentamount']
    serializer_class = CommerzyOrderUploadSerializer


class CommerzyOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'เลขแทร็ก': str,'เบอร์โทร': str}
    copy_upper_row_data = ['number', 'shippingname', 'orderdateString',
                           'invoice_number', 'saleschannel', 'shippingphone', 'paymentamount']
    serializer_class = CommerzyOrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        if row['number'] == "":
            order['number'] = row['invoice_number']

        if row['amount'] > 0:
            order["isCOD"] = True
        
        item = order['list'][len(order['list']) - 1]
        item['sku'] = row['product_code'] + '---' + row['option']
        
        return order
