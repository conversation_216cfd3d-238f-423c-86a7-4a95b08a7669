from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


FIELD_MAPS = {
    'หมายเลขพัสดุ': 'number',
    'หมายเลขพัสดุจากขนส่ง': 'trackingno',
    'ชื่อผู้รับ': 'shippingname',
    'เบอร์โทรผู้รับ': 'shippingphone',
    'ที่อยู่ปลายทาง': 'shippingaddress',
    'ขนส่ง': 'shippingchannel',
    'หมายเหตุ': 'remark'
}
IMPORT_TYPE = '026-shippop'


class OrderUploadSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    trackingno = serializers.CharField(required=True, allow_blank=True)
    shippingname = serializers.Char<PERSON>ield(max_length=200)
    shippingphone = serializers.Char<PERSON>ield(max_length=50)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    remark = serializers.Char<PERSON>ield(
        required=False, allow_blank=True, default="")


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
