from email.policy import default
from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    # 'saleschannel': 'saleschannel',
    'เลขที่บิล': 'number',
    'ชื่อลูกค้า': 'shippingname',
    # 'shippingphone': 'shippingphone',

    # 'trackingno': 'trackingno',
    # 'paymentamount': 'paymentamount',
    # 'discountamount': 'discountamount',
    # 'shippingaddress': 'shippingaddress',
    # 'shippingemail': 'shippingemail',
    # 'shippingchannel': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    'รหัสสินค้า': 'list__sku',
    'ชื่อสินค้า': 'list__name',
    'ปริมาณ': 'list__number',
    'ราคาต่อหน่วย': 'list__pricepernumber',
    'จำนวนเงินรวม (LC)': 'list__totalprice',
    'รหัสหน่วยนับ': 'list__unittext',
    # 'remark': 'remark',
}
TYPE = '024-wang-nam-khiao'


class THBDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            return self.default

        # data = THB 8,190.00
        # replace THB and , with empty string and convert to float
        if isinstance(data, str):
            data = data.replace('THB', '').replace(',', '').strip()
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400, required=False, default='-')
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400, required=False, default='-')
    customerphone = serializers.CharField(max_length=400, required=False, default='-')
    # paymentamount = THBDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    # shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True, default='')
    # shippingamount = THBDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = THBDecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = THBDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = serializers.DecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = THBDecimalField(max_digits=12, decimal_places=2, default=0)
    list__pricepernumber = THBDecimalField(max_digits=12, decimal_places=2, default=0)
    list__unittext = serializers.CharField()
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def post_process_orders(self, orders: Dict):
        for order in orders.values():
            order['list'] = [x for x in order['list'] if x['number'] > 0]
            order['paymentamount'] = sum([x['totalprice'] for x in order['list']])
            order['amount'] = order['paymentamount']
        return orders
