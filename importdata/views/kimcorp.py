
from decimal import Decimal
from email.policy import default
import re
from typing import Dict
from rest_framework import serializers
from importdata.serializers import DecimalField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

IMPORT_TYPE = '007-kimcorp'

# ติดต่อ         | ชื่อ                                |ที่อยู่ผู้ส่ง                           |รหัสติดตามสถานะพัสดุ   |สถานะโดยละเอียด|ชื่อผู้ส่ง |ติดต่อผู้ส่ง     |รหัสไปรษณีย์ของผู้ส่ง|เก็บเงินปลายทาง|ที่อยู่ผู้รับ                                                |ที่อยู่ผู้รับ 2|รหัสไปรษณีย์|รายละเอียดพัสดุ
# +66614360114 | นางสาววันดี แพงภูงา (วันดี แพงภูงา)     |ตำบลคูคต อำเภอลำลูกกา ปทุมธานี 12130 |NVTHKMSHP000038514 |Pending Pickup|Ann   |+66650596397|12130          |189          |60ม.17 ตำบลท่าตะเกียบ อำเภอท่าตะเกียบ จังหวัดฉะเชิงเทรา 24160|        |24160    |แมสเด็ก XS ชาย (100)
# +66622510512 | คุณ (กิตตินันท์  ลับเนตร)               |ตำบลคูคต อำเภอลำลูกกา ปทุมธานี 12130 |NVTHKMSHP000038496 |Pending Pickup|Ann   |+66650596397|12130          |189          |82/1 หมู่ 1 ตำบลโป่งแดง อำเภอเมืองตาก จังหวัดตาก 63000      |        |63000    |แมสเด็ก S ชาย (100)
# +66622734026 | เอื้องฟ้า อินทีวงค์(Aeangfa Inteewong)  |ตำบลคูคต อำเภอลำลูกกา ปทุมธานี 12130 |NVTHKMSHP000038500 |Pending Pickup|Ann   |+66650596397|12130          |189          |5 หมู่7 บ้านปรางค์ ตำบลปัว อำเภอปัว จังหวัดน่าน 55120          |        |55120    |แมสเด็ก XS หญิง (50), แมสเด็ก XS ชาย (50)
FIELD_MAPS = {
    'รหัสติดตามสถานะพัสดุ': 'number',
    'ชื่อ': 'shippingname',
    'ติดต่อ': 'shippingphone',
    'ที่อยู่ผู้รับ': 'shippingaddress',
    'รหัสไปรษณีย์': 'shippingpostcode',
    'รายละเอียดพัสดุ': 'list__name',
}


class KimCorpOrderSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    shippingname = serializers.CharField(max_length=200)
    shippingaddress = serializers.CharField(allow_blank=True)
    shippingpostcode = serializers.CharField(allow_blank=True)
    list__name = serializers.CharField()
    shippingphone = serializers.CharField(max_length=50)


class KimCorpOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = KimCorpOrderSerializer


class KimCorpOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = KimCorpOrderSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['shippingchannel'] = 'Ninja Van'
        order['trackingno'] = order['number']
        return order

    def get_order_list(self, order: Dict):
        # แมสผู้ใหญ่ ขาว (50), แมสผู้ใหญ่ ดำ (50), แมสเด็ก S หญิง (50), แมสเด็ก S ชาย (50)
        list_str = order['list'][0]['name']

        items = []
        for item_str in list_str.split(','):
            name, number = item_str.split('(')
            number = number[:-1]

            items.append({
                'sku': name.strip(),
                'name': name.strip(),
                'number': Decimal(number.strip()),
                'pricepernumber': 0,
                'totalprice': 0
            })

        return items
