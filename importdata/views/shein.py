from email.policy import default
from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAP<PERSON>, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'เว็บไซต์': 'saleschannel',
    'เลขที่ใบสั่งซื้อ': 'number',
    # 'สถานะการสั่งซื้อ': 'status',
    # 'shippingphone': 'shippingphone',

    'หมายเลขใบตราส่งสินค้า': 'trackingno',
    # 'paymentamount': 'paymentamount',
    # 'discountamount': 'discountamount',
    # 'shippingaddress': 'shippingaddress',
    # 'shippingemail': 'shippingemail',
    # 'shippingchannel': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    'SKU ของผู้ขาย': 'list__sku',
    'เลขสินค้า': 'list__sku2',
    'ชื่อสินค้า': 'list__name',
    'ข้อมูลจำเพาะ': 'list__name2',
    'ราคาสินค้า': 'list__totalprice',
    # 'remark': 'remark',
}
TYPE = '030-shein'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    # shippingname = serializers.CharField(max_length=400)
    # shippingphone = serializers.CharField(max_length=400)
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    # shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    # shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__sku2 = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__name2 = serializers.CharField(allow_blank=True, default='')
    # list__number = BlankableDecimalField(
    #     max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'หมายเลขใบตราส่งสินค้า': str}


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'หมายเลขใบตราส่งสินค้า': str}

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['list'][0]['sku'] = row['list__sku'] or row['list__sku2']
        order['list'][0]['name'] = row['list__name'] + ' ' + row['list__name2']
        order['list'][0]['number'] = 1
        order['customername'] = '-'
        order['shippingname'] = '-'
        return order

    def post_process_orders(self, orders: Dict):
        for order in orders.values():
            order['amount'] = sum([item['totalprice'] for item in order['list']])
            order['paymentamount'] = order['amount']
        return orders
