import re
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

FIELD_MAPS = {
    "เลขที่ใบสั่งขาย": "number",
    "ชื่อ และ เบอร์โทรผู้รับสินค้า": "shippingname",
    "รหัสสินค้า": "list__sku",
    "ชื่อสินค้า": "list__name",
    "จำนวน": "list__number",
    "หมายเหตุ": "remark",
}
TYPE = "035-e-count"


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == "":
            data = self.default

        if type(data) is str:
            data = data.replace(",", "")

        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)

    # Optional
    list__sku = serializers.CharField(allow_blank=True, default="")
    list__name = serializers.CharField(allow_blank=True, default="")
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False
    )
    remark = serializers.CharField(required=False, allow_blank=True, default="")


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        phone_numbers = re.findall(
            r"\b(66[-.]?\d{3}[-.]?\d{3}[-.]?\d{4}|\d{3}[-.]?\d{3}[-.]?\d{4})\b",
            order["shippingname"],
        )
        if phone_numbers:
            order["shippingphone"] = phone_numbers[0]
        return order

    def merge_order(self, order, temp_order):
        remark = temp_order.get("remark", "")
        order["remark"] += remark
        return order
