import re

from rest_framework import serializers
from typing import Dict

from importdata.serializers import DateT<PERSON><PERSON><PERSON>, DecimalField
from .generics import OrderUploadG<PERSON>ic<PERSON><PERSON>, OrderImportGenericAPI


class JTExpressOrderSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    orderdateString = DateTimeField()
    # saleschannel = serializers.CharField()
    shippingname = serializers.CharField(max_length=200)

    shippingaddress = serializers.CharField(allow_blank=True)
    shippingsubdistrict = serializers.CharField(allow_blank=True)
    shippingdistrict = serializers.CharField(allow_blank=True)
    shippingprovince = serializers.CharField(allow_blank=True)
    shippingpostcode = serializers.CharField(allow_blank=True)
    list__name = serializers.CharField()

    shippingphone = serializers.CharField(max_length=50)
    shippingchannel = serializers.CharField(required=False, default='J&T Express')
    trackingno = serializers.Char<PERSON><PERSON>(required=True, allow_blank=True)
    description = serializers.Char<PERSON><PERSON>(allow_blank=True)
    # discountamount = DecimalField(max_digits=12, decimal_places=2)
    shippingamount = DecimalField(max_digits=12, decimal_places=2)
    amount = DecimalField(max_digits=12, decimal_places=2)


# เลขนำส่งพัสดุ	เลขที่รายการ วันที่ส่ง	ผู้รับ	โทรศัพท์ผู้รับ	จังหวัดปลายทาง	เขตปลายทาง	ตำบลผู้รับ	ที่อยู่ปลายทาง	รหัสไปรษณีย์ปลายทาง	ชื่อสินค้า	ค่าขนส่งทั้งหมด หมายเหตุ มูลค่าสินค้า
FIELD_MAPS = {
    'เลขที่รายการ': 'number',
    'วันที่ส่ง': 'orderdateString',
    'ผู้รับ': 'shippingname',

    'ที่อยู่ปลายทาง': 'shippingaddress',
    'ตำบลผู้รับ': 'shippingsubdistrict',
    'เขตปลายทาง': 'shippingdistrict',
    'จังหวัดปลายทาง': 'shippingprovince',
    'รหัสไปรษณีย์ปลายทาง': 'shippingpostcode',
    'ชื่อสินค้า': 'list__name',

    'โทรศัพท์ผู้รับ': 'shippingphone',
    'เลขนำส่งพัสดุ': 'trackingno',
    'หมายเหตุ': 'description',
    'ค่าขนส่งทั้งหมด': 'shippingamount',
    'มูลค่าสินค้า': 'amount'
}
IMPORT_TYPE = '004-jtexpress'


class JTExpressOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = JTExpressOrderSerializer


class JTExpressOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = JTExpressOrderSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['shippingchannel'] = 'J&T Express'
        order['number'] = order['trackingno']
        return order

    def get_order_list(self, order: Dict):
        # order['list'][0]['name'] = แมส 300 Pcs. (1), แมส 100 Pcs. (1)
        list_str = order['list'][0]['name']
        items = []
        for item_str in list_str.split(','):
            m = re.search(r'(.*)\s\(([0-9]+)\)', item_str)
            name = m.group(1)
            number = m.group(2)
            print('name:', name, 'number:', number)
            items.append({
                'sku': name,
                'name': name,
                'number': number,
                'pricepernumber': 0,
                'totalprice': 0
            })

        return items
