import re
from email.policy import default
from rest_framework import serializers
import sentry_sdk

from core.exceptions import PatternMismatchException
from .generics import OrderUploadGeneric<PERSON>I, OrderImportGenericAPI


FIELD_MAPS = {
    # 'saleschannel': 'saleschannel',
    'Order Number': 'number',
    'Facebook': 'shippingname',
    'โทร': 'shippingphone',

    'Tracking': 'trackingno',
    # 'paymentamount': 'paymentamount',
    # 'discountamount': 'discountamount',
    'ที่อยู่': 'shippingaddress',
    # 'shippingemail': 'shippingemail',
    # 'shippingchannel': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    # 'item_sku': 'list__sku',
    'รายการสินค้า': 'list__name',
    # 'item_amount': 'list__number',
    # 'item_totalprice': 'list__totalprice',
    # 'remark': 'remark',
}
TYPE = '009-realstore'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(
        max_length=400, required=False, default='Facebook')
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    paymentamount = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(
        max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(
        required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(
        required=False, allow_blank=True, default='')

    class Meta:
        ref_name = 'RealStoreOrderUploadSerializer'


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'Tracking': str, 'Order Number': str}


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'Tracking': str, 'Order Number': str}

    def row_to_order(self, row):
        order = super().row_to_order(row)

        # "
        # MK58 : พรีออเดอร์ มาร์คชีท สเต็มเซลล์แซลม่อน 1 แผ่น (7/6) | 10 | 660;
        # MK60 : พรีออเดอร์ LALARECIPE glow face moisture mask (7/6) | 2 | 178;
        # "
        order_str = order['list'][0]['name']
        pattern = r'(.+)\s*:\s*(.+)\s*\|\s*(\d+)\s*\|\s*(\d+);?'

        order_list = []
        for item in order_str.split('\n'):
            match_obj = re.match(pattern, item.strip())

            if match_obj:
                item_sku = match_obj.group(1)
                item_name = match_obj.group(2)
                item_amount = int(match_obj.group(3))
                item_totalprice = int(match_obj.group(4))

                order_list.append({
                    'sku': item_sku.strip(),
                    'name': item_name.strip(),
                    'number': item_amount,
                    'totalprice': item_totalprice,
                    'pricepernumber': round(item_totalprice / item_amount, 2)
                })
            else:
                raise PatternMismatchException(
                    f'009-realstore "รายการสินค้า" pattern mismatch: "{item}" does not match "{pattern}"'
                )

        order['list'] = order_list

        order['shippingamount'] = 60
        order['paymentamount'] = sum(x['totalprice']
                                     for x in order['list']) + 60
        order['amount'] = order['paymentamount']
        return order
