
import re

from rest_framework import serializers
from typing import Dict

from importdata.serializers import DateT<PERSON><PERSON><PERSON>, DecimalField
from .generics import OrderUpload<PERSON><PERSON>ic<PERSON><PERSON>, OrderImportGenericAPI


class NinjaVanOrderSerializer(serializers.Serializer):
    trackingno = serializers.CharField(max_length=50)
    # orderdateString = DateTimeField()
    # # saleschannel = serializers.CharField()
    shippingname = serializers.CharField(max_length=200)

    shippingaddress = serializers.CharField(allow_blank=True)
    # shippingsubdistrict = serializers.CharField(allow_blank=True)
    # shippingdistrict = serializers.CharField(allow_blank=True)
    # shippingprovince = serializers.CharField(allow_blank=True)
    # shippingpostcode = serializers.Char<PERSON>ield(allow_blank=True)
    # list__name = serializers.CharField()

    shippingphone = serializers.CharField(max_length=50)
    # shippingchannel = serializers.CharField(required=False, default='J&T Express')
    # trackingno = serializers.Cha<PERSON><PERSON><PERSON>(required=True, allow_blank=True)
    # description = serializers.Cha<PERSON><PERSON><PERSON>(allow_blank=True)
    # # discountamount = DecimalField(max_digits=12, decimal_places=2)
    # shippingamount = DecimalField(max_digits=12, decimal_places=2)
    # amount = DecimalField(max_digits=12, decimal_places=2)


# เลขนำส่งพัสดุ	เลขที่รายการ วันที่ส่ง	ผู้รับ	โทรศัพท์ผู้รับ	จังหวัดปลายทาง	เขตปลายทาง	ตำบลผู้รับ	ที่อยู่ปลายทาง	รหัสไปรษณีย์ปลายทาง	ชื่อสินค้า	ค่าขนส่งทั้งหมด หมายเหตุ มูลค่าสินค้า
# ติดต่อ ชื่อ รหัสติดตามสถานะพัสดุ ที่อยู่ผู้รับ
FIELD_MAPS = {
    'รหัสติดตามสถานะพัสดุ': 'trackingno',
    'ชื่อ': 'shippingname',

    'ที่อยู่ผู้รับ': 'shippingaddress',
    # 'ตำบลผู้รับ': 'shippingsubdistrict',
    # 'เขตปลายทาง': 'shippingdistrict',
    # 'จังหวัดปลายทาง': 'shippingprovince',
    # 'รหัสไปรษณีย์ปลายทาง': 'shippingpostcode',
    # 'ชื่อสินค้า': 'list__name',

    'ติดต่อ': 'shippingphone',
    # 'เลขนำส่งพัสดุ': 'trackingno',
    # 'หมายเหตุ': 'description',
    # 'ค่าขนส่งทั้งหมด': 'shippingamount',
    # 'มูลค่าสินค้า': 'amount'
}
IMPORT_TYPE = '005-ninjavan'


class NinjaVanOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = NinjaVanOrderSerializer


class NinjaVanOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = NinjaVanOrderSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['shippingchannel'] = 'Ninja Van'
        order['number'] = order['trackingno']

        try:
            order['shippingname'] = order['shippingname'].split('*')[-1].strip() or 'ไม่ระบุ'
        except Exception:
            pass

        return order
