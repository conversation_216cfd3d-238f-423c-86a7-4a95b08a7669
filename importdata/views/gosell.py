from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

#
# Order Number
# Tracking Number
# Payment Type
# Customer Name
# Customer Address
# Customer District: shippingdistrict
# Customer Sub District: shippingsubdistrict
# Customer Province: shippingprovince
# Customer Zipcode: shippingpostcode
# Tel
# Created Date
# Shipping Status
# Order Status
# SKU
# Item Name
# Price
# Quantity
# Weight
# Discount
# Total
# Courier
# Shop Name
# Create By

FIELD_MAPS = {
    'Shop Name': 'saleschannel',
    'Order Number': 'number',
    'Customer Name': 'shippingname',
    'Tel': 'shippingphone',

    'Tracking Number': 'trackingno',
    'Customer Address': 'shippingaddress',
    'Customer District': 'shippingdistrict',
    'Customer Sub District': 'shippingsubdistrict',
    'Customer Province': 'shippingprovince',
    'Customer Zipcode': 'shippingpostcode',
    'Courier': 'shippingchannel',
    'Order Status': 'status',
    'SKU': 'list__sku',
    'Item Name': 'list__name',
    'Quantity': 'list__number',
    'Total': 'list__totalprice',
    'Discount': 'list__discountamount'
}
STATUS_MAPS = {
    'Processing': 'Pending',
    'Complete': 'Success',
    'Cancel': 'Voided'
}
TYPE = '010-gosell'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default

        if type(data) is str:
            data = data.replace(',', '')

        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    status = serializers.CharField(required=False, allow_blank=True, default='')
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    shippingsubdistrict = serializers.CharField(
        default=None, allow_blank=True, allow_null=True)
    shippingdistrict = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    shippingprovince = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    # def row_to_order(self, row):
    #     order = super().row_to_order(row)
    #     # order['paymentamount'] = sum()
    #     order['amount'] = order['paymentamount']
    #     return order

    def post_process_orders(self, orders: Dict):
        results = {}
        for key, order in orders.items():
            order['status'] = STATUS_MAPS.get(order['status'], 'Pending')
            if order['status'] == 'Voided':
                continue

            order['paymentamount'] = sum(item['totalprice'] for item in order['list'])
            order['amount'] = order['paymentamount']
            order['discountamount'] = sum(item['discountamount'] for item in order['list'])
            order['shippingaddress'] = f"{order['shippingaddress']}, {order['shippingsubdistrict']}, {order['shippingdistrict']}, {order['shippingprovince']} {order['shippingpostcode']}"
            results[key] = order

        return results
