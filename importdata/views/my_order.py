from email.policy import default
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# No., Order No., ช่องทาง/เพจ, วันที่สั่งซื้อ, ชื่อลูกค้า, เบอร์โทร, ที่อยู่, หมายเหตุ, รหัสสินค้า (จำนวนชิ้น), สินค้า (จำนวนชิ้น), น้ำหนัก (กก.), TRACKING NO., สถานะพัสดุ, ส่วนลด(บาท), ค่าจัดส่ง(บาท), ยอดเงิน(บาท), วิธีการชำระเงิน, วันที่ชำระเงิน, การชำระเงิน, สร้างออเดอร์โดย, ยืนยันการชำระเงินโดย, เพิ่มยอดขายล่าสุดโดย"

FIELD_MAPS = {
    'ช่องทาง/เพจ': 'saleschannel',
    'Order No.': 'number',
    'ชื่อลูกค้า': 'shippingname',
    'เบอร์โทร': 'shippingphone',

    'TRACKING NO.': 'trackingno',
    'ยอดเงิน(บาท)': 'paymentamount',
    'ส่วนลด(บาท)': 'discountamount',
    'ที่อยู่': 'shippingaddress',
    # 'shippingchannel': 'shippingchannel', split string จาก TRACKING NO.
    'ค่าจัดส่ง(บาท)': 'shippingamount',
    'รหัสสินค้า (จำนวนชิ้น)': 'list__sku',
    'สินค้า (จำนวนชิ้น)': 'list__name',
    # 'item_amount': 'list__number',
    # 'item_totalprice': 'list__totalprice',
    'หมายเหตุ': 'remark',
}
TYPE = '012-my-order'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default

        if data == '-':
            data = self.default

        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['amount'] = order['paymentamount']

        if '(' in order['trackingno']:
            tracking_no, shipping_channel = order['trackingno'].split('(')
        else:
            tracking_no = ''
            shipping_channel = '-'

        order['trackingno'] = tracking_no.strip()
        order['shippingchannel'] = shipping_channel[:-1]
        order['saleschannel'] = order['saleschannel'][:50]

        if order['remark'] == '-':
            order['remark'] = ''

        order_list = []

        # Example string "fong04 (1), fong03 (1), fong01 (3)"
        item_skus = order['list'][0]['sku'].split(',')

        # Example string "ซองชมพู รสลาบ>> (1), ซองน้ำเงิน รสปูผัดผงกะหรี่>> (1), ซองเขียว รสออริจินอล>> (3)"
        item_names = order['list'][0]['name'].split(',')

        if len(item_skus) < len(item_names):
            item_skus = ['-'] * len(item_names)

        for item_sku, item_name in zip(item_skus, item_names):
            if ' (' in item_name:
                name, amount = item_name.rsplit(' (', 1)
                name = name.strip()
                amount = amount[:-1]
            else:
                name = '-'
                amount = 0

            if ' (' in item_sku:
                sku = item_sku.split(' (')[0]
                sku = sku.strip()
            else:
                sku = '-'

            order_list.append({
                'sku': sku,
                'name': name,
                'number': amount,
                'unittext': '',
                'totalprice': 0,
                'pricepernumber': 0,
                'discountamount': 0
            })

        order['list'] = order_list
        return order
