
from decimal import Decimal
from email.policy import default
import re
from typing import Dict
from rest_framework import serializers
from importdata.serializers import DecimalField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# ออเดอร์	รายการสินค้า	                                                           	ค่าขนส่ง	มูลค่ารวม	Tracking	           ที่อยู่	ตำบล	อำเภอ	จังหวัด	รหัสไปรษณีย์	โทร
# 004706	"d211XS : ด.น้ำเงินคอปกแขนยาวปักหมีXS | 1 | 990 d256xs : d256xs | 1 | 650"	       49	     1689	 CFMG0173423274 	ทวาพร บางทราย 182/24 คอนโดลุมพินีวิลล์ พระนั่งเกล้า ต.ไทรม้า อ.เมืองนนทบุรี จ.นนทบุรี 11000 0804589164(ฝากไว้ที่นิติเลยคะ)	ไทรม้า	เมืองนนทบุรี	นนทบุรี	11000	โทร. 0804589164
FIELD_MAPS = {
    'ออเดอร์': 'number',
    'Facebook': 'shippingname',
    'ที่อยู่': 'shippingaddress',
    'ตำบล': 'shippingsubdistrict',
    'อำเภอ': 'shippingdistrict',
    'จังหวัด': 'shippingprovince',
    'รหัสไปรษณีย์': 'shippingpostcode',
    'รายการสินค้า': 'list__name',

    'โทร': 'shippingphone',
    'Tracking': 'trackingno',
    'ค่าขนส่ง': 'shippingamount',
    'มูลค่ารวม': 'paymentamount',
}
IMPORT_TYPE = '003-eight-studio-brand'


class EightStudioBrandOrderSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    # orderdateString = DateTimeField(input_formats=['%b %d, %Y, %I:%M %p'])
    # saleschannel = serializers.CharField()
    shippingname = serializers.CharField(max_length=200)

    shippingaddress = serializers.CharField(allow_blank=True)
    shippingsubdistrict = serializers.CharField(allow_blank=True)
    shippingdistrict = serializers.CharField(allow_blank=True)
    shippingprovince = serializers.CharField(allow_blank=True)
    shippingpostcode = serializers.CharField(allow_blank=True)
    list__name = serializers.CharField()

    shippingphone = serializers.CharField(max_length=50)
    # shippingchannel = serializers.CharField()
    trackingno = serializers.CharField(required=True, allow_blank=True)
    # description = serializers.CharField(allow_blank=True)
    paymentamount = DecimalField(max_digits=12, decimal_places=2, default=0)
    voucheramount = DecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = DecimalField(max_digits=12, decimal_places=2, default=0)
    shippingamount = DecimalField(max_digits=12, decimal_places=2)
    # amount = DecimalField(max_digits=12, decimal_places=2)

    # def validate(self, attrs):
    #     attrs = super().validate(attrs)
    #     attrs['paymentamount'] = attrs['amount']
    #     return attrs


class EightStudioBrandOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = EightStudioBrandOrderSerializer


class EightStudioBrandOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = EightStudioBrandOrderSerializer

    def get_order_address(self, order: Dict):
        return order['shippingaddress']
        #     order['shippingaddress'],
        #     'แขวง/ตำบล', order['shippingsubdistrict'],
        #     'เขต/อำเภอ', order['shippingdistrict'],
        #     'จังหวัด', order['shippingprovince'],
        #     'รหัสไปรษณีย์่', order['shippingpostcode']
        # ])

    def get_order_list(self, order: Dict):
        list_str = order['list'][0]['name']

        items = []
        for item_str in list_str.split('\n'):
            sku, item_str = item_str.split(':')
            name, number, totalprice = item_str.split('|')

            items.append({
                'sku': sku.strip(),
                'name': name.strip(),
                'number': Decimal(number.strip()),
                'pricepernumber': Decimal(totalprice.strip()) / Decimal(number.strip()),
                'totalprice': Decimal(totalprice.strip())
            })

        return items
