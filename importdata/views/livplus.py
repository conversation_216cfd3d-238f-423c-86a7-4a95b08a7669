from email.policy import default
from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAP<PERSON>, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    # 'saleschannel': 'saleschannel',
    'รหัสใบสั่งซื้อ': 'number',
    'ชื่อ-นามสกุล': 'shippingname',
    'เบอร์โทร': 'shippingphone',

    'Tracking No.': 'trackingno',
    # 'paymentamount': 'paymentamount',
    # 'discountamount': 'discountamount',
    'Address #1': 'shippingaddress',
    'Address #2': 'shippingprovince',
    'Zip Code': 'shippingpostcode',
    # 'shippingemail': 'shippingemail',
    'วิธีส่งสินค้า': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    'รหัสสินค้า(Product Code)': 'list__sku',
    'ชื่อสินค้า(Product Name)': 'list__name',
    'จำนวนสินค้า': 'list__number',
    'ยอด (บาท)': 'list__totalprice',
    # 'remark': 'remark',
}
TYPE = '027-livplus'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    # saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)

    # Optional
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingprovince = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(required=False, allow_blank=True, default='')
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    # shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        row['shippingaddress'] = f"{row['shippingaddress']} {row['shippingprovince']} {row['shippingpostcode']}"
        row['shippingprovince'] = ''
        row['shippingpostcode'] = ''

        order = super().row_to_order(row)
        return order

    def post_process_orders(self, orders: Dict):
        for order in orders.values():
            order['amount'] = sum([item['totalprice'] for item in order['list']])
            order['paymentamount'] = order['amount']
        return orders
