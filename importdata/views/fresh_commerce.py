from typing import Dict
from rest_framework import serializers

from importdata.serializers import DateTimeField
from picking.serializers.zort import DateStringField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# order_number, cart_created_at (UTC+7), customer_name, All Address,
# mobile_number, delivery_partner, seller_name,
# D_product_name, D_price, D_quantity
FIELD_MAPS = {
    'order_number': 'number',
    'cart_created_at (UTC+7)': 'orderdateString',  # 4/8/2022 13:42
    'customer_name': 'shippingname',
    'All Address': 'shippingaddress',
    'mobile_number': 'shippingphone',
    'delivery_partner': 'shippingchannel',
    'seller_name': 'saleschannel',
    'sku': 'list__sku',
    'D_product_name': 'list__name',
    'D_quantity': 'list__number',
    'D_price': 'list__pricepernumber',

    # 'trackingno': 'trackingno',
    # 'paymentamount': 'paymentamount',
    # 'discountamount': 'discountamount',
    # 'shippingemail': 'shippingemail',
    # 'shippingchannel': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    # 'item_sku': 'list__sku',
    # 'remark': 'remark',
}
TYPE = '008-freshcommerce'


class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField(max_length=100)
    orderdateString = serializers.DateTimeField(
        input_formats=['%-d/%-m/%Y %-I:%M:%S %p', '%d/%m/%Y %H:%M', '%Y-%m-%d %H:%M', '%d-%m-%y %H:%M'])
    shippingname = serializers.CharField(max_length=400)
    saleschannel = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingchannel = serializers.CharField(required=False, allow_blank=True)

    list__name = serializers.CharField()
    list__sku = serializers.CharField()
    list__number = serializers.FloatField()
    list__pricepernumber = serializers.FloatField()

    class Meta:
        ref_name = 'FreshCommerceOrderUploadSerializer'

    # trackingno = serializers.CharField(required=False, allow_blank=True)
    # shippingamount = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
    # item_sku = serializers.CharField(allow_blank=True, default='')
    # item_name = serializers.CharField(allow_blank=True, default='')
    # item_amount = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
    # item_totalprice = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['trackingno'] = ''
        return order

    def post_process_orders(self, orders: Dict):
        for order in orders.values():
            order['amount'] = sum(i['totalprice'] for i in order['list'])
            order['paymentamount'] = order['amount']
        return orders
