from rest_framework import serializers

from core.serializers.blankable import BlankableBooleanField
from utils.ignore_exception import try_int
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# | Dobybot Field             | Column | Excel Field            |
# |---------------------------|--------|------------------------|
# | เลขออเดอร์                 | p      | หมายเลขคำสั่งซื้อภายใน      |
# | ช่องทางการขาย              | r      | ชื่อร้านค้า                 |
# | ชื่อขนส่ง                    | h      | บริษัทขนส่ง                |
# | หมายเลขพัสดุ                | i      | หมายเลขพัสดุ              |
# | ชื่อลูกค้า                    | ac     | ผู้รับ                     |
# | ที่อยู่                       | ab     | ที่อยู่ผู้รับ                  |
# | เบอร์โทร                   | aw     | หมายเลขโทรศัพท์ผู้รับ        |
# | sku                       | g      | ข้อมูลสินค้า                |
# | จำนวนสินค้ารวม              | f      | จำนวนสินค้าที่ขายได้         |
# | cod                       | ad     | ชำระเงินปลายทาง          |
# | วันที่สร้างออเดอร์             | ag     | วันที่สร้าง                 |
FIELD_MAPS = {
    "หมายเลขคำสั่งซื้อภายใน": "number",
    "ชื่อร้านค้า": "saleschannel",
    "บริษัทขนส่ง": "shippingchannel",
    "หมายเลขพัสดุ": "trackingno",
    "ผู้รับ": "shippingname",
    "ที่อยู่ผู้รับ": "shippingaddress",
    "หมายเลขโทรศัพท์ผู้รับ": "shippingphone",
    "ข้อมูลสินค้า": "list__sku",
    "ชำระเงินปลายทาง": "cod",
    "วันที่สั่งซื้อ": "orderdateString",
}

TYPE = "023-jst-erp-6"


class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField(max_length=100)
    saleschannel = serializers.CharField(max_length=400)
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)

    shippingaddress = serializers.CharField(
        required=False, allow_blank=True, default=""
    )
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    list__sku = serializers.CharField(allow_blank=True, default="")
    cod = BlankableBooleanField(required=False, allow_blank=True, default=False)
    orderdateString = serializers.DateTimeField(input_formats=["Y/%m/%d %H:%M:%S"])

    def validate_list__sku(self, value):
        ## Value
        # 1.BW03*1
        item_str_arr = value.split(",")
        for item in item_str_arr:
            item_info_list = item.split("*")
            if len(item_info_list) < 2:
                raise serializers.ValidationError("Wrong Format")
        return value


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {"หมายเลขคำสั่งซื้อภายใน": try_int}


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {"หมายเลขคำสั่งซื้อภายใน": try_int}

    def row_to_order(self, row):
        order = super().row_to_order(row)

        ## Value
        # 1.BW03*1
        item_str = order["list"][0]["sku"]
        item_str_arr = item_str.split(",")
        item_list = []
        for item in item_str_arr:
            item: str
            total_number, parts = item.split(".", 1)
            sku, number2 = parts.split("*")
            item_list.append(
                {
                    "sku": sku,
                    "name": sku,
                    "number": number2,
                    "unittext": "",
                    "totalprice": 0,
                    "pricepernumber": 0,
                    "discountamount": 0,
                }
            )

        order["list"] = item_list

        return order
