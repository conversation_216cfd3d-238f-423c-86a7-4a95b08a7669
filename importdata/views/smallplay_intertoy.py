
from rest_framework import serializers
from importdata.serializers import DateTimeField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI
import decimal

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'ออเดอร์': 'number',
    'รายการสินค้า': 'list__name',
    'จำนวน': 'list__number',
    'ราคาต่อหน่อย': 'list__pricepernumber',
    'ราคารวม': 'list__totalprice',
    'Tracking': 'trackingno',
    'ที่อยู่': 'shippingaddress',
    'จังหวัด': 'shippingprovince',
    'อำเภอ': 'shippingdistrict',
    'ตำบล': 'shippingsubdistrict',
    'รหัสไปรษณีย์': 'shippingpostcode',
    'Facebook': 'shippingname', 
    'วันที่สร้าง': 'orderdateString',
    'โทร': 'phone_number',
    'ขนส่ง': 'shippingchannel',
    'ค่าส่ง': 'shippingamount',
    'ราคาสุทธิ': 'paymentamount',
}
TYPE = '038-smallplay-intertoy'

def extract_code(input_string):
    # Split the string by colon and return the first part (trim spaces)
    return input_string.split(":")[0].strip()

def extract_phone_number(input_string):
    # Remove "โทร. " and return the rest of the string
    return input_string.replace("โทร. ", "").strip()

def format_order_number(order_number):
    return str(order_number).zfill(6)


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        
        try:
            value = decimal.Decimal(data)
            data = value.quantize(decimal.Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)
        except decimal.DecimalException:
            self.fail('invalid')

        return super().to_internal_value(data)

class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField()

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingprovince = serializers.CharField(required=False, allow_blank=True, default='')
    shippingdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingsubdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(required=False, allow_blank=True, default='')
    shippingname = serializers.CharField(max_length=400)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    phone_number = serializers.CharField(required=False, allow_blank=True, default='')
    trackingno = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__pricepernumber = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__number = BlankableDecimalField( max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    orderdateString = DateTimeField(input_formats=['%Y-%m-%d %H:%M:%S'])

class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'shippingpostcode': str, 'number': str}
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'shippingpostcode': str, 'number': str}
    serializer_class = OrderUploadSerializer
    
    def row_to_order(self, row):
        order = super().row_to_order(row)
        for item in  order["list"]:
            item["sku"] = extract_code(item["name"])
        
        order["shippingphone"] = extract_phone_number(row["phone_number"])
        order["number"] = format_order_number(row["number"]) 

        if row["paymentamount"]:
            order['paymentamount'] = row['paymentamount']
            order['shippingamount'] = row['shippingamount']

        return order

    def merge_order(self, order, temp_order):
        order['paymentamount'] = temp_order['paymentamount']
        order['shippingamount'] = temp_order['shippingamount']
        return order


