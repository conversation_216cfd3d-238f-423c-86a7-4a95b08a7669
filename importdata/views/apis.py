from dynamic_rest.viewsets import DynamicModelViewSet
from rest_framework.response import Response
from rest_framework.views import APIView

from companies.models.models import Company
from core.permissions import ModelPermissions
from importdata.models import OrderImportRequest
from importdata.serializers import OrderImportRequestSerializer


class OrderImportRequestViewSet(DynamicModelViewSet):
    serializer_class = OrderImportRequestSerializer
    permission_classes = [ModelPermissions]

    def get_queryset(self, queryset=None):
        company_id = self.request.user.company_id
        return OrderImportRequest.objects.filter(company_id=company_id)


class ImportTypeListAPIView(APIView):
    def get(self, request):
        import_types = {
            "001-simple": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-bucket-1/importdata/001.xlsx",
                "company": "all",
            },
            "001-easy-etax": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/001-easy-etax-v2.xlsx",
                "company": "all",
                "feature_flag": Company.ETAX,
            },
            "001-full": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/001-full-v2.xlsx",
                "company": "all",
            },
            "001-full-etax": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/001-full-v4.xlsx",
                "company": "all",
                "feature_flag": Company.ETAX,
            },
            "002-behind": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-bucket-1/importdata/002-behind.xlsx",
                "company": "all",
            },
            "003-eight-studio-brand": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-bucket-1/importdata/003-eight-studio-brand.xlsx",
                "company": ["Uat", "test", "8 Studio Brand"],
            },
            "004-jtexpress": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/004-jt-express.xlsx",
                "company": "all",
            },
            "005-ninjavan": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/005-ninjavan.xlsx",
                "company": "all",
            },
            "006-quickerbox": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "Quickerbox"],
            },
            "007-kimcorp": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "Kim Corporation"],
            },
            "008-freshcommerce": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "Freshcommerce"],
            },
            "009-realstore": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "REAL STORE", "EXCEL_IMPORT"],
            },
            "010-gosell": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/010-gosell.xlsx",
                "company": "all",
            },
            "011-xcommerce": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/011-xcommerce.xls",
                "company": "all",
            },
            "012-my-order": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/012-my-order-example.xlsx",
                "company": "all",
            },
            "013-shipnity": {
                "help": 'ใช้ "เลขที่ออเดอร์" เป็นหมายเลข Order Number',
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/013-shipnity.xlsx",
                "company": "all",
            },
            "013-shipnity-2": {
                "help": 'ใช้ "ID บน Marketplace" (ถ้ามี) เป็นหมายเลข Order Number',
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/013-shipnity.xlsx",
                "company": "all",
            },
            "013-shipnity-3": {
                "help": 'ใช้ "เลขที่ออเดอร์ (ออเดอร์)" เป็นหมายเลข Order Number, แยกสินค้าเป็นแถว',
                "example": "",
                "company": "all",
            },
            "013-shipnity-4": {
                "help": 'ใช้ "เลขที่ออเดอร์ (ออเดอร์)" เป็นหมายเลข Order Number, แยกสินค้าเป็นแถว',
                "example": "",
                "company": "all",
            },
            "014-morning-dworld": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/014-morning-dworld.xlsx",
                "company": [
                    "Uat",
                    "test",
                    "MORNING DWORLDS",
                    "พี่ทรายไลฟ์สด - Integrate System",
                ],
            },
            "015-flash": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/015-flash.xlsx",
                "company": "all",
            },
            "016-ship-offline-xcommerce": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "PAWDY INNOVATION", "HEALTHY BESTFRIEND"],
            },
            "017-ship-offline-gosell": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "PAWDY INNOVATION", "HEALTHY BESTFRIEND"],
            },
            "018-ship-offline-gosell-2": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "PAWDY INNOVATION", "HEALTHY BESTFRIEND"],
            },
            "019-zwizshop": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/019-zwizshop.xlsx",
                "company": "all",
            },
            "020-page365": {
                "help": "",
                "example": "https://storage.googleapis.com/dobybot-public-bucket/example/importdata/020-page365.xlsx",
                "company": "all",
            },
            "021-commerzy": {"help": "", "example": "", "company": "all"},
            "021-commerzy-2": {"help": "", "example": "", "company": "all"},
            "022-nocnoc": {"help": "", "example": "", "company": "all"},
            "023-jst-erp": {"help": "", "example": "", "company": "all"},
            "023-jst-erp-2": {"help": "", "example": "", "company": "all"},
            "023-jst-erp-3": {"help": "", "example": "", "company": "all"},
            "023-jst-erp-4": {"help": "", "example": "", "company": "all"},
            "023-jst-erp-5": {"help": "", "example": "", "company": "all"},
            "023-jst-erp-6": {"help": "", "example": "", "company": "all"},
            "024-wang-nam-khiao": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "WANGNAMKHIAO FARM INTER"],
            },
            "025-gosell-2": {"help": "", "example": "", "company": "all"},
            "026-shippop": {"help": "", "example": "", "company": "all"},
            "027-livplus": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "LIVPLUS HEALTH SOLUTION", "Shop Laew Ruay"],
            },
            "028-ddg-jewelry": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "DDG JEWELRY"],
            },
            "029-ddg-jewelry-2": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "DDG JEWELRY"],
            },
            "030-shein": {"help": "", "example": "", "company": "all"},
            "031-make-web-easy": {"help": "", "example": "", "company": "all"},
            "032-shopify": {"help": "", "example": "", "company": "all"},
            "033-srichan-amado": {
                "help": "",
                "example": "",
                "company": ["Uat", "test", "Srichan"],
            },
            "034-pancake": {"help": "", "example": "", "company": "all"},
            "035-e-count": {"help": "", "example": "", "company": "all"},
            "036-somphop-liquor-house": {
                "help": "",
                "example": "",
                "company": ["UAT", "test", "SOMPHOP LIQUOR HOUSE COMPANY LIMITED"],
            },
            "037-jambolive": {
                "help": "",
                "example": "",
                "company": "all",
            },
            "037-jambolive-2": {
                "help": "",
                "example": "",
                "company": "all",
            },
            "038-smallplay-intertoy": {
                "help": "",
                "example": "",
                "company": ["UAT", "test", "Smallplay Intertoy"],
            },
            "039-wellnesssys": {
                "help": "",
                "example": "",
                "company": ["UAT", "test", "WELLNESSSYS"],
            },
            "040-rocketstar": {
                "help": "",
                "example": "",
                "company": ["UAT", "test", "Rockgetstar"],
            },
            "041-flattwhite": {
                "help": "",
                "example": "",
                "company": ["UAT", "test", "Flattwhite"],
            },
            "042-etax-charoensin": {
                "help": "",
                "example": "",
                "company": ["UAT", "test", "Charoensin Kanchanaburi Trading"],
            },
            "043-duangtawanpech": {
                "help": "",
                "example": "",
                "company": ["UAT", "test", "ดวงตะวันเพชร"],
            },
        }

        company: Company = request.user.company
        result = {}
        for k, v in import_types.items():
            if not (v["company"] == "all" or company.name in v["company"]):
                continue

            if "feature_flag" in v and not company.feature_flag.get(v["feature_flag"]):
                continue

            result[k] = v

        return Response(result)
