from rest_framework import serializers

from importdata.serializers import DateTimeField
from core.serializers.blankable import BlankableBooleanField
from importdata.views.commerzy import BlankableDecimalField
from utils.ignore_exception import try_int
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# | Dobybot Field             | Column | Excel Field            |
# |---------------------------|--------|------------------------|
    # ออเดอร์ 
# | วันเวลาที่สร้างออเดอร์         | A      | สร้างเมื่อ (Created Order at) |
# | เลขออเดอร์                 | B      | เลขที่การสั่งซื้อ (Order No.) |
# | ช่องทางการขาย              | AF     | ช่องทางการสั่งซื้อ (Channel) |
# | ชื่อขนส่ง                    | AC     | ช่องทางการจัดส่ง(Shipping) |
# | ชื่อลูกค้า                    | V      | ชื่อผู้รับ (Customer Name)   |
# | เบอร์โทร                   | AB     | เบอร์โทรศัพท์ (Phone)      |
# | ที่อยู่                       | U      | ชื่อ-ที่อยู่ผู้รับ (Address)     |
# | แขวง/ตำบล                 | X      | แขวง/ตำบล (Subdistrict) |
# | เขต/อำเภอ                 | Y      | เขต/อำเภอ (District)    |
# | จังหวัด                     | Z      | จังหวัด (Province)        |
# | รหัสไปรษณีย์                 | AA     | รหัสไปรษณีย์ (Postcode)    |

    # สินค้า
# | ชื่อสินค้า                    | I      | ชื่อสินค้า (Product Name)   |
# | sku                       | K      | รหัสสินค้า (SKU)           |
# | จำนวนสินค้า                 | L      | จำนวน (Quantity)        |
# | ราคาสินค้ารวม               | M      | ราคาสินค้า (Product Price)|
# | ยอดรวมทั้งหมด               | R      | ยอดรวมทั้งสิ้น (Total)      |
FIELD_MAPS = {
    "สร้างเมื่อ (Created Order at)": "createdatetimeString",
    "เลขที่การสั่งซื้อ (Order No.)": "number",
    "ช่องทางการสั่งซื้อ (Channel)": "saleschannel",
    "ช่องทางการจัดส่ง(Shipping)": "shippingchannel",
    "ชื่อผู้รับ (Customer Name)": "shippingname",
    "ชื่อ-ที่อยู่ผู้รับ (Address)": "shippingaddress",
    "เบอร์โทรศัพท์ (Phone)": "shippingphone",
    "แขวง/ตำบล (Subdistrict)": "shippingsubdistrict",
    "เขต/อำเภอ (District)": "shippingdistrict",
    "จังหวัด (Province)": "shippingprovince",
    "รหัสไปรษณีย์ (Postcode)": "shippingpostcode",
    "ยอดรวมทั้งสิ้น (Total)": "amount",

    "ชื่อสินค้า (Product Name)": "list__name",
    "รหัสสินค้า (SKU)": "list__sku",
    "จำนวน (Quantity)": "list__number",
    "ราคาสินค้า (Product Price)": "list__totalprice",
}

TYPE = "043-duangtawanpech"


class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    saleschannel = serializers.CharField(max_length=400)
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    shippingphone = serializers.CharField(max_length=400)
    amount = serializers.DecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default="")
    shippingsubdistrict = serializers.CharField(required=False, allow_blank=True, default="")
    shippingdistrict = serializers.CharField(required=False, allow_blank=True, default="")
    shippingpostcode = serializers.CharField(required=False, allow_blank=True, default="")
    shippingprovince = serializers.CharField(required=False, allow_blank=True, default="")
    createdatetimeString = serializers.DateTimeField(input_formats=['%d/%m/%Y  %H:%M', '%d/%m/%Y  %H:%M:%S'])

    list__name = serializers.CharField(allow_blank=True, default="")
    list__sku = serializers.CharField(allow_blank=True, default="")
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    copy_upper_row_data = ['number', 'shippingname', 'saleschannel', 'shippingchannel', 'shippingphone', 'amount', 'createdatetimeString']
    converters = { 'เลขที่การสั่งซื้อ (Order No.)': str, 'เบอร์โทรศัพท์ (Phone)': str, 'รหัสไปรษณีย์ (Postcode)': str } 
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    copy_upper_row_data = ['number', 'shippingname', 'saleschannel', 'shippingchannel', 'shippingphone', 'amount', 'createdatetimeString']
    converters = { 'เลขที่การสั่งซื้อ (Order No.)': str, 'เบอร์โทรศัพท์ (Phone)': str, 'รหัสไปรษณีย์ (Postcode)': str } 
    serializer_class = OrderUploadSerializer
    
    def post_process_orders(self, orders):
        for order in orders.values():
            new_items = []

            for item in order['list']:
                should_remove = item['totalprice'] == order["amount"] and "," in item['sku']
                
                if should_remove:
                    continue
                else:
                    new_items.append(item)

            order['list'] = new_items 
        return orders
                    





