
import re

from rest_framework import serializers
from typing import Dict

from importdata.serializers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>imal<PERSON>ield
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


class QuickBoxOrderSerializer(serializers.Serializer):
    trackingno = serializers.CharField(max_length=50)
    status = serializers.CharField()
    shippingname = serializers.CharField(max_length=200)
    shippingaddress = serializers.CharField(allow_blank=True)
    shippingphone = serializers.Char<PERSON>ield(max_length=50)
    orderdateString = DateTimeField()
    list__name = serializers.CharField(allow_blank=True)

    def validate_status(self, status):
        mapping = {
            'Pending': 'Pending',
            'Transit': 'Shipping'
        }
        return mapping.get(status, 'Pending')


# Tracking Id
# Status
# Name
# Address
# Order Creation Date
# Receiver / Buyer's Phone no.
# Delivery Instructions
FIELD_MAPS = {
    'Tracking Id ': 'trackingno',
    'Status': 'status',
    'Name': 'shippingname',
    'Address': 'shippingaddress',
    "Receiver / Buyer's Phone no.": 'shippingphone',
    'Order Creation Date': 'orderdateString',
    'Delivery Instructions': 'list__name'
}
IMPORT_TYPE = '006-quickerbox'


class QuickerBoxOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = QuickBoxOrderSerializer


class QuickerBoxOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = QuickBoxOrderSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['shippingchannel'] = 'Ninja Van'
        order['number'] = order['trackingno']
        return order

    def get_order_list(self, order: Dict):
        # Exampple list_str
        # 00 ไม่พิมพ์=200 ใบ กล่องผลไม้M=10ใบ กล่องผลไม้M+=10ใบ กล่องผลไม้L=10ใบ
        list_str = order['list'][0]['name']
        return [{
            'sku': '-',
            'name': list_str or '-',
            'number': 0,
            'pricepernumber': 0,
            'totalprice': 0
        }]
