from email.policy import default
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI
import re
# วันที่สร้าง	เลขที่ออเดอร์	Marketplace ของออเดอร์	ID บน Marketplace	เลขพัสดุ	รายการขาย	ชื่อ (ลูกค้า)	ที่อยู่	เบอร์โทร

FIELD_MAPS = {
    'Marketplace ของออเดอร์': 'saleschannel',
    'ID บน Marketplace': 'number',
    'ชื่อ (ลูกค้า)': 'shippingname',
    'เบอร์โทร': 'shippingphone',
    'วันที่สร้าง': 'orderdateString',
    'เลขที่ออเดอร์': 'order_id',

    'เลขพัสดุ': 'trackingno',
    # 'paymentamount': 'paymentamount',
    # 'discountamount': 'discountamount',
    'ที่อยู่': 'shippingaddress',
    # 'shippingemail': 'shippingemail',
    # 'shippingchannel': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    # 'item_sku': 'list__sku',
    'รายการขาย': 'list__name',
    # 'item_amount': 'list__number',
    # 'item_totalprice': 'list__totalprice',
    # 'เลขที่ออเดอร์': 'remark',
}
TYPE = '013-shipnity'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class ShipnityOrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400, allow_blank=True)
    orderdateString = serializers.DateTimeField(
        input_formats=['%d/%m/%Y %H:%M'])
    order_id = serializers.CharField(max_length=100)
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    # shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    # shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(
        required=False, allow_blank=True, default='')
    # list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    # list__number = BlankableDecimalField(
    #     max_digits=12, decimal_places=2, default=1, required=False)
    # list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = ShipnityOrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = ShipnityOrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        items = []
        items_str = order['list'][0]['name']

        # if order['number'] == '-' or not order['number']:
        order['number'] = row['order_id']

        for item_str in items_str.split('|'):
            # pattern = r'(?P<item_name>.+?)\s\((?P<item_sku>.+?)\)\s=\s(?P<item_amount>\d+)'
            pattern1 = r'(?P<item_name>.+?)\s?(?:\((?P<item_sku>.+?)\))?\s=\s(?P<item_amount>\d+)'
            match = re.search(pattern1, item_str.strip())

            if match:
                item_name = match.group('item_name')
                item_sku = match.group('item_sku') or item_name
                item_amount = match.group('item_amount')

                items.append({
                    'sku': item_sku,
                    'name': item_name,
                    'number': item_amount,
                    'unittext': '',
                    'totalprice': 0,
                    'pricepernumber': 0,
                    'discountamount': 0
                })
            else:
                print('No match found.', item_str.strip())

        order['list'] = items
        return order
