from email.policy import default
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


FIELD_MAPS = {
    'Date & Time': 'orderdateString',
    'Channel': 'saleschannel',
    'Order Number': 'number',
    'Real Name': 'shippingname',
    'Tel.': 'shippingphone',

    'Tracking No.': 'trackingno',
    'Total': 'paymentamount',
    'Discount': 'discountamount',
    'Address': 'shippingaddress',
    'Email Address': 'shippingemail',
    'Shipping': 'shippingchannel',
    'Shipping fee': 'shippingamount',

    'SKU': 'list__sku',
    'Products': 'list__name',
    'SKU Amount': 'list__number',
    'Price': 'list__totalprice',
    'Remark': 'remark',
}

TYPE = '019-zwizshop'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if type(data) == str:
            data = data.replace(',', '')

        if data == '':
            data = self.default

        return super().to_internal_value(data)


class BlankableDateTimeField(serializers.DateTimeField):
    def to_internal_value(self, value):
        if value == '':
            return ''
        return super().to_internal_value(value)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400, allow_blank=True, default='')
    number = serializers.CharField(max_length=100, allow_blank=True, default='')
    shippingname = serializers.CharField(max_length=400, allow_blank=True, default='')
    shippingphone = serializers.CharField(max_length=400, allow_blank=True, default='')
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    orderdateString = BlankableDateTimeField(input_formats=['%d/%m/%Y %H:%M'])

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = [
        'number',
        'orderdateString',
        'saleschannel',
        'shippingname',
        'shippingphone',
        'shippingchannel'
    ]
    converters = {
        'Order Number': str,
        'Tel.': str
    }


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = [
        'number',
        'orderdateString',
        'saleschannel',
        'shippingname',
        'shippingphone',
        'shippingchannel'
    ]
    converters = {
        'Order Number': str,
        'Tel.': str
    }

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['amount'] = order['paymentamount']
        return order
