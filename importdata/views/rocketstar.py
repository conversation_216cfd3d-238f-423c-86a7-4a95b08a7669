from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'วันที่สร้าง': 'orderdateString',
    'เลขที่ออเดอร์ (ออเดอร์)': 'number',
    'ช่องทางที่ลูกค้าทักมา (ออเดอร์)': 'saleschannel',
    'ขนส่ง (ออเดอร์)': 'shippingchannel',
    'หมายเหตุ (ออเดอร์)': 'remark',
    'รหัส (สินค้าย่อย)': 'list__sku',
    'ชื่อสินค้า (สินค้า)': 'product_name_1',
    'ชื่อ (สินค้าย่อย)': 'product_name_2',
    'จำนวนที่ซื้อ': 'list__number',
    'ชื่อ (ลูกค้า)': 'shippingname',
    'ที่อยู่ (ลูกค้า)': 'shippingaddress',
    'เบอร์โทร (ลูกค้า)': 'shippingphone',
    'ยอด COD (ออเดอร์)': 'cod_amount',
}
TYPE = '040-rocketstar'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    orderdateString = serializers.DateTimeField(input_formats=['%d/%m/%Y %H:%M'])
    number = serializers.CharField(max_length=100)
    saleschannel = serializers.CharField(max_length=400)
    shippingname = serializers.CharField(max_length=400, allow_blank=True)
    shippingphone = serializers.CharField(max_length=400, allow_blank=True)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    product_name_1 = serializers.CharField(allow_blank=True, default='')
    product_name_2 = serializers.CharField(allow_blank=True, default='')
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    cod_amount = serializers.CharField()
    remark = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)

        if row['cod_amount'] != '-':
            order['isCOD'] = True
            order['amount'] = float(row['cod_amount'])
        order['list'][len(order['list']) - 1]['name'] = row['product_name_1'] + ' ' + row['product_name_2']
        
        return order

