from email.policy import default
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    # 'saleschannel': 'saleschannel',
    'Refเลขที่สั่งซื้อ': 'number',
    'ชื่อ สกุลลูกค้า': 'shippingname',
    'เบอร์โทรศัพท์': 'shippingphone',
    'เบอร์โทรศัพท์รอง': 'customerphone',

    'เลขพัสดุ': 'trackingno',
    'วันที่': 'orderdateString',
    'จำนวนเงินรวม': 'paymentamount',
    # 'discountamount': 'discountamount',
    'ที่อยู่': 'shippingaddress',
    # 'shippingemail': 'shippingemail',
    # 'shippingchannel': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    'รหัสสินค้า': 'list__sku',
    'ชื่อสินค้า': 'list__name',
    'จำนวน': 'list__number',
    'ราคา/หน่วย': 'list__totalprice',
    # 'remark': 'remark',
}
TYPE = '033-srichan-amado'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400, default="AMADO")
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    customerphone = serializers.CharField(max_length=400, allow_blank=True, default="")
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    orderdateString = serializers.DateTimeField()

    # Optional
    # shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    # shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'เบอร์โทรศัพท์': str, 'เบอร์โทรศัพท์รอง': str}
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'เบอร์โทรศัพท์': str, 'เบอร์โทรศัพท์รอง': str}
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['amount'] = order['paymentamount']
        return order
