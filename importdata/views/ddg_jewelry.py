from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


SIMPLE_ORDER_FIELD_MAPS = {
    'เลขที่บิล': 'number',
    # 'หมายเลขติดตามพัสดุ': 'trackingno',
    'ชื่อลูกค้า': 'shippingname',
    'เบอร์โทร.': 'shippingphone',
    'ที่อยู่': 'shippingaddress',
    'รหัสสินค้า': 'list__name'
}
SIMPLE_ORDER_IMPORT_TYPE = '028-ddg-jewelry'


class SimpleOrderUploadSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    # trackingno = serializers.CharField(required=True, allow_blank=True)
    shippingname = serializers.CharField(max_length=200, allow_blank=True, default="")
    shippingphone = serializers.CharField(max_length=50, allow_blank=True, default="")
    shippingaddress = serializers.Char<PERSON>ield(allow_blank=True, default="")
    list__name = serializers.Char<PERSON>ield(max_length=200, allow_blank=True, default="")


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = SIMPLE_ORDER_IMPORT_TYPE
    field_maps = SIMPLE_ORDER_FIELD_MAPS
    serializer_class = SimpleOrderUploadSerializer
    copy_upper_row_data = ['number']


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = SIMPLE_ORDER_IMPORT_TYPE
    field_maps = SIMPLE_ORDER_FIELD_MAPS
    serializer_class = SimpleOrderUploadSerializer
    copy_upper_row_data = ['number']

    def merge_order(self, order, temp_order):
        order['shippingaddress'] += ' ' + temp_order['shippingaddress']
        return order
