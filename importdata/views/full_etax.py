from rest_framework import serializers

from core.serializers.blankable import BlankableBooleanField, BlankableDecimalField
from core.serializers.tax_serializers import (
    BranchNoField,
    PostCodeField,
    ThaiTaxIDField,
)
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    "saleschannel": "saleschannel",
    "order_number": "number",
    "shippingname": "shippingname",
    "shippingphone": "shippingphone",
    "trackingno": "trackingno",
    "paymentamount": "paymentamount",
    "discountamount": "discountamount",
    "shippingaddress": "shippingaddress",
    "shippingprovince": "shippingprovince",
    "shippingdistrict": "shippingdistrict",
    "shippingsubdistrict": "shippingsubdistrict",
    "shippingpostcode": "shippingpostcode",
    "shippingemail": "shippingemail",
    "shippingchannel": "shippingchannel",
    "shippingamount": "shippingamount",
    "item_sku": "list__sku",
    "item_name": "list__name",
    "item_amount": "list__number",
    "item_totalprice": "list__totalprice",
    "remark": "remark",
    "cod": "cod",
    "customeremail": "customeremail",
    "customername": "customername",
    "customerphone": "customerphone",
    "customeridnumber": "customeridnumber",
    "customerbranchname": "customerbranchname",
    "customerbranchno": "customerbranchno",
    "customeraddress": "customeraddress",
    "customerpostcode": "customerpostcode",
    "auto_etax": "auto_etax",
    "send_email_etax": "send_email_etax",
}
TYPE = "001-full-etax"


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)

    # Required etax
    customeremail = serializers.EmailField()
    customername = serializers.CharField()
    customerphone = serializers.CharField()
    customeridnumber = ThaiTaxIDField()
    customerbranchno = BranchNoField(allow_blank=True)
    customerbranchname = serializers.CharField(allow_blank=True)
    customeraddress = serializers.CharField()
    customerpostcode = PostCodeField()
    auto_etax = BlankableBooleanField(required=False, allow_blank=True, default=False)
    send_email_etax = BlankableBooleanField(
        required=False, allow_blank=True, default=False
    )

    # Optional
    shippingname = serializers.CharField(max_length=400, allow_blank=True)
    shippingphone = serializers.CharField(max_length=400, allow_blank=True)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(
        required=False, allow_blank=True, default=""
    )
    shippingprovince = serializers.CharField(
        required=False, allow_blank=True, default=""
    )
    shippingdistrict = serializers.CharField(
        required=False, allow_blank=True, default=""
    )
    shippingsubdistrict = serializers.CharField(
        required=False, allow_blank=True, default=""
    )
    shippingpostcode = serializers.CharField(
        required=False, allow_blank=True, default=""
    )
    list__sku = serializers.CharField(allow_blank=True, default="")
    list__name = serializers.CharField(allow_blank=True, default="")
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False
    )
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default="")
    cod = BlankableBooleanField(required=False, allow_blank=True, default=False)


CONVERTERS = {
    "shippingpostcode": str,
    "shippingphone": str,
    "customerphone": str,
    "customeridnumber": str,
    "customerbranchno": str,
    "customerpostcode": str,
}


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = CONVERTERS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = CONVERTERS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        if not row["shippingname"]:
            row["shippingname"] = row["customername"]
        if not row["shippingphone"]:
            row["shippingphone"] = row["customerphone"]

        order = super().row_to_order(row)
        order["amount"] = order["paymentamount"]

        if row["cod"]:
            order["isCOD"] = True

        order["extra"] = {
            "auto_etax": row["auto_etax"],
            "send_email_etax": row["send_email_etax"],
        }

        order["customeremail"] = order["shippingemail"]
        return order
