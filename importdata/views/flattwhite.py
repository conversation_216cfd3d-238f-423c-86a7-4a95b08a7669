from email.policy import default
from rest_framework import serializers
from .generics import OrderUpload<PERSON>enericAPI, OrderImportGenericAPI
import decimal

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'saleschannel': 'saleschannel',
    'order_number': 'number',
    'shippingname': 'shippingname',
    'shippingphone': 'shippingphone',

    'trackingno': 'trackingno',
    'paymentamount': 'paymentamount',
    'discountamount': 'discountamount',
    'shippingaddress': 'shippingaddress',
    'shippingprovince': 'shippingprovince',
    'shippingdistrict': 'shippingdistrict',
    'shippingsubdistrict': 'shippingsubdistrict',
    'shippingpostcode': 'shippingpostcode',
    'shippingemail': 'shippingemail',
    'shippingchannel': 'shippingchannel',
    'shippingamount': 'shippingamount',
    'item_sku': 'list__sku',
    'item_name': 'list__name',
    'item_amount': 'list__number',
    'item_totalprice': 'list__totalprice',
    'remark': 'remark',

    'cod': 'cod',
    
    'ref1': 'ref1',
    'ref2': 'ref2',
    'ref3': 'ref3',
    'ref4': 'ref4',
    'ref5': 'ref5',
    'ref6': 'ref6',
}
TYPE = '041-flattwhite'



class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        
        try:
            value = decimal.Decimal(data)
            data = value.quantize(decimal.Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)
        except decimal.DecimalException:
            self.fail('invalid')

        return super().to_internal_value(data)


class BlankableCodBooleanField(serializers.CharField):
    def to_internal_value(self, data):
        if data == '':
            return False
        
        if data.lower() in ['y', 'yes', 't', 'true', '1']:
            return True

        return False


class FullOrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingprovince = serializers.CharField(required=False, allow_blank=True, default='')
    shippingdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingsubdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')
    cod = BlankableCodBooleanField(required=False, allow_blank=True, default=False)
    ref1 = serializers.CharField(allow_blank=True, default='')
    ref2 = serializers.CharField(allow_blank=True, default='')
    ref3 = serializers.CharField(allow_blank=True, default='')
    ref4 = serializers.CharField(allow_blank=True, default='')
    ref5 = serializers.CharField(allow_blank=True, default='')
    ref6 = serializers.CharField(allow_blank=True, default='')


class FullOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'shippingpostcode': str, 'shippingphone': str}
    serializer_class = FullOrderUploadSerializer


class FullOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'shippingpostcode': str, 'shippingphone': str}
    serializer_class = FullOrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['amount'] = order['paymentamount']

        if row['cod']:
            order['isCOD'] = True

        extra = {
            "ref1": row['ref1'],
            "ref2": row['ref2'],
            "ref3": row['ref3'],
            "ref4": row['ref4'],
            "ref5": row['ref5'],
            "ref6": row['ref6'],
        }

        order['extra'] = extra

        return order
