from django.core.validators import FileExtensionValidator
from rest_framework import serializers
import sentry_sdk
import io
import json
import traceback
from io import BytesIO
from typing import Dict, List
from django.conf import settings
import pandas as pd
from cloudtasks.tasks import create_import_order_task
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.forms.models import model_to_dict
from django.http import FileResponse
from django.utils import timezone
from django_pg_bulk_update import bulk_update_or_create
from companies.models.models import Company
from core.authentication import DobybotJWTAuthentication
from core.exceptions import PatternMismatchException
from core.permissions import ModelPermissions
from etax.models import TaxDocument
from importdata.models import OrderImportRequest
from importdata.serializers import OrderImportRequestSerializer
from openpyxl import Workbook
from openpyxl.formatting.rule import Rule
from openpyxl.styles import Font, PatternFill
from openpyxl.styles.differential import DifferentialStyle
from openpyxl.utils import get_column_letter
from picking.models import <PERSON><PERSON><PERSON>r
from picking.serializers.zort import ZortOrderSerializer
from rest_framework.authentication import TokenAuthentication
from rest_framework.generics import <PERSON>ricAPIView, get_object_or_404
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from django.db.models.fields.files import FieldFile
from services.etax_invoice.etax_service import ETaxService
from services.sms.sms import format_phone_number
from shipping.services import ShippingService
import chardet
import pydash as py_


def get_file_content_type(file) -> str:
    if type(file) == FieldFile:
        content_type = file.file.mime_type
    elif type(file) == InMemoryUploadedFile:
        content_type = file.content_type
    else:
        raise PatternMismatchException(
            f"Invalid data type for file: {type(file)}, expected FieldFile or InMemoryUploadedFile"
        )
    return content_type


def get_file_with_utf8_encoding(file):
    file_data = file.read()
    encoding = chardet.detect(file_data)["encoding"]
    file_str = file_data.decode(encoding)
    file_bytes = file_str.encode("utf-8")
    temp_file = io.BytesIO(file_bytes)
    # temp_file.seek(0)
    return temp_file


def get_data_from_file(
    file: InMemoryUploadedFile,
    field_maps: List[str],
    copy_upper_row_data: List[str] = None,
    converters=None,
    pre_process_df=None,
) -> List[dict]:
    """Read data from .xls / .xlsx / .csv file using pandas and return List[Dict]

    Parameters
    ----------
    file : File-like object
    field_maps : List[str]
        For rename columns
    copy_upper_row_data : List[str], optional
        List for column name that need to be copy from upper row,
        The first element in this list will be used as key

    Returns
    -------
    List[dict]
        _description_
    """
    if get_file_content_type(file) == "text/csv":
        temp_file = get_file_with_utf8_encoding(file)
        df = pd.read_csv(temp_file, converters=converters)
    else:
        df = pd.read_excel(file, converters=converters)

    df = df.rename(field_maps, axis="columns")
    df = df.fillna("")

    if pre_process_df:
        df = pre_process_df(df)

    if "shippingphone" in df.columns:
        if df["shippingphone"].dtype == "int64":
            df["shippingphone"] = (
                df["shippingphone"].astype(str).apply(format_phone_number)
            )

    if copy_upper_row_data:
        key_field = copy_upper_row_data[0]
        for i in range(len(df)):
            if not df.loc[i, key_field]:
                df.loc[i, copy_upper_row_data] = df.loc[i - 1, copy_upper_row_data]

    return df.to_dict(orient="records")


# class PickOrderSerializer(serializers.ModelSerializer):

#     # order_json = serializers.SerializerMethodField()

#     # def get_order_json(self, instance):

#     class Meta:
#         model = PickOrder
#         exclude = ['company', 'ready_to_ship_by']


def pick_order_to_dict(pick_order):
    data = model_to_dict(pick_order)
    del data["id"]
    return data


class OrderUploadFileValidator(serializers.Serializer):
    file = serializers.FileField(
        validators=[FileExtensionValidator(["xlsx", "xls", "csv"])]
    )
    options = serializers.JSONField(default={})


class OrderUploadGenericAPI(GenericAPIView):
    """
    Validate file, if pass upload the file to google cloud storage,
    en-queue for importing
    """

    field_maps = {}
    import_type = None
    copy_upper_row_data = None  # ex: ['number', 'saleschannel']
    converters = None  # ex: {'เลขแทร็ก': str}
    permission_classes = [ModelPermissions]
    queryset = OrderImportRequest.objects.all()

    def post(self, request):
        validator = OrderUploadFileValidator(data=request.data)
        validator.is_valid(raise_exception=True)
        file: InMemoryUploadedFile = validator.validated_data["file"]
        import_options = validator.validated_data.get("options", {})

        # Get data from file
        data = get_data_from_file(
            file,
            self.field_maps,
            self.copy_upper_row_data,
            self.converters,
            self.pre_process_df,
        )

        # Validate the file
        validator = self.get_serializer(data=data, many=True)
        if not validator.is_valid():
            # return excel file with errors
            workbook = self.create_xlsx_error_report(data, validator.errors)
            temp = BytesIO()
            workbook.save(temp)
            temp.seek(0)
            return FileResponse(
                temp, as_attachment=True, filename=file.name, status=422
            )

        # Save file to GCS
        serializer = OrderImportRequestSerializer(
            data={
                "company": request.user.company_id,
                "file": file,
                "options": import_options,
            }
        )
        serializer.is_valid(raise_exception=True)
        import_request: OrderImportRequest = serializer.save(create_by=request.user)
        import_request.writelog(
            f"File accepted: {import_request.file} ({import_request.id})"
        )
        import_request.writelog(f"Options: {import_options}")

        # Create Google Cloud Task for importing
        if settings.CLOUD_TASK_HOST:
            task_id = create_import_order_task(self.import_type, import_request.id)
            import_request.writelog(f"Queue id: {task_id} ({import_request.id})")

        return Response(serializer.data)

    def create_xlsx_error_report(self, data: List[dict], error: List[dict]) -> BytesIO:
        # Append error message to data cell
        table = []
        for row, err in zip(data, error):
            for key in err:
                row[key] = str(row.get(key, "")) + " !!! " + ",".join(err[key])
            table.append(row)
        df = pd.DataFrame(table)

        # Rename headers
        inv_field_maps = {v: k for k, v in self.field_maps.items()}
        df = df.rename(inv_field_maps, axis="columns")

        # Write df to excel file
        bytes_io = BytesIO()
        writer = pd.ExcelWriter(bytes_io, engine="openpyxl")
        df.to_excel(writer, sheet_name="Sheet1", index=False)
        wb: Workbook = writer.book

        # Highlight error cells with conditional formatting
        ws = wb.active
        red_text = Font(color="9C0006")
        red_fill = PatternFill(bgColor="FFC7CE")
        dxf = DifferentialStyle(font=red_text, fill=red_fill)
        rule = Rule(type="containsText", operator="containsText", text="!!!", dxf=dxf)
        rule.formula = ['NOT(ISERROR(SEARCH("!!!",A1)))']
        ws.conditional_formatting.add(
            f"A1:{get_column_letter(df.shape[1])}{df.shape[0] + 1}", rule
        )

        return wb

    def pre_process_df(self, df: pd.DataFrame):
        return df


class OrderImportGenericAPI(GenericAPIView):
    """
    Perform importing
    """

    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication, DobybotJWTAuthentication]
    field_maps = {}
    copy_upper_row_data = None
    converters = None
    default_sale_channel = "IMPORT"
    default_status = "Pending"

    def get_queryset(self):
        return OrderImportRequest.objects.all()

    def get_object(self):
        id = self.request.data.get("import_request_id")
        return get_object_or_404(self.get_queryset(), id=id)

    def post(self, request):
        import_request: OrderImportRequest = self.get_object()
        file = import_request.file
        data = get_data_from_file(
            file,
            self.field_maps,
            self.copy_upper_row_data,
            self.converters,
            self.pre_process_df,
        )

        if self.serializer_class:
            serializer = self.get_serializer(data=data, many=True)
            serializer.is_valid(raise_exception=True)
            data = serializer.validated_data

        try:
            row_number = 0
            orders = {}
            for row in data:
                row_number += 1
                temp_order = self.row_to_order(row)
                order = orders.get(temp_order["number"])
                if order:
                    order["list"] = [*order["list"], *temp_order["list"]]
                    order = self.merge_order(order, temp_order)
                else:
                    order_number = temp_order["number"]
                    orders[order_number] = temp_order

            orders = self.post_process_orders(orders)
        except Exception as e:
            traceback.print_exc()
            event_id = sentry_sdk.capture_exception(e)
            import_request.writelog(
                f"Failed to process order row: {row_number}, \nerror : {str(e)}, \nevent_id: {event_id}"
            )
            import_request.status = OrderImportRequest.ERROR
            import_request.save()
            return Response(import_request.log, status=400)

        serializer = ZortOrderSerializer(data=list(orders.values()), many=True)
        if not serializer.is_valid():
            import_request.writelog(
                json.dumps(serializer.errors, ensure_ascii=False), commit=False
            )
            import_request.status = OrderImportRequest.ERROR
            import_request.save()
            return Response(serializer.errors, status=400)

        existed_pick_orders = self.get_existed_pick_orders(
            company=import_request.company, order_numbers=orders.keys()
        )

        upsertlist = []
        insertlist: List[PickOrder] = []
        updatelist: List[PickOrder] = []
        for order in serializer.validated_data:
            pick_order = existed_pick_orders.get(order["number"])
            if pick_order:
                pick_order.update_from_zort_order(
                    order=order,
                    commit=False,
                    update_by=import_request.create_by,
                    order_oms="import",
                    import_options=import_request.options,
                )
                updatelist.append(pick_order)
            else:
                pick_order = PickOrder.create_from_zort_order(
                    company=import_request.company,
                    order=order,
                    commit=False,
                    create_by=import_request.create_by,
                    order_oms="import",
                )
                insertlist.append(pick_order)
            upsertlist.append(pick_order_to_dict(pick_order))

        count = bulk_update_or_create(
            PickOrder, values=upsertlist, key_fields=["company", "order_number"]
        )
        import_request.status = OrderImportRequest.SUCCESS
        import_request.writelog(f"Success importing {count} orders")
        import_request.save()

        company: Company = import_request.company
        if company.get_setting("AUTO_GENERATE_TRACKINGNO_ENABLE"):
            self.enqueue_auto_trackingno_tasks(import_request, insertlist, updatelist)

        if company.feature_flag.get(Company.ETAX):
            pick_orders = PickOrder.objects.filter(
                company=company,
                order_number__in=[p.order_number for p in insertlist + updatelist],
                order_json__extra__auto_etax=True,
            )
            ETaxService.enqueue_auto_etax_tasks(pick_orders)

        return Response(status=200)

    def enqueue_auto_trackingno_tasks(
        self,
        ir: OrderImportRequest,
        insertlist: List[PickOrder],
        updatelist: List[PickOrder],
    ):
        if ir.options.do_not_update_tracking:
            orderlist = insertlist
        else:
            orderlist = insertlist + updatelist

        order_numbers = [x.order_number for x in orderlist if x.order_trackingno == ""]
        pick_orders = PickOrder.objects.filter(
            order_number__in=order_numbers,
            order_trackingno__exact="",
            company=ir.company,
        )
        ShippingService.enqueue_create_tracking_no(ir.company, pick_orders)

    def get_existed_pick_orders(self, company, order_numbers):
        pick_orders = PickOrder.objects.filter(
            company=company, order_number__in=order_numbers
        )
        return {po.order_number: po for po in pick_orders}

    def pre_process_df(self, df: pd.DataFrame):
        return df

    def merge_order(self, order, temp_order):
        return order

    def post_process_orders(self, orders: Dict):
        return orders

    def row_to_order(self, row):
        order = {
            "amount": self.get_field(row, "amount"),
            "createdatetimeString": self.get_field(
                row,
                "createdatetimeString",
                dtype="datetime",
                default=timezone.localtime(),
            ),
            "customeraddress": self.get_field(row, "customeraddress", default=""),
            "customeremail": self.get_field(row, "customeremail", default=""),
            "customername": self.get_field(row, "customername"),
            "customerphone": self.get_field(row, "customerphone"),
            "customeridnumber": self.get_field(row, "customeridnumber", default=""),
            "customerbranchname": self.get_field(row, "customerbranchname", default=""),
            "customerbranchno": self.get_field(row, "customerbranchno", default=""),
            "customerpostcode": self.get_field(row, "customerpostcode", default=""),
            "description": self.get_field(row, "description", default=""),
            "discount": self.get_field(row, "discount", default="0"),
            "discountamount": self.get_field(row, "discountamount", default=0),
            "isCOD": self.get_field(row, "isCOD", default=0),
            "warehousecode": self.get_field(row, "warehousecode", default="00000"),
            "list": [
                {
                    "sku": self.get_field(row, "list__sku"),
                    "name": self.get_field(row, "list__name"),
                    "number": self.get_field(row, "list__number"),
                    "unittext": self.get_field(row, "list__unittext", default=""),
                    "totalprice": self.get_field(row, "list__totalprice"),
                    "pricepernumber": self.get_field(row, "list__pricepernumber"),
                    "discountamount": self.get_field(
                        row, "list__discountamount", default=0
                    ),
                    "sku_barcode": self.get_field(row, "list__sku_barcode", default=""),
                },
            ],
            "number": self.get_field(row, "number"),
            "orderdateString": self.get_field(
                row, "orderdateString", dtype="date", default=timezone.localdate()
            ),
            "paymentamount": self.get_field(row, "paymentamount"),
            "paymentmethod": self.get_field(row, "paymentmethod"),
            "payments": [
                {
                    "id": self.get_field(row, "payments__id"),
                    "name": self.get_field(row, "payments__name"),
                    "amount": self.get_field(row, "payments__amount"),
                    "paymentdatetimeString": self.get_field(
                        row, "payments__paymentdatetimeString", dtype="datetime"
                    ),
                }
            ],
            "paymentstatus": self.get_field(row, "paymentstatus", default="Paid"),
            "platformdiscount": self.get_field(row, "platformdiscount", default=0),
            "saleschannel": self.get_field(
                row, "saleschannel", default=self.default_sale_channel
            ),
            "sellerdiscount": self.get_field(row, "sellerdiscount", default=0),
            "shippingaddress": self.get_field(row, "shippingaddress", default="ไม่ระบุ"),
            "shippingamount": self.get_field(row, "shippingamount", default=0),
            "shippingchannel": self.get_field(row, "shippingchannel", default="ไม่ระบุ"),
            "shippingdistrict": self.get_field(row, "shippingdistrict", default=""),
            "shippingemail": self.get_field(row, "shippingemail", default=""),
            "shippingname": self.get_field(row, "shippingname"),
            "shippingphone": self.get_field(row, "shippingphone", default=""),
            "shippingpostcode": self.get_field(row, "shippingpostcode", default=""),
            "shippingprovince": self.get_field(row, "shippingprovince", default=""),
            "shippingsubdistrict": self.get_field(
                row, "shippingsubdistrict", default=""
            ),
            "shippingvat": self.get_field(row, "shippingvat", default=0),
            "status": self.get_field(row, "status", default=self.default_status),
            "trackingno": self.get_field(row, "trackingno", default=""),
            "updatedatetimeString": self.get_field(
                row, "updatedatetimeString", dtype="datetime"
            ),
            "vatamount": self.get_field(row, "vatamount", default=0),
            "vatpercent": self.get_field(row, "vatpercent", default=7),
            "vattype": self.get_field(row, "vattype", default=1),
            "voucheramount": self.get_field(row, "voucheramount", default=0),
            "remark": self.get_field(row, "remark", default=""),
        }

        if order["customername"] is None:
            order["customername"] = order["shippingname"]
        if order["customerphone"] is None:
            order["customerphone"] = order["shippingphone"]

        order["shippingaddress"] = self.get_order_address(order)
        try:
            order["list"] = self.get_order_list(order)
        except Exception as e:
            traceback.print_exc()
            sentry_sdk.capture_exception(e)
            order["list"] = []
        order["payments"] = self.get_order_payments(order)
        order["extra"] = {}
        return order

    def get_field(self, row, key, dtype=str, default=None):
        if key not in self.field_maps.values():
            value = default
        else:
            value = row.get(key)

        if value and dtype == "date":
            value = value.strftime("%Y-%m-%d")

        if value and dtype == "datetime":
            value = value.strftime("%Y-%m-%d %H:%M")

        return value

    def get_order_address(self, order: Dict):
        return order.get("shippingaddress", "ไม่ระบุ")

    def get_order_list(self, order: Dict):
        list = order.get("list", [])
        if not list:
            return []

        list0 = order["list"][0]
        if not list0.get("sku") and not list0.get("name"):
            return []

        if not list0.get("sku"):
            list0["sku"] = "-"

        if list0["pricepernumber"] is None and list0["totalprice"] and list0["number"]:
            list0["pricepernumber"] = round(list0["totalprice"] / list0["number"], 2)
        if list0["totalprice"] is None and list0["pricepernumber"] and list0["number"]:
            list0["totalprice"] = list0["pricepernumber"] * list0["number"]

        if list0["pricepernumber"] is None:
            list0["pricepernumber"] = 0
        if list0["number"] is None:
            list0["number"] = 0
        if list0["totalprice"] is None:
            list0["totalprice"] = 0

        return [list0]

    def get_order_payments(self, order: Dict):
        payments = order.get("payments", [])
        if not payments:
            return []

        payment0 = payments[0]
        if payment0["name"] is None and payment0["amount"] is None:
            return []

        if payment0["amount"] is None:
            payment0["amount"] = order["paymentamount"]

        return [payment0]
