
from rest_framework import serializers

from importdata.serializers import DateTimeField, DecimalField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI
import decimal

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'หมายเลขคำสั่งซื้อภายใน': 'number',
    'ยอดรวมที่ต้องชำระ': 'paymentamount',
    'เลขพัสดุ': 'trackingno',

    'ผู้รับ': 'shippingname',
    'บริษัทขนส่ง': 'shippingchannel',
    'ค่าจัดส่ง': 'shippingamount',
    'แพลตฟอร์ม': 'saleschannel',
    'ที่อยู่ผู้รับ': 'shippingaddress',
    'จังหวัด': 'shippingprovince',
    'เมือง': 'shippingdistrict',
    'แขวง/ตำบล': 'shippingsubdistrict',
    'รหัสไปรษณีย์': 'shippingpostcode',

    'รหัสสินค้า': 'list__sku',
    'ชื่อสินค้า': 'list__name',
    'ราคาต่อหน่วย': 'list__pricepernumber',
    'จำนวน': 'list__number',
    'จำนวนส่วนลดแพลตฟอร์ม': 'list__discount',
    'จํานวนเงินจํากัด (ตัดส่วนลด)': 'list__totalprice',
}
TYPE = '039-wellnesssys'

class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        
        try:
            value = decimal.Decimal(data)
            data = value.quantize(decimal.Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)
        except decimal.DecimalException:
            self.fail('invalid')

        return super().to_internal_value(data)

class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField()
    shippingname = serializers.CharField(max_length=400)

    # Optional

    trackingno = serializers.CharField(required=False, allow_blank=True, default='')
    saleschannel = serializers.CharField(required=False, allow_blank=True)

    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingprovince = serializers.CharField(required=False, allow_blank=True, default='')
    shippingdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingsubdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(required=False, allow_blank=True, default='')
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__pricepernumber = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__number = BlankableDecimalField(max_digits=12, decimal_places=2, default=1, required=False)
    list__discount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

