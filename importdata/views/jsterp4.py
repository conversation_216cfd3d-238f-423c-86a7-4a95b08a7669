from email.policy import default
from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'หมายเลขคำสั่งซื้อภายใน': 'number',
    'สินค้า ': 'list__sku',
    'บริษัทขนส่ง': 'shippingchannel',
    'เลขพัสดุ': 'trackingno',
    'ร้านค้า​': 'saleschannel',
    'ที่อยู่ผู้รับ': 'shippingaddress',
    'จังหวัด': 'shippingprovince',
    'เมือง': 'shippingdistrict',
    'แขวง/ตำบล': 'shippingsubdistrict',
    'ผู้รับ': 'shippingname',
    'รหัสไปรษณีย์': 'shippingpostcode',
    'เบอร์โทรศัพท์': 'shippingphone',
}

TYPE = '023-jst-erp-4'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.Char<PERSON>ield(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    # shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingsubdistrict = serializers.CharField(allow_blank=True)
    shippingdistrict = serializers.CharField(allow_blank=True)
    shippingprovince = serializers.CharField(allow_blank=True)
    shippingpostcode = serializers.CharField(allow_blank=True)
    list__sku = serializers.CharField(allow_blank=True, default='')
    # list__name = serializers.CharField(allow_blank=True, default='')
    # list__number = BlankableDecimalField(
    #     max_digits=12, decimal_places=2, default=1, required=False)
    # list__pricepernumber = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')

    def validate_list__sku(self, value):
        ## Value
        # CHRBN-KLW-B210-BK*1 \\ GAOMC-KAZ-KZ409S-BK*1,TDS-S1*1 
        item_str_arr = value.split(',')
        item_list = []
        for item in item_str_arr:
            item_info_list = item.split('*')
            if len( item_info_list ) < 2:
                raise serializers.ValidationError("Wrong Format")
        return value
            
            
def try_int(value):
    try:
        return int(value)
    except ValueError:
        return value


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'หมายเลขคำสั่งซื้อภายใน': try_int}


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'หมายเลขคำสั่งซื้อภายใน': try_int}

    def post_process_orders(self, orders: Dict):
        return orders
    
    def get_order_address(self, order: Dict):
        return f"{order['shippingaddress']}, {order['shippingsubdistrict']}, {order['shippingdistrict']}, {order['shippingprovince']}, {order['shippingpostcode']}"

    def row_to_order(self, row):
        order = super().row_to_order(row)

        # CHRBN-KLW-B210-BK*1
        # GAOMC-KAZ-KZ409S-BK*1,TDS-S1*1
        item_str = order['list'][0]['sku']
        item_str_arr = item_str.split(',')
        item_list = []
        for item in item_str_arr:
            sku, number = item.split('*')
            item_list.append({
                'sku': sku,
                'name': sku,
                'number': number,
                "unittext": "",
                "totalprice": 0,
                "pricepernumber": 0,
                "discountamount": 0
            })
        order['list'] = item_list

        return order
