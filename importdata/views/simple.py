from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


SIMPLE_ORDER_FIELD_MAPS = {
    'เลขที่ออเดอร์': 'number',
    'หมายเลขติดตามพัสดุ': 'trackingno',
    'ชื่อลูกค้า': 'shippingname',
    'เบอร์โทรลูกค้า': 'shippingphone',
    'อีเมล์ลูกค้า': 'shippingemail',
    'หมายเหตุ': 'remark'
}
SIMPLE_ORDER_IMPORT_TYPE = '001-simple'


class SimpleOrderUploadSerializer(serializers.Serializer):
    number = serializers.Char<PERSON>ield(max_length=50)
    trackingno = serializers.CharField(required=True, allow_blank=True)
    shippingname = serializers.CharField(max_length=200)
    shippingphone = serializers.Char<PERSON>ield(max_length=50)
    shippingemail = serializers.Char<PERSON><PERSON>(
        max_length=200, required=False, allow_blank=True, default="")
    remark = serializers.Char<PERSON>ield(
        required=False, allow_blank=True, default="")


class SimpleOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = SIMPLE_ORDER_IMPORT_TYPE
    field_maps = SIMPLE_ORDER_FIELD_MAPS
    serializer_class = SimpleOrderUploadSerializer


class SimpleOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = SIMPLE_ORDER_IMPORT_TYPE
    field_maps = SIMPLE_ORDER_FIELD_MAPS
