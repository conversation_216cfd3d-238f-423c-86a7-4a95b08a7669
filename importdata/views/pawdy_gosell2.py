from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

#
# Order Number
# Tracking Number
# Payment Type
# Customer Name
# Customer Address
# Customer District: shippingdistrict
# Customer Sub District: shippingsubdistrict
# Customer Province: shippingprovince
# Customer Zipcode: shippingpostcode
# Tel
# Created Date
# Shipping Status
# Order Status
# SKU
# Item Name
# Price
# Quantity
# Weight
# Discount
# Total
# Courier
# Shop Name
# Create By
# วันที่ผลิต

FIELD_MAPS = {
    'ช่องทางการขาย': 'saleschannel',
    'เลขที่คำสั่งซื้อ': 'number',
    'ลูกค้า': 'shippingname',
    'เบอร์โทร1': 'shippingphone',

    'หมายเลขพัสดุ': 'trackingno',
    'ที่อยู่': 'shippingaddress',
    'แขวง/ ตำบล': 'shippingdistrict',
    'เขต/ อำเภอ': 'shippingsubdistrict',
    'จังหวัด': 'shippingprovince',
    'รหัสไปรษณีย์': 'shippingpostcode',
    'ขนส่ง': 'shippingchannel',
    'สถานะคำสั่งซื้อ': 'status',
    'รหัสสินค้า (SKU)': 'list__sku',
    'ชื่อสินค้า': 'list__name',
    'จำนวนสินค้า': 'list__number',
    'ยอดรวมสินค้า': 'list__totalprice',
    'ส่วนลดต่อชิ้น': 'list__discountamount',

    'วันที่ผลิต': 'list__mfg_date',
}
STATUS_MAPS = {
    'กำลังดำเนินการ': 'Pending',
    'Complete': 'Success',
    'ยกเลิก': 'Voided'
}
TYPE = '018-ship-offline-gosell-2'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default

        if type(data) is str:
            data = data.replace(',', '')

        return super().to_internal_value(data)


class BlankableDateField(serializers.DateField):
    def to_internal_value(self, value):
        if value == '':
            return None
        return super().to_internal_value(value)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    status = serializers.CharField(required=False, allow_blank=True, default='')
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    shippingsubdistrict = serializers.CharField(
        default=None, allow_blank=True, allow_null=True)
    shippingdistrict = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    shippingprovince = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__mfg_date = BlankableDateField(
        input_formats=["%d-%m-%Y", "%Y-%m-%d %H:%M:%S"], allow_null=True, required=False)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'วันที่ผลิต': str, 'Tel': str}
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {'วันที่ผลิต': str, 'Tel': str}
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['list'][0]['mfg_date'] = self.get_field(row, 'list__mfg_date', dtype='date')
        return order

    def post_process_orders(self, orders: Dict):
        results = {}
        for key, order in orders.items():
            order['status'] = STATUS_MAPS.get(order['status'], 'Pending')
            if order['status'] == 'Voided':
                continue

            order['paymentamount'] = sum(item['totalprice'] for item in order['list'])
            order['amount'] = order['paymentamount']
            order['discountamount'] = sum(item['discountamount'] for item in order['list'])
            order['shippingaddress'] = f"{order['shippingaddress']}, {order['shippingsubdistrict']}, {order['shippingdistrict']}, {order['shippingprovince']} {order['shippingpostcode']}"
            results[key] = order

        return results
