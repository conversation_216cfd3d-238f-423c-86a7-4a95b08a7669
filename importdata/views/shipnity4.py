from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'วันที่สร้าง': 'orderdateString',
    'เลขที่ออเดอร์ (ออเดอร์)': 'number',
    'ขนส่ง (ออเดอร์)': 'shippingchannel',
    'ช่องทางที่ลูกค้าทักมา (ออเดอร์)': 'saleschannel',
    'ID บน Marketplace (ออเดอร์)': 'order_id',
    'เลขพัสดุ (ออเดอร์)': 'trackingno',
    'ชื่อ (ลูกค้า)': 'shippingname',
    'เบอร์โทร (ลูกค้า)': 'shippingphone',
    'ที่อยู่ (ลูกค้า)': 'shippingaddress',
    'รหัสสินค้า': 'list__sku',
    'รายการขาย (ออเดอร์)': 'list__name',
    'ยอด COD (ออเดอร์)': 'cod_amount',
    'หมายเหตุ (ออเดอร์)': 'shipping_sorting_no',

    # 'paymentamount': 'paymentamount',
    # 'discountamount': 'discountamount',
    # 'shippingemail': 'shippingemail',
    # 'shippingchannel': 'shippingchannel',
    # 'shippingamount': 'shippingamount',
    # 'item_amount': 'list__number',
    # 'item_totalprice': 'list__totalprice',
    # 'remark': 'remark',
}
TYPE = '013-shipnity-4'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    orderdateString = serializers.DateTimeField(input_formats=['%d/%m/%Y %H:%M'])
    number = serializers.CharField(max_length=100)
    order_id = serializers.CharField(max_length=100)
    saleschannel = serializers.CharField(max_length=400)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingname = serializers.CharField(max_length=400, allow_blank=True)
    shippingphone = serializers.CharField(max_length=400, allow_blank=True)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    cod_amount = serializers.CharField()
    shipping_sorting_no = serializers.CharField(allow_blank=True, default='')

    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # list__number = BlankableDecimalField(
    #     max_digits=12, decimal_places=2, default=1, required=False)
    # list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)

        if row['cod_amount'] != '-':
            order['isCOD'] = True
            order['amount'] = float(row['cod_amount'])
        
        order['shippingphone'] = ''
        order['shipping_sorting_no'] = row.get('shipping_sorting_no', '')
        
        return order

    def post_process_orders(self, orders: Dict):
        for order_number, order in orders.items():
            item_skus = [item['sku'] for item in order['list']]
            item_skus.reverse()
            item_names_amount = order['list'][0]['name'].split('|')
            # item_names = []
            # item_amounts = []
            items = []
            for i in range(len(item_skus)):
                name_amount = item_names_amount[i].split('=')
                item_name = name_amount[0].strip()
                item_amount = name_amount[1].strip()
                item_sku = item_skus[i]
                items.append({
                    'sku': item_sku,
                    'name': item_name,
                    'number': float(item_amount),
                    'unittext': '',
                    'totalprice': 0,
                    'pricepernumber': 0,
                    'discountamount': 0
                })

            order['list'] = items
        return orders
