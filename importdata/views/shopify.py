from email.policy import default
from rest_framework import serializers

from importdata.serializers import DateTimeField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'Name': 'number',
    'Shipping Name': 'shippingname',
    'Shipping Phone': 'shippingphone',

    # 'trackingno': 'trackingno',
    # 'saleschannel': 'saleschannel',
    'Created at': 'orderdateString',
    'Total': 'paymentamount',
    'Discount Amount': 'discountamount',
    'Shipping Address1': 'shippingaddress',
    'Shipping Address2': 'shippingaddress2',
    'Shipping City': 'shippingdistrict',
    'Shipping Zip': 'shippingpostcode',

    'Email': 'shippingemail',
    'Shipping Method': 'shippingchannel',
    'Shipping': 'shippingamount',
    'Lineitem sku': 'list__sku',
    'Lineitem name': 'list__name',
    'Lineitem quantity': 'list__number',
    'Lineitem price': 'list__totalprice',
    # 'remark': 'remark',
}
TYPE = '032-shopify'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400, default='Shopify')
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    orderdateString = DateTimeField(input_formats=['%Y-%m-%d %H:%M:%S %z'])

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingaddress2 = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    shippingdistrict = serializers.CharField(default=None, allow_blank=True, allow_null=True)

    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = ['shippingname', 'shippingphone']
    converters = {'Shipping Phone': str}


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = ['shippingname', 'shippingphone']
    converters = {'Shipping Phone': str}

    def row_to_order(self, row):
        row['shippingaddress'] = f'{row["shippingaddress"]} {row["shippingaddress2"]}, {row["shippingdistrict"]}, {row["shippingpostcode"]}'
        order = super().row_to_order(row)
        order['amount'] = order['paymentamount']
        return order
