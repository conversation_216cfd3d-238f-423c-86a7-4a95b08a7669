from email.policy import default
from rest_framework import serializers

from importdata.serializers import Date<PERSON><PERSON><PERSON><PERSON>, DecimalField
from .generics import OrderUp<PERSON><PERSON>enericAP<PERSON>, OrderImportGenericAPI
import decimal

# Excel Field: Dobybot Field
# FIELD_MAPS = {
#     'invno': 'number',
#     'invdate': 'orderdateString',
#     'custcode': 'customer_code',
#     'Custname': 'customer_name',
#     'goodcode': 'list__sku',
#     'GoodQty2': 'list__number',
#     'GoodName': 'list__name',
#     'GoodUnitname': 'list__unittext',
#     'GoodAmnt': 'list__totalprice',
#     'Barcode': 'list__sku_barcode',
#     'ShiptoName': 'shippingaddress',
#     'CustAdd': 'customeraddress',
#     'ContTel': 'shippingphone',
# }

FIELD_MAPS = {
    "invno": "number",
    "invdate": "orderdateString",
    "custcode": "customer_code",
    "Custname": "customer_name",
    "goodcode": "list__sku",
    "goodqty2": "list__number",
    "goodname": "list__name",
    "goodunitname": "list__unittext",
    "goodamnt": "list__totalprice",
    "Barcode": "list__sku_barcode",
    "ShiptoName": "shippingaddress",
    "CustAdd": "customeraddress",
    "ContTel": "shippingphone",
}
TYPE = "036-somphop-liquor-house"


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == "":
            data = self.default

        try:
            value = decimal.Decimal(data)
            data = value.quantize(
                decimal.Decimal("0.01"), rounding=decimal.ROUND_HALF_UP
            )
        except decimal.DecimalException:
            self.fail("invalid")

        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField()
    customer_code = serializers.CharField()
    customer_name = serializers.CharField()

    # Optional
    shippingphone = serializers.CharField(required=False, allow_blank=True, default="")
    shippingaddress = serializers.CharField(
        required=False, allow_blank=True, default=""
    )
    list__sku = serializers.CharField(allow_blank=True, default="")
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False
    )
    list__name = serializers.CharField(allow_blank=True, default="")
    list__unittext = serializers.CharField(allow_blank=True, default="")
    list__sku_barcode = serializers.CharField(allow_blank=True, default="")
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    orderdateString = DateTimeField(input_formats=["%d/%m/%Y %H:%M:%S"])
    customer_address = serializers.CharField(
        required=False, allow_blank=True, default=""
    )


converters = {
    "invno": str,
    "ContTel": str,
    "custcode": str,
    "goodcode": str,
    "Barcode": str,
}


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = converters


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = converters

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order["customername"] = row["customer_name"] + " " + row["customer_code"]
        order["shippingname"] = order["customername"]

        if order["list"][0]["unittext"] is None:
            order["list"][0]["unittext"] = ""

        return order
