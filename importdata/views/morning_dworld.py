from email.policy import default
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


FIELD_MAPS = {
    'เลขที่เอกสาร': 'order_id',
    'เลขที่ออเดอร์มาร์เก็ตเพลส': 'number',
    'ชื่อผู้รับ': 'shippingname',
    'เบอร์โทร': 'shippingphone',

    'เลขแทร็ก': 'trackingno',
    'ที่อยู่ผู้รับ': 'shippingaddress',
    'วิธีการส่ง': 'shippingchannel',
    'รหัส ปณ ผู้รับ': 'shippingpostcode',
    'รหัสสินค้า': 'list__sku',
    'ชื่อสินค้า': 'list__name',
    'จำนวน': 'list__number',
    'ออฟชั่น': 'list__unittext',
}
TYPE = '014-morning-dworld'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class UploadSerializer(serializers.Serializer):
    # Required
    order_id = serializers.CharField(max_length=100)
    number = serializers.CharField(max_length=100, allow_blank=True, default='')
    shippingname = serializers.Char<PERSON>ield(max_length=400)
    shippingphone = serializers.CharField(max_length=400)

    # Optional
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    shippingpostcode = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(max_digits=12, decimal_places=2, default=1, required=False)
    list__unittext = serializers.CharField(allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = UploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = UploadSerializer

    def row_to_order(self, row):
        row['list__name'] += f' {row["list__unittext"]}'
        row['list__unittext'] = ''

        order = super().row_to_order(row)
        if not order['number']:
            order['number'] = row['order_id']

        return order
