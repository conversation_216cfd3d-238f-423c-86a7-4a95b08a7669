from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI
import decimal

# Excel Field: Dobybot Field
FIELD_MAPS = {
    "วันที่": "orderdateString",
    "เลขที่บิล": "number",
    "รหัสซื้อขาย": "list__sku",
    "ชื่อสินค้า": "list__name",
    "หน่วย": "list__unit",
    "จำนวน": "list__number",
    "ราคา@": "list__pricepernumber",
    "ขายรวมภพ.": "list__totalprice",
    "ส่วนลด@": "list__discountamount",
    "ภพ.": "list__vat",
}
TYPE = "042-etax-charoensin"


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data: str):
        if data == "":
            data = self.default

        if data == "-":
            data = self.default

        if isinstance(data, str) and "," in data:
            data = data.replace(",", "")

        try:
            value = decimal.Decimal(data)
            data = value.quantize(
                decimal.Decimal("0.01"), rounding=decimal.ROUND_HALF_UP
            )
        except decimal.DecimalException:
            self.fail("invalid")

        return super().to_internal_value(data)


class ThaiDateField(serializers.DateField):
    def to_internal_value(self, value):
        value = super().to_internal_value(value)
        value = value.replace(year=value.year - 543)
        return value


class OrderUploadSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=100)
    orderdateString = ThaiDateField(input_formats=["%d/%m/%Y"])

    list__sku = serializers.CharField(allow_blank=True, default="")
    list__name = serializers.CharField(allow_blank=True, default="")
    list__unit = serializers.CharField(allow_blank=True, default="")
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False
    )
    list__pricepernumber = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=0
    )
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__discountamount = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=0
    )
    list__vat = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)


def pre_process_df(df):
    df.drop(index=0, inplace=True)
    df.drop(index=df.index[-1], inplace=True)

    df["number"] = df["number"].str.strip()
    df = df[df["number"] != "รวมทั้งบิล"]
    df = df[df["number"] != "รวมวันที่"]
    df = df[df["number"] != "รวมสาขา"]
    df = df[df["number"] != "รวมทั้งหมด"]

    df = df.reset_index(drop=True)
    df = df.reset_index(drop=True)
    return df


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {"number": str}
    copy_upper_row_data = ["number"]
    serializer_class = OrderUploadSerializer

    def pre_process_df(self, df):
        return pre_process_df(df)


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """

    import_type = TYPE
    field_maps = FIELD_MAPS
    converters = {"number": str}
    copy_upper_row_data = ["number"]
    serializer_class = OrderUploadSerializer

    def pre_process_df(self, df):
        return pre_process_df(df)

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order["shippingname"] = "-"
        order["customername"] = "-"

        return order

    def post_process_orders(self, orders):
        for order in orders.values():
            order["amount"] = sum(i["totalprice"] for i in order["list"])
            order["paymentamount"] = order["amount"]
        return orders
