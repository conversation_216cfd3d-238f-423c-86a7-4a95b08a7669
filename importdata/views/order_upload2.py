import pandas as pd

from typing import List
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import serializers
from companies.models import Company


from picking.cloud_tasks import create_import_orders_task
from picking.models import PickOrder
from picking.serializers.zort import ZortOrderSerializer


def row_to_order(row, field_maps):
    def get_field(key, dtype='str', default=None):
        if key not in field_maps:
            return default

        value = row[field_maps[key]]

        if dtype == 'date':
            value = value.strftime('%Y-%m-%d')

        if dtype == 'datetime':
            value = value.strftime('%Y-%m-%d %H:%m')

        return value


class GenericOrderUploadAPI(APIView):
    field_maps = {}

    def validate_headers(self, file_headers: List[str]):
        for h in self.field_maps.values():
            if not h in file_headers:
                raise serializers.ValidationError(f'Invalid Header, missing {h}')

    def post(self, request):
        file = request.FILES.get('file')
        # get file content as dict
        df = pd.read_excel(file)
        file_headers = list(df.columns.values)
        file_content = df.to_dict(orient='records')

        # Validate file content
        self.validate_headers(file_headers)

        orders = {}
        for row in file_content:
            temp_order = row_to_order(row, self.field_maps)

            order = orders.get(temp_order['number'])
            if order:
                order['list'] = [*order['list'], *temp_order['list']]
            else:
                order_number = temp_order['number']
                orders[order_number] = temp_order

        serializer = ZortOrderSerializer(data=list(orders.values()), many=True)
        serializer.is_valid(raise_exception=True)

        createlist = []
        for order in serializer.validated_data:
            createlist.append(PickOrder.create_from_zort_order(
                request.user.company,
                order,
                commit=False)
            )

        PickOrder.objects.bulk_create(createlist)

        # file = 'file'

        # Create task
        # create_import_orders_task(company_id=request.user.company_id, filename=file)

        return Response('OK, your file is in the queue')


class CompanyUUIDField(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        return Company.objects.all()

    def to_internal_value(self, data):
        if self.pk_field is not None:
            data = self.pk_field.to_internal_value(data)
        try:
            return self.get_queryset().get(uuid=data)
        except Company.DoesNotExist:
            self.fail('does_not_exist', pk_value=data)
        except (TypeError, ValueError):
            self.fail('incorrect_type', data_type=type(data).__name__)


class GenericOrderImportAPI(APIView):
    class Validator(serializers.Serializer):
        filename = serializers.CharField(required=False)
        file = serializers.FileField(required=False)
        company = CompanyUUIDField()

        def validate(self, attrs):
            # validate require file or filename
            print(attrs)
            return super().validate(attrs)

        def get_file(self):
            file = self.validated_data.get('file')
            if file:
                return file

            filename = self.validated_data.get('filename')
            # load file from storage
            file = 'load file from storage'
            return file

    field_maps = {}
    default_sale_channels = 'unknown'
    default_status = 'Pending'

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)

        action = request.GET.get('action', 'import')
        company = validator.validated_data['company']
        file = validator.get_file()

        if request.user.company_id != company.id and not request.user.is_superuser:
            raise serializers.ValidationError('Unauthorized')

        if action == 'import':
            return self.import_orders(company, file)
        if action == 'upload':
            return self.upload_orders(company, file)

        raise serializers.ValidationError(
            'Invalid action, action must be `import` or `upload`')

    def upload_orders(self, request):
        pass

    def import_orders(self, company, file):
        df = pd.read_excel(file)
        # file_headers = list(df.columns.values)
        file_data = df.to_dict(orient='records')

        orders = self.rows_to_orders(file_data)

        createlist = []
        for order in orders:
            pick_order = PickOrder.create_from_zort_order(company, order, commit=False)
            createlist.append(pick_order)

        PickOrder.objects.bulk_create(createlist, batch_size=1000)

        # delete file

        return Response('Import complete')

    def rows_to_orders(self, rows):
        orders = {}
        for row in rows:
            temp_order = self.row_to_order(row)

            order = orders.get(temp_order['number'])
            if order:
                order['list'] = [*order['list'], *temp_order['list']]
            else:
                order_number = temp_order['number']
                orders[order_number] = temp_order

        serializer = ZortOrderSerializer(data=list(orders.values()), many=True)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def row_to_order(self, row):
        order = {
            "amount": self.get_field(row, 'amount'),
            "createdatetimeString": self.get_field(row, 'createdatetimeString', dtype='datetime'),
            "customeraddress": self.get_field(row, 'customeraddress', default=''),
            "customeremail": self.get_field(row, "customeremail", default=''),
            "customername": self.get_field(row, "customername"),
            "customerphone": self.get_field(row, "customerphone"),
            "description": self.get_field(row, "description", default=''),
            "discount": self.get_field(row, "discount", default='0'),
            "discountamount": self.get_field(row, 'discountamount', default=0),
            "isCOD": self.get_field(row, "isCOD", default=0),
            "warehousecode": self.get_field(row, "warehousecode", default="00000"),
            "list": [
                {
                    "sku": self.get_field(row, "list.sku"),
                    "name": self.get_field(row, "list.name"),
                    "number": self.get_field(row, "list.number"),
                    "unittext": self.get_field(row, "list.unittext", default=""),
                    "totalprice": self.get_field(row, "list.totalprice"),
                    "pricepernumber": self.get_field(row, 'list.pricepernumber')
                },
            ],
            "number": self.get_field(row, "number"),
            "orderdateString": self.get_field(row, "orderdateString", dtype='date'),
            "paymentamount": self.get_field(row, "paymentamount"),
            "paymentmethod": self.get_field(row, "paymentmethod"),
            "payments": [
                {
                    "id": self.get_field(row, "payments.id"),
                    "name": self.get_field(row, "payments.name"),
                    "amount": self.get_field(row, "payments.amount"),
                    "paymentdatetimeString": self.get_field(row, "payments.paymentdatetimeString", dtype='datetime')
                }
            ],
            "paymentstatus": self.get_field(row, "paymentstatus", default='Paid'),
            "platformdiscount": self.get_field(row, "platformdiscount", default=0),
            "saleschannel": self.get_field(row, "saleschannel", default=self.default_sale_channels),
            "sellerdiscount": self.get_field(row, "sellerdiscount", default=0),
            "shippingaddress": self.get_field(row, "shippingaddress"),
            "shippingamount": self.get_field(row, "shippingamount"),
            "shippingchannel": self.get_field(row, "shippingchannel"),
            "shippingdistrict": self.get_field(row, "shippingdistrict"),
            "shippingemail": self.get_field(row, "shippingemail", default=''),
            "shippingname": self.get_field(row, "shippingname"),
            "shippingphone": self.get_field(row, "shippingphone"),
            "shippingpostcode": self.get_field(row, "shippingpostcode", default=''),
            "shippingprovince": self.get_field(row, "shippingprovince", default=''),
            "shippingsubdistrict": self.get_field(row, "shippingsubdistrict", default=''),
            "shippingvat": self.get_field(row, "shippingvat", default=0),
            "status": self.get_field(row, "status", default=self.default_status),
            "trackingno": self.get_field(row, "trackingno"),
            "updatedatetimeString": self.get_field(row, "updatedatetimeString", dtype='datetime'),
            "vatamount": self.get_field(row, "vatamount", default=0),
            "vatpercent": self.get_field(row, "vatpercent", default=7),
            "vattype": self.get_field(row, "vattype", default=1),
            "voucheramount": self.get_field(row, "voucheramount", default=0)
        }

        if order['customername'] is None:
            order['customername'] = order['shippingname']
        if order['customerphone'] is None:
            order['customerphone'] = order['shippingphone']

        list0 = order['list'][0]
        if list0['pricepernumber'] is None:
            list0['pricepernumber'] = round(list0['totalprice'] / list0['number'], 2)
        if list0['totalprice'] is None:
            list0['totalprice'] = list0['pricepernumber'] * list0['number']

        payment0 = order['payments'][0]
        if payment0['amount'] is None:
            payment0['amount'] = order['paymentamount']

        return order

    def get_field(self, row, key, dtype=str, default=None):
        if key not in self.field_maps:
            return default

        value = row[self.field_maps[key]]

        if dtype == 'date':
            value = value.strftime('%Y-%m-%d')

        if dtype == 'datetime':
            value = value.strftime('%Y-%m-%d %H:%m')

        return value


class VRichOrderUploadAPI(GenericOrderImportAPI):
    field_maps = {
        'number': 'เลขที่ Order',
        'orderdateString': 'วันที่',
        'shippingname': 'ชื่อลูกค้า',
        'shippingaddress': 'ที่อยู่',
        'shippingdistrict': 'จังหวัด',
        'shippingpostcode': 'รหัสไปรษณีย์',
        'shippingphone': 'โทรศัพท์',
        'shippingchannel': 'ขนส่ง',
        'trackingno': 'เลขพัสดุ',
        # 'จำนวนทั้งหมด': '',
        # 'จำนวนเงิน': 'paymentamount',
        'discountamount': 'ส่วนลด',
        'shippingamount': 'ค่าส่ง',
        'amount': 'รวม',  # not sure
        'payments.name': 'เลขบัญชี',
        'paymentamount': 'โอนแล้ว',
        'payments.paymentdatetimeString': 'วัน-เวลาโอน',
        'createdatetimeString': 'วัน-เวลาโอน',
        'list.sku': 'รหัสสินค้า',
        'list.name': 'รายละเอียด',
        'list.number': 'จำนวน',
        'list.totalprice': 'ราคา',
    }
    default_sale_channels = 'VRICH'
