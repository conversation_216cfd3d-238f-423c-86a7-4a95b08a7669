
from rest_framework import serializers
from typing import Dict

from importdata.serializers import DateT<PERSON><PERSON>ield, DecimalField
from .generics import OrderUploadGeneric<PERSON><PERSON>, OrderImportGenericAPI


class BehindOrderSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    orderdateString = DateTimeField(input_formats=['%b %d, %Y, %I:%M %p'])
    saleschannel = serializers.CharField()
    shippingname = serializers.CharField(max_length=200)

    shippingaddress = serializers.CharField(allow_blank=True)
    shippingsubdistrict = serializers.CharField(allow_blank=True)
    shippingdistrict = serializers.CharField(allow_blank=True)
    shippingprovince = serializers.CharField(allow_blank=True)
    shippingpostcode = serializers.CharField(allow_blank=True)
    list__name = serializers.CharField()

    shippingphone = serializers.CharField(max_length=50)
    shippingchannel = serializers.CharField()
    trackingno = serializers.CharField(required=True, allow_blank=True)
    description = serializers.CharField(allow_blank=True)
    discountamount = DecimalField(max_digits=12, decimal_places=2)
    shippingamount = DecimalField(max_digits=12, decimal_places=2)
    amount = DecimalField(max_digits=12, decimal_places=2)


# หมายเลขคำสั่งซื้อ	วันที่สั่งซื้อ	สถานะ	ชื่อลูกค้า	ประเภทที่มาของลูกค้า	ชื่อโซเซียล	ที่อยู่	แขวง/ตำบล	เขต/อำเภอ	จังหวัด	รหัสไปรษณีย์	สถานะลูกค้า	เบอร์โทร	สินค้า	ส่วนลดรวม	ค่าจัดส่ง	ยอดรวม	ต้นทุน	วิธีการชำระเงิน	ธนาคารที่ชำระ	ชื่อบัญชี	เลขบัญชี	วันที่โอนเงิน	วิธีการจัดส่ง	หมายเลขพัสดุ	พนักงานขาย	หมายเหตุ
FIELD_MAPS = {
    'หมายเลขคำสั่งซื้อ': 'number',
    'วันที่สั่งซื้อ': 'orderdateString',
    'ประเภทที่มาของลูกค้า': 'saleschannel',
    'ชื่อลูกค้า': 'shippingname',

    'ที่อยู่': 'shippingaddress',
    'แขวง/ตำบล': 'shippingsubdistrict',
    'เขต/อำเภอ': 'shippingdistrict',
    'จังหวัด': 'shippingprovince',
    'รหัสไปรษณีย์': 'shippingpostcode',
    'สินค้า': 'list__name',

    'เบอร์โทร': 'shippingphone',
    'วิธีการจัดส่ง': 'shippingchannel',
    'หมายเลขพัสดุ': 'trackingno',
    'หมายเหตุ': 'description',
    'ส่วนลดรวม': 'discountamount',
    'ค่าจัดส่ง': 'shippingamount',
    'ยอดรวม': 'amount'
    # 'สินค้า': ''
}
IMPORT_TYPE = '002-behind'


class BehindPosOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = BehindOrderSerializer


class BehindPosOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = BehindOrderSerializer

    def get_order_address(self, order: Dict):
        return ' '.join([
            order['shippingaddress'],
            'แขวง/ตำบล', order['shippingsubdistrict'],
            'เขต/อำเภอ', order['shippingdistrict'],
            'จังหวัด', order['shippingprovince'],
            'รหัสไปรษณีย์่', order['shippingpostcode']
        ])

    def get_order_list(self, order: Dict):
        return [{
            'sku': '-',
            'name': order['list'][0]['name'],
            'number': 1,
            'pricepernumber': order['amount'],
            'totalprice': order['amount']
        }]
