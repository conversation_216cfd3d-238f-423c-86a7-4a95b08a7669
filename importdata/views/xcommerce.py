from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI
from utils.ignore_exception import ignore_exception

# หมายเลขคำสั่งซื้อ
# สถานะ
# พนง.
# ใบแจ้งหนี้
# การชำระเงิน
# ส่วนลด
# ยอดขาย
# ผู้ให้บริการ
# จำนวน
# ค่าจัดส่ง
# ค่า COD
# หมายเลขพัสดุ
# ประเภท
# ชื่อช่องทาง
# ชื่อ-สกุล
# เบอร์โทรศัพท์
# ที่อยู่
# ตำบล
# อำเภอ
# จังหวัด
# รหัสไปรษณีย์
# วันที่แพค
# วันที่สร้าง
# วันที่ชำระเงิน
# ส่วนลดร้านค้า
# ส่วนลดแพลตฟอร์ม
# เหรียญ
# รหัส SKU
# ชื่อสินค้า
# ตัวเลือกสินค้า (ถ้ามี)
# ราคาต่อชิ้น
# ส่วนลดต่อชิ้น
# จำนวนสินค้าตามรายการ
# หมายเหตุ

FIELD_MAPS = {
    'ชื่อช่องทาง': 'saleschannel',
    'หมายเลขคำสั่งซื้อ': 'number',
    'ชื่อ-สกุล': 'shippingname',
    'เบอร์โทรศัพท์': 'shippingphone',

    'หมายเลขพัสดุ': 'trackingno',
    'ที่อยู่': 'shippingaddress',
    'อำเภอ': 'shippingdistrict',
    'ตำบล': 'shippingsubdistrict',
    'จังหวัด': 'shippingprovince',
    'รหัสไปรษณีย์': 'shippingpostcode',
    'ผู้ให้บริการ': 'shippingchannel',
    'สถานะ': 'status',
    'ยอดขาย': 'paymentamount',
    'ส่วนลด': 'discountamount',
    'ค่าจัดส่ง': 'shippingamount',
    'รหัส SKU': 'list__sku',
    'ชื่อสินค้า': 'list__name',
    'จำนวนสินค้าตามรายการ': 'list__number',
    'ราคาต่อชิ้น': 'list__totalprice',
    'ส่วนลดต่อชิ้น': 'list__discountamount',
    'หมายเหตุ': 'remark',
}
STATUS_MAPS = {
    'ยังไม่ชำระเงิน': 'Pending',
    'ยืนยัน': 'Pending',
    'สำเร็จ': 'Success',
    'ยกเลิก': 'Voided'
}
TYPE = '011-xcommerce'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default

        if type(data) is str:
            data = data.replace(',', '')

        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)

    # Optional
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    status = serializers.CharField(required=False, allow_blank=True, default='')
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    shippingsubdistrict = serializers.CharField(
        default=None, allow_blank=True, allow_null=True)
    shippingdistrict = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    shippingprovince = serializers.CharField(default=None, allow_blank=True, allow_null=True)
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    list__discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    copy_upper_row_data = ['number', 'saleschannel', 'shippingname', 'shippingphone']
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    copy_upper_row_data = ['number', 'saleschannel', 'shippingname', 'shippingphone']
    serializer_class = OrderUploadSerializer

    # def row_to_order(self, row):
    #     order = super().row_to_order(row)
    #     # order['paymentamount'] = sum()
    #     order['amount'] = order['paymentamount']
    #     return order

    def post_process_orders(self, orders: Dict):
        results = {}
        for key, order in orders.items():
            order['status'] = STATUS_MAPS.get(order['status'], 'Pending')
            if order['status'] == 'Voided':
                continue

            order['amount'] = order['paymentamount']

            order['shippingpostcode'] = ignore_exception(
                lambda x: f'{float(x):.0f}')(
                order['shippingpostcode'],
                default=order['shippingpostcode'])

            order['shippingaddress'] = (
                f"{order['shippingaddress']}, "
                f"{order['shippingsubdistrict']}, {order['shippingdistrict']}, "
                f"{order['shippingprovince']} {order['shippingpostcode']}"
            )
            results[key] = order

        return results
