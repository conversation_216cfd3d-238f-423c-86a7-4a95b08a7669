from email.policy import default
from rest_framework import serializers
from .generics import OrderUploadGeneric<PERSON><PERSON>, OrderImportGenericAPI
from importdata.serializers import DateTimeField

# Excel Field: Dobybot Field
FIELD_MAPS = {
    # 'saleschannel': 'saleschannel',
    'เลขที่ใบสั่งซื้อ': 'number',
    'ชื่อผู้รับ': 'shippingname',
    'เบอร์โทรผู้รับ': 'shippingphone',
    'วันที่สั่งซื้อ': 'orderdateString',

    'Track code': 'trackingno',
    'ยอดรวมทั้งหมด': 'paymentamount',
    'ส่วนลดทั้งหมด': 'discountamount',
    'ที่อยู่ผู้รับ': 'shippingaddress',
    'จังหวัดผู้รับ': 'shippingprovince',
    'อำเภอ/เขต': 'shippingdistrict',
    'ตำบล/แขวง': 'shippingsubdistrict',
    'รหัสไปรษณีย์ผู้รับ': 'shippingpostcode',
    'อีเมล์': 'shippingemail',
    'ชำระเงินผ่าน': 'isCOD',
    # 'shippingchannel': 'shippingchannel',
    'ค่าจัดส่ง': 'shippingamount',
    'รหัสสินค้า': 'list__sku',
    'รายการสินค้า': 'list__name',
    'จำนวน': 'list__number',
    'ราคา': 'list__totalprice',
    'remark': 'remark',
}
TYPE = '031-make-web-easy'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    orderdateString = DateTimeField(input_formats=['%d/%m/%Y %H:%M'])

    # Optional
    isCOD = serializers.CharField(required=False, allow_blank=True)
    saleschannel = serializers.CharField(max_length=400, default="Make Web Easy")
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    shippingprovince = serializers.CharField(required=False, allow_blank=True, default='')
    shippingdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingsubdistrict = serializers.CharField(required=False, allow_blank=True, default='')
    shippingpostcode = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = ['number', 'shippingname',
                           'shippingphone', 'paymentamount', 'orderdateString']
    converters = {'เบอร์โทรผู้รับ': str}


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = ['number', 'shippingname',
                           'shippingphone', 'paymentamount', 'orderdateString']
    converters = {'เบอร์โทรผู้รับ': str}

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['amount'] = order['paymentamount']
        if order['isCOD'] == 'cod':
            order['isCOD'] = True
        else :
            order['isCOD'] = False
        return order
