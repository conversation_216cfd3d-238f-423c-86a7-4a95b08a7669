
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'Source': 'saleschannel',
    'No.': 'number',
    'Customer Name': 'shippingname',
    'Customer Phone': 'shippingphone',

    'Tracking Number': 'trackingno',
    'Total': 'paymentamount',
    'Discount': 'discountamount',
    'Customer Address': 'shippingaddress',
    'Shipping Option': 'shippingchannel',
    'Shipping Cost': 'shippingamount',
    'SKU': 'list__sku',
    'Item Name': 'list__name',  # Variant Name, Item Note
    'Variant Name': 'list__name_variant',
    'Item Note': 'list__name_note',
    'Item Qty': 'list__number',
    'Item Subtotal': 'list__totalprice',
    'remark': 'remark',
}
TYPE = '020-page365'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400)
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.CharField(max_length=400)
    paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__name_variant = serializers.CharField(allow_blank=True, default='')
    list__name_note = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer

    def row_to_order(self, row):
        row['list__name'] = f'{row["list__name"]} ({row["list__name_variant"]}) [note: {row["list__name_note"]}]'
        order = super().row_to_order(row)

        order['amount'] = order['paymentamount']
        return order
