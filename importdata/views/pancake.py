from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'รหัสการสั่งซื้อ': 'number',
    'ลูกค้า': 'shippingname',
    'เบอร์โทรศัพท์': 'shippingphone',

    'เลขพัสดุ': 'trackingno',
    'ที่อยู่': 'shippingaddress',
    'บริษัทขนส่ง': 'shippingchannel',
    'รหัสสินค้า': 'list__sku',
    'รายละเอียดสินค้า': 'list__name',
    'จำนวน': 'list__number',
}
TYPE = '034-pancake'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            data = self.default
        return super().to_internal_value(data)


class OrderUploadSerializer(serializers.Serializer):
    # Required
    saleschannel = serializers.CharField(max_length=400, default="Pancake")
    number = serializers.CharField(max_length=100)
    shippingname = serializers.CharField(max_length=400)
    shippingphone = serializers.Char<PERSON>ield(max_length=400)
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    # shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(
        max_digits=12, decimal_places=2, default=1, required=False)
    # list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # remark = serializers.CharField(required=False, allow_blank=True, default='')


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'รหัสการสั่งซื้อ': str}
    copy_upper_row_data = ['number', 'shippingname', 'shippingphone']


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    converters = {'รหัสการสั่งซื้อ': str}
    copy_upper_row_data = ['number', 'shippingname', 'shippingphone']

    # def row_to_order(self, row):
    #     order = super().row_to_order(row)
    #     order['amount'] = order['paymentamount']
    #     return order
