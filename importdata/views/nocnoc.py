from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI

# Excel Field: Dobybot Field
FIELD_MAPS = {
    'หมายเลขคำสั่งซื้อ': 'number',
    # 'เลขที่ใบสั่งซื้อ': 'invoice_number',
    'ชื่อลูกค้า': 'shippingname',
    'หมายเลขโทรศัพท์': 'shippingphone',

    'หมายเลขติดตามพัศดุ': 'trackingno',
    # 'ส่วนลดค่าสินค้า': 'discountamount',
    'ที่อยู่สำหรับจัดส่ง': 'shippingaddress',
    'ผู้ให้บริการจัดส่ง': 'shippingchannel',
    'ค่าจัดส่ง (ลูกค้าเป็นผู้ชำระ)': 'shippingamount',
    'หมายเลข SKU': 'list__sku',
    'ชื่อสินค้า': 'list__name',
    'จำนวนสินค้า': 'list__number',
    'ราคาขายสุทธิ': 'list__totalprice',
    # 'ส่วนลดค่าสินค้า': 'list__discountamount',
}
TYPE = '022-nocnoc'


class BlankableDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if type(data) == str:
            data = data.replace(',', '')
            data = float(data)

        data = round(data, 2)

        if data == '':
            data = self.default

        return super().to_internal_value(data)


class NocnocOrderUploadSerializer(serializers.Serializer):
    # Required
    shippingphone = serializers.CharField(max_length=400)
    saleschannel = serializers.CharField(max_length=400, default='Nocnoc')

    number = serializers.CharField(max_length=100, allow_blank=True)
    shippingname = serializers.CharField(max_length=400, allow_blank=True)
    # invoice_number = serializers.CharField(max_length=100, allow_blank=True, default='')
    # paymentamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)

    # Optional
    shippingchannel = serializers.CharField(required=False, allow_blank=True)
    trackingno = serializers.CharField(required=False, allow_blank=True)
    shippingamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # shippingemail = serializers.CharField(max_length=200, allow_blank=True, default="")
    # amount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    discountamount = BlankableDecimalField(max_digits=20, decimal_places=10, default=0)
    shippingaddress = serializers.CharField(required=False, allow_blank=True, default='')
    list__sku = serializers.CharField(allow_blank=True, default='')
    list__name = serializers.CharField(allow_blank=True, default='')
    list__number = BlankableDecimalField(max_digits=12, decimal_places=2, default=1, required=False)
    list__totalprice = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    # list__discountamount = BlankableDecimalField(max_digits=12, decimal_places=2, default=0)
    remark = serializers.CharField(required=False, allow_blank=True, default='')


class NocnocOrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = NocnocOrderUploadSerializer


class NocnocOrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = TYPE
    field_maps = FIELD_MAPS
    serializer_class = NocnocOrderUploadSerializer

    # def row_to_order(self, row):
    #     order = super().row_to_order(row)
    #     # sum total price from items
    #     # order['discountamount'] = round(row['discountamount'])

    def post_process_orders(self, orders: Dict):
        for order_number, order in orders.items():
            total_items_price = sum([item['totalprice'] for item in order['list']])
            total_discount = sum([item['discountamount'] for item in order['list']])
            order['amount'] = round(total_items_price - total_discount, 2) + order['shippingamount']
            order['paymentamount'] = order['amount']
            order['discountamount'] = 0
        return orders
