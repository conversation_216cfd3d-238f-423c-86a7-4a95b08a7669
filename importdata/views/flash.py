import re

from rest_framework import serializers
from typing import Dict

from importdata.serializers import Date<PERSON><PERSON><PERSON><PERSON>, DecimalField
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


class FlashOrderSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    orderdateString = DateTimeField()
    shippingname = serializers.CharField(max_length=200)
    shippingphone = serializers.CharField(max_length=50)
    shippingaddress = serializers.Char<PERSON>ield(allow_blank=True)
    list__name = serializers.CharField(allow_blank=True, default="")


FIELD_MAPS = {
    'เลขพัสดุ': 'number',  # TH270845FWFD4F
    'เวลาสร้าง': 'orderdateString',  # 2023-05-27 09:02:53
    'ชื่อผู้รับ': 'shippingname',  # คุณทิวา กองขุนทด/สติ๊กเกอร์ฮีโร่
    'เบอร์ผู้รับ': 'shippingphone',  # 0943625299
    'ที่อยู่ที่รับ': 'shippingaddress',  # noqa 85/1 ม.5 ตำบลบ้านเก่า อำเภอด่านขุนทด จังหวัดนครราชสีมา 30210 บ้านเก่า ด่านขุนทด นครราชสีมา 30210
    'หมายเหตุ': 'list__name',  # (ค้อน) (1), (มือ) (1)
}
IMPORT_TYPE = '015-flash'


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = FlashOrderSerializer


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """
    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = FlashOrderSerializer

    def row_to_order(self, row):
        order = super().row_to_order(row)
        order['shippingchannel'] = 'Flash Express'
        order['trackingno'] = order['number']
        return order

    def get_order_list(self, order: Dict):
        # order['list'][0]['name'] = (ค้อน) (1), (มือ) (1)
        list_str = order['list'][0]['name']

        items = []
        for item_str in list_str.split(','):
            m = re.search(r"\((.*?)\) \((.*?)\)", item_str)
            name = m.group(1)
            number = m.group(2)
            items.append({
                'sku': name,
                'name': name,
                'number': number,
                'pricepernumber': 0,
                'totalprice': 0
            })

        return items
