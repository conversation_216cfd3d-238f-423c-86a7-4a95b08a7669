from typing import Dict
from rest_framework import serializers
from .generics import OrderUploadGenericAPI, OrderImportGenericAPI


FIELD_MAPS = {
    "ชื่อลูกค้า": "number",
    "เบอร์โทร.": "shippingphone",
    "ที่อยู่": "shippingaddress",
    "เลขที่บิล": "list__sku",
    "รหัสสินค้า": "list__name",
    "เลขพัสดุ": "trackingno",
    "ขนส่ง": "shippingchannel",
}
IMPORT_TYPE = "029-ddg-jewelry-2"


class OrderUploadSerializer(serializers.Serializer):
    number = serializers.CharField(max_length=50)
    trackingno = serializers.CharField(allow_blank=True, default="")
    shippingname = serializers.CharField(max_length=200, allow_blank=True, default="")
    shippingphone = serializers.CharField(max_length=50, allow_blank=True, default="")
    shippingaddress = serializers.Char<PERSON>ield(allow_blank=True, default="")
    shippingchannel = serializers.CharField(allow_blank=True, default="")
    list__sku = serializers.Char<PERSON><PERSON>(max_length=200)
    list__name = serializers.Char<PERSON>ield(max_length=200, allow_blank=True, default="")


class OrderUploadAPI(OrderUploadGenericAPI):
    """
    Validate file and upload to google cloud storage, queue for importing
    """

    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = ["number"]


class OrderImportAPI(OrderImportGenericAPI):
    """
    Import orders to database
    """

    import_type = IMPORT_TYPE
    field_maps = FIELD_MAPS
    serializer_class = OrderUploadSerializer
    copy_upper_row_data = ["number"]

    def merge_order(self, order, temp_order):
        order["shippingaddress"] += " " + temp_order["shippingaddress"]
        return order

    def post_process_orders(self, orders: Dict):
        result = {}

        for order in orders.values():
            order["shippingname"] = order["number"]
            order["customername"] = order["shippingname"]
            order["number"] = order["list"][0]["sku"]
            result[order["number"]] = order

        return result
