from difflib import restore
import os
from unittest import skip

from django.test import tag, TestCase
from companies.models import Company
from importdata.models import OrderImportRequest

from core.tests.testutils import Test<PERSON>ase<PERSON>elper, TestClient
from core.tests import setup
from users.models import User
from picking.models import PickOrder


class SimpleOrderUploadTestCase(TestCase, TestCaseHelper):
    def setUp(self) -> None:
        result = setup.init()
        self.company: Company = result['company']
        self.user: User = result['user']

        self.client = TestClient()
        self.client.login("admin")

    @tag('upload-order')
    def test_upload_simple_order(self):
        testfile = os.path.join(os.path.dirname(__file__), "data/simple-order.xlsx")
        resultfile = os.path.join(os.path.dirname(__file__), "result/simple-order-error.xlsx")
        with open(testfile, "rb") as file:
            response = self.client.post(
                "/api/importdata/orders/upload/001-simple/",
                {"file": file}
            )

        if response.status_code == 400:
            print(response.json())

        if response.status_code == 422:
            with open(resultfile, "wb") as file:
                for b in response.streaming_content:
                    file.write(b)

        self.assertEqual(response.status_code, 200)
        data = response.json()
        oi_request = OrderImportRequest.objects.get(id=data['id'])
        self.assertIsNotNone(oi_request.file)

    @tag('import-order')
    @skip('To be fixed')
    def test_import_simple_order(self):
        # Upload File
        testfile = os.path.join(os.path.dirname(__file__), "data/simple-order.xlsx")
        with open(testfile, "rb") as file:
            response = self.client.post(
                "/api/importdata/orders/upload/001-simple/",
                {"file": file}
            )

        if response.status_code == 400:
            print(response.json())

        self.assertEqual(response.status_code, 200)

        response_data = response.json()

        # Import File
        response = self.client.post(
            '/api/importdata/orders/import/001-simple/',
            data={'id': response_data['id']})

        if response.status_code == 400:
            print(response.json())

        self.assertEqual(response.status_code, 200)

        pick_orders = PickOrder.objects.all()
        self.assertEqual(pick_orders.count(), 5)

        pick_order = pick_orders[0]
        self.assertEqual(pick_order.order_number, 'TEST000001')
        self.assertEqual(pick_order.order_trackingno, 'SHP0000001')
        self.assertEqual(pick_order.order_customer, 'apple')
        self.assertEqual(pick_order.order_customerphone, '************')

    @tag('upload-order')
    @skip('To be fixed')
    def test_upload_simple_order_validation(self):
        # TODO: complete this test, add assertions
        testfile = os.path.join(os.path.dirname(__file__), "data/simple-order.xlsx")
        resultfile = os.path.join(os.path.dirname(__file__), "result/simple-order.xlsx")
        with open(testfile, "rb") as file:
            response = self.client.post(
                "/api/picking/orders/upload/101/",
                {"file": file}
            )

            print('-' * 100)
            print(response)
            with open(resultfile, "wb") as file:
                for b in response.streaming_content:
                    file.write(b)
