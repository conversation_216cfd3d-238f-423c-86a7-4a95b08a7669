from django.urls import path

from importdata.views import nocnoc
from .views import (
    flattwhite,
    rocketstar,
    simple,
    apis,
    behind,
    eight_studio_brand,
    jtexpress,
    ninjavan,
    quickerbox,
    kimcorp,
    fresh_commerce,
    full,
    full_etax,
    realstore,
    gosell,
    xcommerce,
    my_order,
    shipnity,
    shipnity2,
    shipnity3,
    shipnity4,
    morning_dworld,
    flash,
    pawdy_xcommerce,
    pawdy_gosell,
    pawdy_gosell2,
    zwizshop,
    page365,
    commerzy,
    commerzy2,
    jsterp,
    jsterp2,
    jsterp3,
    jsterp4,
    jsterp5,
    jsterp6,
    wang_nam_khiao,
    gosell2,
    shippop,
    livplus,
    ddg_jewelry,
    ddg_jewelry2,
    shein,
    make_web_easy,
    shopify,
    srichan_amado,
    pancake,
    e_count,
    somphop_liquor_house,
    jambolive,
    jambolive2,
    smallplay_intertoy,
    wellnesssys,
    etax_charoensin,
    easy_etax,
    duangtawanpetch
)

from dynamic_rest.routers import DynamicRouter

router = DynamicRouter()
router.register("resource/order-import-requests/", apis.OrderImportRequestViewSet)

# /api/importdata/orders/upload/001-full-etax/
# /api/importdata/
urlpatterns = router.urls + [
    path("import-types/", apis.ImportTypeListAPIView.as_view()),
    path("orders/upload/001-simple/", simple.SimpleOrderUploadAPI.as_view()),
    path("orders/import/001-simple/", simple.SimpleOrderImportAPI.as_view()),
    path("orders/upload/001-full/", full.FullOrderUploadAPI.as_view()),
    path("orders/import/001-full/", full.FullOrderImportAPI.as_view()),
    path("orders/upload/001-full-etax/", full_etax.OrderUploadAPI.as_view()),
    path("orders/import/001-full-etax/", full_etax.OrderImportAPI.as_view()),
    path("orders/upload/001-easy-etax/", easy_etax.OrderUploadAPI.as_view()),
    path("orders/import/001-easy-etax/", easy_etax.OrderImportAPI.as_view()),
    # JP Miracle, Behind POS
    path("orders/upload/002-behind/", behind.BehindPosOrderUploadAPI.as_view()),
    path("orders/import/002-behind/", behind.BehindPosOrderImportAPI.as_view()),
    # 8Studio Brand
    path(
        "orders/upload/003-eight-studio-brand/",
        eight_studio_brand.EightStudioBrandOrderUploadAPI.as_view(),
    ),
    path(
        "orders/import/003-eight-studio-brand/",
        eight_studio_brand.EightStudioBrandOrderImportAPI.as_view(),
    ),
    # J&T Express
    path("orders/upload/004-jtexpress/", jtexpress.JTExpressOrderUploadAPI.as_view()),
    path("orders/import/004-jtexpress/", jtexpress.JTExpressOrderImportAPI.as_view()),
    # Ninja Van
    path("orders/upload/005-ninjavan/", ninjavan.NinjaVanOrderUploadAPI.as_view()),
    path("orders/import/005-ninjavan/", ninjavan.NinjaVanOrderImportAPI.as_view()),
    # Quicker Box
    path(
        "orders/upload/006-quickerbox/", quickerbox.QuickerBoxOrderUploadAPI.as_view()
    ),
    path(
        "orders/import/006-quickerbox/", quickerbox.QuickerBoxOrderImportAPI.as_view()
    ),
    # Kimcorp
    path("orders/upload/007-kimcorp/", kimcorp.KimCorpOrderUploadAPI.as_view()),
    path("orders/import/007-kimcorp/", kimcorp.KimCorpOrderImportAPI.as_view()),
    # Fresh Commerce, 008-freshcommerce
    path("orders/upload/008-freshcommerce/", fresh_commerce.OrderUploadAPI.as_view()),
    path("orders/import/008-freshcommerce/", fresh_commerce.OrderImportAPI.as_view()),
    # Real store
    path("orders/upload/009-realstore/", realstore.OrderUploadAPI.as_view()),
    path("orders/import/009-realstore/", realstore.OrderImportAPI.as_view()),
    # Gosell (pawdy)
    path("orders/upload/010-gosell/", gosell.OrderUploadAPI.as_view()),
    path("orders/import/010-gosell/", gosell.OrderImportAPI.as_view()),
    # x commerce (pawdy)
    path("orders/upload/011-xcommerce/", xcommerce.OrderUploadAPI.as_view()),
    path("orders/import/011-xcommerce/", xcommerce.OrderImportAPI.as_view()),
    # MyOrder
    path("orders/upload/012-my-order/", my_order.OrderUploadAPI.as_view()),
    path("orders/import/012-my-order/", my_order.OrderImportAPI.as_view()),
    # Shipnity
    path("orders/upload/013-shipnity/", shipnity.OrderUploadAPI.as_view()),
    path("orders/import/013-shipnity/", shipnity.OrderImportAPI.as_view()),
    path("orders/upload/013-shipnity-2/", shipnity2.OrderUploadAPI.as_view()),
    path("orders/import/013-shipnity-2/", shipnity2.OrderImportAPI.as_view()),
    path("orders/upload/013-shipnity-3/", shipnity3.OrderUploadAPI.as_view()),
    path("orders/import/013-shipnity-3/", shipnity3.OrderImportAPI.as_view()),
    path("orders/upload/013-shipnity-4/", shipnity4.OrderUploadAPI.as_view()),
    path("orders/import/013-shipnity-4/", shipnity4.OrderImportAPI.as_view()),
    # Morning DWorld
    path("orders/upload/014-morning-dworld/", morning_dworld.OrderUploadAPI.as_view()),
    path("orders/import/014-morning-dworld/", morning_dworld.OrderImportAPI.as_view()),
    # Flash
    path("orders/upload/015-flash/", flash.OrderUploadAPI.as_view()),
    path("orders/import/015-flash/", flash.OrderImportAPI.as_view()),
    # Pawdy (xcommerce) สั่งทำ
    path(
        "orders/upload/016-ship-offline-xcommerce/",
        pawdy_xcommerce.OrderUploadAPI.as_view(),
    ),
    path(
        "orders/import/016-ship-offline-xcommerce/",
        pawdy_xcommerce.OrderImportAPI.as_view(),
    ),
    # Pawdy (gosell) สั่งทำ
    path(
        "orders/upload/017-ship-offline-gosell/", pawdy_gosell.OrderUploadAPI.as_view()
    ),
    path(
        "orders/import/017-ship-offline-gosell/", pawdy_gosell.OrderImportAPI.as_view()
    ),
    # Pawdy (gosell) สั่งทำ 2 (gosell เปลี่ยน template)
    path(
        "orders/upload/018-ship-offline-gosell-2/",
        pawdy_gosell2.OrderUploadAPI.as_view(),
    ),
    path(
        "orders/import/018-ship-offline-gosell-2/",
        pawdy_gosell2.OrderImportAPI.as_view(),
    ),
    # Zwizshop
    path("orders/upload/019-zwizshop/", zwizshop.OrderUploadAPI.as_view()),
    path("orders/import/019-zwizshop/", zwizshop.OrderImportAPI.as_view()),
    # Page365
    path("orders/upload/020-page365/", page365.OrderUploadAPI.as_view()),
    path("orders/import/020-page365/", page365.OrderImportAPI.as_view()),
    # Commerzy
    path("orders/upload/021-commerzy/", commerzy.CommerzyOrderUploadAPI.as_view()),
    path("orders/import/021-commerzy/", commerzy.CommerzyOrderImportAPI.as_view()),
    path("orders/upload/021-commerzy-2/", commerzy2.CommerzyOrderUploadAPI.as_view()),
    path("orders/import/021-commerzy-2/", commerzy2.CommerzyOrderImportAPI.as_view()),
    # Nocnoc
    path("orders/upload/022-nocnoc/", nocnoc.NocnocOrderUploadAPI.as_view()),
    path("orders/import/022-nocnoc/", nocnoc.NocnocOrderImportAPI.as_view()),
    # JST-ERP
    path("orders/upload/023-jst-erp/", jsterp.OrderUploadAPI.as_view()),
    path("orders/import/023-jst-erp/", jsterp.OrderImportAPI.as_view()),
    path("orders/upload/023-jst-erp-2/", jsterp2.OrderUploadAPI.as_view()),
    path("orders/import/023-jst-erp-2/", jsterp2.OrderImportAPI.as_view()),
    path("orders/upload/023-jst-erp-3/", jsterp3.OrderUploadAPI.as_view()),
    path("orders/import/023-jst-erp-3/", jsterp3.OrderImportAPI.as_view()),
    path("orders/upload/023-jst-erp-4/", jsterp4.OrderUploadAPI.as_view()),
    path("orders/import/023-jst-erp-4/", jsterp4.OrderImportAPI.as_view()),
    path("orders/upload/023-jst-erp-5/", jsterp5.OrderUploadAPI.as_view()),
    path("orders/import/023-jst-erp-5/", jsterp5.OrderImportAPI.as_view()),
    path("orders/upload/023-jst-erp-6/", jsterp6.OrderUploadAPI.as_view()),
    path("orders/import/023-jst-erp-6/", jsterp6.OrderImportAPI.as_view()),

    # WANGNAMKHIAO FARM INTER
    path("orders/upload/024-wang-nam-khiao/", wang_nam_khiao.OrderUploadAPI.as_view()),
    path("orders/import/024-wang-nam-khiao/", wang_nam_khiao.OrderImportAPI.as_view()),
    # Go Sell 2
    path("orders/upload/025-gosell-2/", gosell2.OrderUploadAPI.as_view()),
    path("orders/import/025-gosell-2/", gosell2.OrderImportAPI.as_view()),
    # Shippop
    path("orders/upload/026-shippop/", shippop.OrderUploadAPI.as_view()),
    path("orders/import/026-shippop/", shippop.OrderImportAPI.as_view()),
    # livplus
    path("orders/upload/027-livplus/", livplus.OrderUploadAPI.as_view()),
    path("orders/import/027-livplus/", livplus.OrderImportAPI.as_view()),
    # DDG JEWELRY
    path("orders/upload/028-ddg-jewelry/", ddg_jewelry.OrderUploadAPI.as_view()),
    path("orders/import/028-ddg-jewelry/", ddg_jewelry.OrderImportAPI.as_view()),
    path("orders/upload/029-ddg-jewelry-2/", ddg_jewelry2.OrderUploadAPI.as_view()),
    path("orders/import/029-ddg-jewelry-2/", ddg_jewelry2.OrderImportAPI.as_view()),
    # SHEIN
    path("orders/upload/030-shein/", shein.OrderUploadAPI.as_view()),
    path("orders/import/030-shein/", shein.OrderImportAPI.as_view()),
    # Make Web Easy
    path("orders/upload/031-make-web-easy/", make_web_easy.OrderUploadAPI.as_view()),
    path("orders/import/031-make-web-easy/", make_web_easy.OrderImportAPI.as_view()),
    # Shopify
    path("orders/upload/032-shopify/", shopify.OrderUploadAPI.as_view()),
    path("orders/import/032-shopify/", shopify.OrderImportAPI.as_view()),
    # Srichan-amado
    path("orders/upload/033-srichan-amado/", srichan_amado.OrderUploadAPI.as_view()),
    path("orders/import/033-srichan-amado/", srichan_amado.OrderImportAPI.as_view()),
    # Pancake
    path("orders/upload/034-pancake/", pancake.OrderUploadAPI.as_view()),
    path("orders/import/034-pancake/", pancake.OrderImportAPI.as_view()),
    # E-count
    path("orders/upload/035-e-count/", e_count.OrderUploadAPI.as_view()),
    path("orders/import/035-e-count/", e_count.OrderImportAPI.as_view()),
    # SOMPHOP LIQUOR HOUSE
    path(
        "orders/upload/036-somphop-liquor-house/",
        somphop_liquor_house.OrderUploadAPI.as_view(),
    ),
    path(
        "orders/import/036-somphop-liquor-house/",
        somphop_liquor_house.OrderImportAPI.as_view(),
    ),
    # JAMBOLIVE
    path("orders/upload/037-jambolive/", jambolive.OrderUploadAPI.as_view()),
    path("orders/import/037-jambolive/", jambolive.OrderImportAPI.as_view()),
    path("orders/upload/037-jambolive-2/", jambolive2.OrderUploadAPI.as_view()),
    path("orders/import/037-jambolive-2/", jambolive2.OrderImportAPI.as_view()),
    # Smallplay Intertory 
    path("orders/upload/038-smallplay-intertoy/", smallplay_intertoy.OrderUploadAPI.as_view()),
    path("orders/import/038-smallplay-intertoy/", smallplay_intertoy.OrderImportAPI.as_view()),
    #  
    path("orders/upload/039-wellnesssys/", wellnesssys.OrderUploadAPI.as_view()),
    path("orders/import/039-wellnesssys/", wellnesssys.OrderImportAPI.as_view()),
    # Rocketstar
    path("orders/upload/040-rocketstar/", rocketstar.OrderUploadAPI.as_view()),
    path("orders/import/040-rocketstar/", rocketstar.OrderImportAPI.as_view()),
    # Flat White
    path("orders/upload/041-flattwhite/", flattwhite.FullOrderUploadAPI.as_view()),
    path("orders/import/041-flattwhite/", flattwhite.FullOrderImportAPI.as_view()),
    # etax - บริษัท เจริญสิน กาญจนบุรี เทรดดิ้ง จำกัด
    path(
        "orders/upload/042-etax-charoensin/", etax_charoensin.OrderUploadAPI.as_view()
    ),
    path(
        "orders/import/042-etax-charoensin/", etax_charoensin.OrderImportAPI.as_view()
    ),
    path(
        "orders/upload/043-duangtawanpech/", duangtawanpetch.OrderUploadAPI.as_view()
    ),
    path(
        "orders/import/043-duangtawanpech/", duangtawanpetch.OrderImportAPI.as_view()
    ),
]
