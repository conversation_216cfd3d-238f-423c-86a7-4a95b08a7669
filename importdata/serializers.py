from rest_framework import serializers
from importdata.models import OrderImportOptions, OrderImportRequest
from dynamic_rest.serializers import DynamicModelSerializer
from picking.serializers.zort import ZortOrderSerializer

from users.serializers import SimpleUserSerializer


class DecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if type(data) is str:
            data = data.replace(",", "")
        return super().to_internal_value(data)


class DateTimeField(serializers.DateTimeField):
    def to_internal_value(self, value):
        return super().to_internal_value(value)


class OrderImportOptionSerializer(serializers.Serializer):
    do_not_update_tracking = serializers.BooleanField(default=False)

    def to_representation(self, instance):
        if isinstance(instance, OrderImportOptions):
            return instance.model_dump()

        return instance


class OrderImportRequestSerializer(DynamicModelSerializer):
    create_by = SimpleUserSerializer(embed=True, read_only=True)
    options = OrderImportOptionSerializer()

    class Meta:
        model = OrderImportRequest
        fields = [
            "id",
            "company",
            "file",
            "create_date",
            "create_by",
            "status",
            "log",
            "options",
        ]


class CharIntegerField(serializers.IntegerField):
    def to_internal_value(self, data):
        if data == "":
            return None
        return serializers.IntegerField.to_internal_value(self, data)


class CharDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == "":
            return None
        return serializers.DecimalField.to_internal_value(self, data)


class CharBooleanField(serializers.BooleanField):
    TRUE_VALUES = {"1", "true", "yes", "y", True, 1}
    FALSE_VALUES = {"0", "false", "no", "n", False, 0}

    def to_internal_value(self, data):
        if isinstance(data, str):
            data = data.strip().lower()

        if data in self.TRUE_VALUES:
            return True
        if data in self.FALSE_VALUES:
            return False

        self.fail("invalid", input=data)
