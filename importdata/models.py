from typing import Union
from django.db import models
from django.utils import timezone
from pydantic import BaseModel


def order_imports_directory(instance, filename):
    today = timezone.localdate().strftime('%Y-%m-%d')
    return f"order-imports/{instance.company_id}/{today}/{filename}"




class OrderImportOptions(BaseModel):
    do_not_update_tracking: bool = False

    @staticmethod
    def default():
        return OrderImportOptions().model_dump()

class OrderImportRequest(models.Model):
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE)
    file = models.FileField(upload_to=order_imports_directory)
    create_date = models.DateTimeField(auto_now=True)
    create_by = models.ForeignKey('users.User', on_delete=models.PROTECT)
    _options = models.JSONField(default=OrderImportOptions.default)

    IN_QUEUE = 'in-queue'
    PROCESSING = 'processing'
    SUCCESS = 'success'
    ERROR = 'error'
    STATUSES = [
        (IN_QUEUE, "In Queue"),
        (PROCESSING, "Processing"),
        (SUCCESS, "Success"),
        (ERROR, "Error"),
    ]
    status = models.Char<PERSON>ield(max_length=20, choices=STATUSES, default=IN_QUEUE)
    log = models.TextField(blank=True, default="")

    def writelog(self, text, prepend_timestamp=True, commit=True):
        if prepend_timestamp:
            text = f"[{timezone.localtime().strftime('%Y-%m-%d %H:%M:%S')}] {text}"

        if self.log:
            text = '\n' + text

        self.log += text
        self.save()
    
    @property
    def options(self):
        return OrderImportOptions(**self._options)
    
    @options.setter
    def options(self, value: Union[OrderImportOptions, dict]):
        if isinstance(value, OrderImportOptions):
            self._options = value.model_dump()
        else:
            self._options = value
