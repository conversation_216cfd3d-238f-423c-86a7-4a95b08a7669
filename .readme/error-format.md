## Error Status
200 = OK
201 = Created
204 = No Content

400 = Bad Request, Validation Error
404 = Not Found


## Validation error from Serializer

```json
{
    "key1": ["error1", "error2"],
    "key2": ["error1", "error2"],
    "non_field_errors": [
        {"code": "CODE", "message": ""}
    ]
}
```

## Custom error
```json
{
    "non_fields_error": [
        {"code": "CODE", "message": ""}
    ]
}
```

## Utilities

```python
from core.responses import ResponseError

class APIView(...):
    def post(self, request):
        return ResponseError(code, message)
```

