# Setup Dobybot for Windows 10/11 without WSL

## Requirements
- python 3.11, 3.12
- PostgreSQL Database

## Steps
```
git clone ...

# Create virtual env
virtualenv .venv

# Activate virtual env
.venv/Scripts/activate

# Install dependencies
pip install -r requirements-py3.12.txt

# Setup .env, copy .env.example into .env then open file .env and find "TODO:""
cp .env.example .env

# Migrate database
python manage.py migrate

# Runserver
python manage.py runserver

```