Requirements
- WSL
- Docker
- Pyenv
- NVM
- Virtualenv


```bash
sudo apt install libpq-dev weasyprint cmake libpoppler-cpp-dev poppler-utils

# Clone project
git clone ...
cd dobybot

# Setup .env file
cp .example.env .env # then change all TODO:

# setup virtualenv
virtualenv .venv
source virtualenv .venv
(.venv) pip install -r requirements.txt

# Migrate
(.venv) python manage.py migrate

# Setup First User & Company
(.venv) python manage.py createuser

# Run server
(.venv) python manage.py runserver 0:8000
# visit http://localhost:8000/admin
```