Requirements
- Mac<PERSON>
- Docker
- Pyenv
- Virtualenv
- Python 3.9

```bash
brew install ...
brew install poppler
brew install weasyprint

git clone ...
cd dobybot

pyenv install 3.9
pyenv shell 3.9
pip install virtualenv

virtualenv .venv
source .venv/bin/activate

# Install pip requirements
(.venv) pip install -r requirements.txt

# Install everything in folder .python-lib
(.venv) pip install .python-lib/django_ez_report-1.2.4-py3-none-any.whl

# Setup .env file, copy from old computer
cp .env.example .env

# Setup Database (Using dbeaver to import db)

# Runserver
python manage.py runserver
```