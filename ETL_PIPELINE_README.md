# ETL Pipeline to BigQuery

This document describes the ETL (Extract, Transform, Load) pipeline that exports order and order item data to BigQuery tables, updating every hour via Cloud Scheduler.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Cloud Scheduler │───▶│ Django API       │───▶│ Cloud Tasks     │───▶│ ETL Service     │
│ (Hourly)        │    │ /api/etl/export/ │    │ (Background)    │    │ (CSV Export)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
                                                                                │
                                                                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ BigQuery Tables │◀───│ Cloud Function   │◀───│ GCS Bucket      │◀───│ CSV Files       │
│ - orders        │    │ (Auto-trigger)   │    │ (File Upload)   │    │ - orders.csv    │
│ - order_items   │    │                  │    │                 │    │ - order_items.csv│
└─────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components

### 1. Django ETL App (`etl/`)
- **Models**: `ETLJob` - Tracks ETL job executions
- **Services**: `ETLService`, `BigQueryService`, `CSVExportService`
- **Views**: API endpoints for triggering and monitoring ETL jobs
- **Management Commands**: Manual ETL execution

### 2. Cloud Function (`cloud_functions/bigquery_loader/`)
- Automatically triggered when CSV files are uploaded to GCS
- Loads CSV data into BigQuery tables
- Creates datasets and tables if they don't exist

### 3. Cloud Scheduler
- Triggers ETL export every hour
- Calls Django API endpoint to start the process

### 4. BigQuery Schema
- **Dataset**: `dobybot_company_{company_id}` (per company)
- **Tables**: `orders`, `order_items`

## Setup Instructions

### 1. Install Dependencies
```bash
# BigQuery dependency already added to requirements.txt
pip install -r requirements.txt
```

### 2. Django Configuration
Add to `settings.py`:
```python
INSTALLED_APPS = [
    # ... existing apps
    'etl',
]

# ETL Configuration
GS_ETL_BUCKET_NAME = env('GS_ETL_BUCKET_NAME', default='dobybot-etl-bucket')
```

Add to main `urls.py`:
```python
urlpatterns = [
    # ... existing patterns
    path('api/etl/', include('etl.urls')),
]
```

### 3. Database Migration
```bash
python manage.py makemigrations etl
python manage.py migrate
```

### 4. Google Cloud Setup
```bash
# Make setup script executable
chmod +x deployment/setup_etl_pipeline.sh

# Run setup (update PROJECT_ID and other variables first)
export PROJECT_ID="your-gcp-project-id"
export REGION="asia-southeast1"
export ETL_BUCKET_NAME="dobybot-etl-bucket"
export APP_URL="https://your-app-url.com"

./deployment/setup_etl_pipeline.sh
```

## Usage

### Manual Execution
```bash
# Export orders for company 1 (yesterday's data)
python manage.py export_orders_to_bigquery --company-id 1

# Export specific date range
python manage.py export_orders_to_bigquery \
    --company-id 1 \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --job-type full_export

# Dry run (see what would be exported)
python manage.py export_orders_to_bigquery --company-id 1 --dry-run
```

### API Usage
```bash
# Trigger ETL export
curl -X POST https://your-app-url.com/api/etl/export/ \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "job_type": "full_export",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  }'

# Check job status
curl https://your-app-url.com/api/etl/jobs/{job_id}/ \
  -H "Authorization: Bearer YOUR_TOKEN"

# List recent jobs
curl https://your-app-url.com/api/etl/jobs/ \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## BigQuery Schema

### Orders Table
```sql
CREATE TABLE `project.dobybot_company_1.orders` (
  order_id INT64 NOT NULL,
  uuid STRING NOT NULL,
  company_id INT64 NOT NULL,
  order_number STRING NOT NULL,
  order_saleschannel STRING,
  order_customer STRING,
  order_customerphone STRING,
  order_trackingno STRING,
  order_warehousecode STRING,
  order_shippingchannel STRING,
  order_total_quantity INT64,
  order_total_price NUMERIC,
  order_oms STRING,
  order_marketplace STRING,
  order_marketplaceshop STRING,
  order_date DATE NOT NULL,
  order_json JSON,
  packing_json JSON,
  receipt_url STRING,
  short_receipt_url STRING,
  ready_to_ship BOOL,
  ready_to_ship_timestamp TIMESTAMP,
  create_date TIMESTAMP NOT NULL,
  update_date TIMESTAMP,
  etl_processed_at TIMESTAMP NOT NULL
);
```

### Order Items Table
```sql
CREATE TABLE `project.dobybot_company_1.order_items` (
  order_item_id STRING NOT NULL,
  order_id INT64 NOT NULL,
  order_uuid STRING NOT NULL,
  company_id INT64 NOT NULL,
  order_number STRING NOT NULL,
  product_id INT64,
  sku STRING,
  name STRING,
  quantity INT64,
  unit_text STRING,
  price_per_unit NUMERIC,
  discount STRING,
  discount_amount NUMERIC,
  total_price NUMERIC,
  product_type INT64,
  serial_no_list JSON,
  sku_type STRING,
  order_date DATE NOT NULL,
  etl_processed_at TIMESTAMP NOT NULL
);
```

## Monitoring and Troubleshooting

### Check ETL Job Status
```python
from etl.models import ETLJob

# Recent jobs
recent_jobs = ETLJob.objects.filter(company_id=1).order_by('-created_at')[:10]
for job in recent_jobs:
    print(f"{job.uuid}: {job.status} - {job.total_orders} orders")
```

### Cloud Function Logs
```bash
gcloud functions logs read bigquery-loader --limit 50
```

### BigQuery Queries
```sql
-- Check data freshness
SELECT 
  DATE(etl_processed_at) as etl_date,
  COUNT(*) as orders_count
FROM `project.dobybot_company_1.orders`
GROUP BY etl_date
ORDER BY etl_date DESC
LIMIT 7;

-- Order items summary
SELECT 
  order_date,
  COUNT(DISTINCT order_id) as orders,
  COUNT(*) as items,
  SUM(total_price) as total_value
FROM `project.dobybot_company_1.order_items`
WHERE order_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
GROUP BY order_date
ORDER BY order_date DESC;
```

## Configuration

### Environment Variables
```bash
# Required
GS_ETL_BUCKET_NAME=dobybot-etl-bucket
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Optional
BIGQUERY_LOCATION=US
ETL_DEFAULT_JOB_TYPE=full_export
```

### Cloud Scheduler Configuration
- **Schedule**: `0 * * * *` (every hour)
- **Timezone**: `Asia/Bangkok`
- **Retry**: 3 attempts with exponential backoff

## Security Considerations

1. **Authentication**: API endpoints require user authentication
2. **Authorization**: Users can only access their company's data
3. **Service Account**: Cloud Function uses service account with minimal permissions
4. **Data Encryption**: All data encrypted in transit and at rest
5. **Access Logs**: All API calls and BigQuery access logged

## Performance Optimization

1. **Batch Processing**: Process data in chunks to avoid memory issues
2. **Parallel Loading**: BigQuery loads can run in parallel
3. **Data Partitioning**: Tables partitioned by `order_date`
4. **Compression**: CSV files compressed before upload
5. **Cleanup**: Old CSV files automatically deleted after 30 days
