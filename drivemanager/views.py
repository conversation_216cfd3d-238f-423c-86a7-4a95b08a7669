from datetime import timedelta, date
from typing import List
from cloudtasks.tasks import create_google_drive_daily_delete_video_folder_task


from companies.models import Company
from django.utils import timezone
from django.db.models import Sum
from core.serializers.serializers import end_of, start_of
from logger.models import VideoR<PERSON>ordLog
from rest_framework import serializers
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAdminUser
from rest_framework.views import APIView

from rest_framework.response import Response
from services.google.drive.drive import GoogleDriveService
from services.line_noti.line_noti import line_notify
from wallets.models import GoogleDriveTransaction, GoogleDriveTransactionType
from core.storages import default_secure_storage
from django.core.files.storage import Storage

# Create your views here.


class DailyDeleteOldVideoTaskSchedulerAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    def post(self, request):
        count = 0
        for company in Company.objects.all():
            if company.get_setting("GOOGLE_DRIVE_AUTO_REMOVE_OLD_FILE"):
                create_google_drive_daily_delete_video_folder_task(company.id)
                count += 1
        return Response(count)


class GoogleDriveVideoFolderDeleteAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    class Validator(serializers.Serializer):
        company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
        date = serializers.DateField(allow_null=True, required=False)

    def post(self, request):
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        company: Company = validator.validated_data["company"]
        target_date: date = validator.validated_data.get("date")

        result = self.delete_old_google_drive_folder(company, target_date)
        return Response(result)

    @classmethod
    def delete_old_google_drive_folder(cls, company: Company, target_date: date):
        drive_id = company.get_setting("GOOGLE_DRIVE_SHARE_DRIVE_ID")
        if not drive_id:
            raise Exception(
                "Missing required company setting: GOOGLE_DRIVE_SHARE_DRIVE_ID"
            )

        if not target_date:
            target_date = cls.get_last_retain_date(company)

        # Get google drive folder with `target_date`
        folder_ids = list(
            VideoRecordLog.objects.filter(
                company=company,
                upload_date__gte=start_of(target_date),
                upload_date__lte=end_of(target_date),
                drive_file_deleted=False,
            )
            .values_list("drive_folder_id", flat=True)
            .distinct()
        )

        # No folders found, just return
        if not folder_ids:
            return {
                "folder_ids": folder_ids,
                "deleted_size": 0,
                "deleted_count": 0,
            }

        # Delete the folders from google drive
        drive_service = GoogleDriveService()
        drive_service.bulk_delete(folder_ids)

        # Reduce google drive quota usage
        result = cls.reclaim_space(company, folder_ids, target_date)

        return {"folder_ids": folder_ids, **result}

    @classmethod
    def get_last_retain_date(cls, company):
        retain_duration = company.get_setting(
            "GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION"
        )
        if retain_duration == 0:
            raise Exception(
                "Missing required company setting: GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION"
            )

        return timezone.localdate() - timedelta(days=retain_duration)

    @classmethod
    def reclaim_space(cls, company: Company, folder_ids: List[str], target_date: date):
        """
        Reclaim Wallet.google_drive_usage for deleted files

        Parameters
        ----------
        company : Company
            Target company
        folder_ids : List[str]
            List of google drive folder id

        Returns
        -------
        deleted_size : int
            Total space reclaimed in bytes
        """
        v_logs = VideoRecordLog.objects.filter(
            company=company,
            drive_folder_id__in=folder_ids,
            drive_file_deleted=False,
        )

        v_log_ids = list(v_logs.values_list("id", flat=True))

        VideoRecordLog.objects.filter(id__in=v_log_ids).update(
            drive_file_deleted=True,
            drive_file_deleted_timestamp=timezone.now(),
        )
        deleted_size = (
            VideoRecordLog.objects.filter(id__in=v_log_ids)
            .aggregate(Sum("file_size"))
            .get("file_size__sum")
            or 0
        )
        deleted_count = len(v_log_ids)

        if deleted_size:
            deleted_size_mb = deleted_size / 1e6
            GoogleDriveTransaction.add_transaction(
                wallet=company.wallet,
                type=GoogleDriveTransactionType.DAILY_AUTO_REMOVE,
                value=(-deleted_size_mb),
                create_by=None,
                description=f"GOOGLE DRIVE - Auto delete folder {target_date}",
            )

        return {"deleted_size": deleted_size, "deleted_count": deleted_count}


class PlayableVideoDeleteAPI(APIView):
    permission_classes = [IsAdminUser]
    authentication_classes = [TokenAuthentication]

    def post(self, request):
        storage: Storage = default_secure_storage
        folders, files = storage.listdir("playable-videos")
        # print(dir(default_secure_storage))
        now = timezone.now()

        count = 0
        for file in files:
            created_at = storage.get_created_time("playable-videos/" + file)
            if created_at < now - timezone.timedelta(days=1):
                storage.delete("playable-videos/" + file)
                count += 1

        line_notify("DELETE_PLAYABLE_VIDEOS", f"Deleted {count} files")

        return Response(f"Deleted {count} files")
