from dynamic_rest.routers import DynamicRouter
from django.urls import path
from . import views

# /api/drivemanager/

router = DynamicRouter()
# router.register("", views.DynamicViewSet)

urlpatterns = [
    path('delete-video-folder/scheduler/', views.DailyDeleteOldVideoTaskSchedulerAPI.as_view()),
    path('delete-video-folder/handler/', views.GoogleDriveVideoFolderDeleteAPI.as_view()),
    path('delete-playable-videos/handler/', views.PlayableVideoDeleteAPI.as_view()),
] + router.urls
