# from datetime import date, datetime
# from decimal import Decimal
# from django.utils import timezone
# from unittest.mock import patch
# from django.test import TestCase
# from django.test.utils import tag
# from companies.models.models import Company
# from core.tests.testutils import AssertMatchMixin, TestClient
# from core.tests import setup
# from drivemanager.views import GoogleDriveVideoFolderDeleteAPI
# from logger.models import VideoRecordLog
# from services.google.drive.drive import GoogleDriveService
# from wallets.models import GoogleDriveTransaction


# # python manage.py test --tag=

# def mocked_localdate():
#     return date(2022, 1, 1)


# def mocked_now():
#     return timezone.make_aware(datetime(2022, 1, 1, 13, 0, 0))


# class MockedGoogleDriveService:
#     def __init__(self) -> None:
#         pass

#     def list(self, *args, **kwargs):
#         return {
#             'files': [
#                 {'id': 'drive_folder_1', 'name': '2022-05-21', 'parents': ['155-4sPQ_UkxPPNe1yMQX6_6dQwEd517l'], 'createdTime': '2022-05-21T01:50:03.804Z'}
#             ]
#         }

#     def bulk_delete(self, *args, **kwargs):
#         return {
#             'success': [],
#             'fail': []
#         }


# class DeleteOldVideoTaskHandlerAPITestCase(TestCase, AssertMatchMixin):
#     def setUp(self) -> None:
#         self.company: Company
#         self.user, self.company, self.wallet = setup.init().values()
#         self.client = TestClient()
#         self.client.login()

#     def setup_video_record_logs(self):
#         setup.create_test_videorecordlog(
#             name='test1', drive_folder_id='drive_folder_1', file_size=1_001_000)
#         setup.create_test_videorecordlog(
#             name='test2', drive_folder_id='drive_folder_1', file_size=1_002_000)
#         setup.create_test_videorecordlog(
#             name='test3', drive_folder_id='drive_folder_1', file_size=1_003_000)
#         setup.create_test_videorecordlog(
#             name='test4', drive_folder_id='drive_folder_2', file_size=2_001_000)
#         setup.create_test_videorecordlog(
#             name='test5', drive_folder_id='drive_folder_2', file_size=2_002_000)

#     @tag('delete-old-video')
#     def test_can_run(self):
#         self.assertEqual(1, 1)

#     @tag('delete-old-video')
#     @patch('django.utils.timezone.localdate', side_effect=mocked_localdate)
#     def test_get_last_retain_date(self, *args):
#         self.company.set_setting('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', 30)
#         d = GoogleDriveVideoFolderDeleteAPI.get_last_retain_date(company=self.company)
#         self.assertEqual(d, date(2021, 12, 2))

#     @tag('delete-old-video')
#     @patch('django.utils.timezone.now', side_effect=mocked_now)
#     def test_reclaim_space(self, *args):
#         self.setup_video_record_logs()
#         reclaimed_space = GoogleDriveVideoFolderDeleteAPI.reclaim_space(
#             self.company,
#             folder_ids=['drive_folder_1']
#         )
#         self.assertEqual(reclaimed_space, 3_006_000)

#         for name in ['test1', 'test2', 'test3']:
#             lv = VideoRecordLog.objects.get(name=name)
#             self.assertEqual(lv.drive_file_deleted, True)
#             self.assertEqual(lv.drive_file_deleted_timestamp, mocked_now())

#         tx = GoogleDriveTransaction.objects.first()
#         self.assertAlmostEquals(tx.value, Decimal('-3.01'))
#         self.assertAlmostEqual(self.company.wallet.google_drive_usage, Decimal('-3.01'))

#     @tag('delete-old-video')
#     @patch('django.utils.timezone.now', side_effect=mocked_now)
#     def test_reclaim_space_2(self, *args):
#         self.setup_video_record_logs()
#         reclaimed_space = GoogleDriveVideoFolderDeleteAPI.reclaim_space(
#             self.company,
#             folder_ids=['drive_folder_1', 'drive_folder_2']
#         )
#         self.assertEqual(reclaimed_space, 7_009_000)

#         for name in ['test1', 'test2', 'test3', 'test4', 'test5']:
#             lv = VideoRecordLog.objects.get(name=name)
#             self.assertEqual(lv.drive_file_deleted, True)
#             self.assertEqual(lv.drive_file_deleted_timestamp, mocked_now())

#         tx = GoogleDriveTransaction.objects.first()
#         self.assertAlmostEquals(tx.value, Decimal('-7.01'))
#         self.assertAlmostEqual(self.company.wallet.google_drive_usage, Decimal('-7.01'))

#     @tag('delete-old-video')
#     @patch('django.utils.timezone.now', side_effect=mocked_now)
#     def test_reclaim_space_3(self, *args):
#         self.setup_video_record_logs()
#         reclaimed_space = GoogleDriveVideoFolderDeleteAPI.reclaim_space(
#             self.company,
#             folder_ids=['drive_folder_not_found']
#         )

#         for name in ['test1', 'test2', 'test3', 'test4', 'test5']:
#             lv = VideoRecordLog.objects.get(name=name)
#             self.assertEqual(lv.drive_file_deleted, False)
#             self.assertEqual(lv.drive_file_deleted_timestamp, None)

#         self.assertEqual(reclaimed_space, 0)

#     @tag('delete-old-video')
#     @patch('django.utils.timezone.now', side_effect=mocked_now)
#     @patch.object(GoogleDriveService, '__init__', MockedGoogleDriveService.__init__)
#     @patch.object(GoogleDriveService, 'list', MockedGoogleDriveService.list)
#     @patch.object(GoogleDriveService, 'bulk_delete', MockedGoogleDriveService.bulk_delete)
#     def test_delete_old_google_drive_folder(self, *args):
#         self.company.set_setting('GOOGLE_DRIVE_SHARE_DRIVE_ID', 'drive_1')
#         self.company.set_setting('GOOGLE_DRIVE_AUTO_REMOVE_RETAIN_DURATION', 30)
#         self.setup_video_record_logs()
#         result = GoogleDriveVideoFolderDeleteAPI.delete_old_google_drive_folder(
#             company=self.company,
#             target_date='2022-05-21'
#         )

#         self.assertEqual(result['folder_ids'], ['drive_folder_1'])
#         self.assertEqual(result['reclaimed_space'], 3_006_000)
